function getRoundedCurrentTime() {
    var now = new Date();
    var minutes = now.getMinutes();
    var roundedMinutes = minutes < 15 ? 15 : 0;
    var nextHour = roundedMinutes === 0 ? now.getHours() + 1 : now.getHours();
    return `${String(nextHour).padStart(2, '0')}:${String(roundedMinutes).padStart(2, '0')}`;
}

function getFutureDate() {
    var now = new Date();
    now.setMinutes(now.getMinutes() + 30); // Add 30 minutes to current time
    return now;
}

// Format date to Romanian format
function formatDateRo(date) {
    var day = date.getDate().toString().padStart(2, '0');
    var month = (date.getMonth() + 1).toString().padStart(2, '0');
    var year = date.getFullYear();
    return `${day}.${month}.${year}`;
}

$(document).ready(function () {
    // Initialize tooltips safely
    try {
        if (typeof bootstrap !== 'undefined') {
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl)
            });
        }
    } catch (e) {
        console.error('Error initializing tooltips:', e);
    }

    // Initialize select2 for regular dropdowns
    // Inițializare simplă pentru select2
    try {
        // Inițializare select2 pentru dropdown-uri
        $('.select2').select2({
            width: '100%',
            minimumResultsForSearch: 6
        });

        // Inițializare specifică pentru selecturile de durată, structură, superior și funcție
        $('#durata, #structura, #superior, #functia').select2({
            width: '100%',
            minimumResultsForSearch: -1, // Dezactivează câmpul de căutare
            dropdownCssClass: 'select2-dropdown-custom'
        });
    } catch (e) {
        console.error('Error initializing select2:', e);
    }

    // Set minimum time to 30 minutes from now
    var minTime = getFutureDate();
    var formattedMinTime = minTime.getHours() + ":" + (minTime.getMinutes() < 10 ? '0' : '') + minTime.getMinutes();

    // Custom time picker implementation
    try {
        // Get current time and add 30 minutes
        var now = new Date();
        now.setMinutes(now.getMinutes() + 60);

        // Format time as HH:MM (24-hour format)
        var hours = now.getHours().toString().padStart(2, '0');
        var mins = now.getMinutes().toString().padStart(2, '0');
        var defaultTime = hours + ':' + mins;

        // Set default time for both time inputs
        $('#oraLucrare').val(defaultTime);
        $('#oraInvoire').val(defaultTime);

        // Variables to store selected time
        var selectedHour = parseInt(hours, 10);
        var selectedMinute = parseInt(mins, 10);

        // Generate hours and minutes for the time picker
        function generateTimePickerOptions() {
            var hoursContainer = $('.time-picker-hours');
            var minutesContainer = $('.time-picker-minutes');

            // Clear existing content
            hoursContainer.empty();
            minutesContainer.empty();

            // Generate hours (0-23)
            for (var i = 0; i < 24; i++) {
                var hourElement = $('<div class="time-picker-hour">' + i.toString().padStart(2, '0') + '</div>');
                if (i === selectedHour) {
                    hourElement.addClass('selected');
                }
                hoursContainer.append(hourElement);
            }

            // Generate minutes (0-59, step 5)
            for (var i = 0; i < 60; i += 5) {
                var minuteElement = $('<div class="time-picker-minute">' + i.toString().padStart(2, '0') + '</div>');
                if (i === Math.floor(selectedMinute / 5) * 5) {
                    minuteElement.addClass('selected');
                }
                minutesContainer.append(minuteElement);
            }

            // Scroll to selected values
            setTimeout(function() {
                var selectedHourElement = hoursContainer.find('.selected');
                var selectedMinuteElement = minutesContainer.find('.selected');

                if (selectedHourElement.length) {
                    hoursContainer.scrollTop(selectedHourElement.position().top - hoursContainer.height() / 2 + selectedHourElement.height() / 2);
                }

                if (selectedMinuteElement.length) {
                    minutesContainer.scrollTop(selectedMinuteElement.position().top - minutesContainer.height() / 2 + selectedMinuteElement.height() / 2);
                }
            }, 100);
        }

        // Show time picker dropdown
        function showTimePicker(clickedElement) {
            generateTimePickerOptions();
            $('#timePickerDropdown').addClass('show');

            // Store which input triggered the time picker
            var $activeInput = $(clickedElement).is('input') ? $(clickedElement) : $(clickedElement).siblings('input');
            $('#timePickerDropdown').data('active-input', $activeInput.attr('id'));

            // Position the dropdown - with safety checks
            var inputPosition = $activeInput.offset();
            if (inputPosition) { // Check if offset exists
                var inputHeight = $activeInput.outerHeight() || 0;
                $('#timePickerDropdown').css({
                    'top': inputPosition.top + inputHeight + 5,
                    'left': inputPosition.left
                });
            } else {
                // Fallback positioning if offset can't be determined
                $('#timePickerDropdown').css({
                    'top': '50%',
                    'left': '50%',
                    'transform': 'translate(-50%, -50%)'
                });
            }

            // Add click outside handler
            $(document).on('click.timepicker', function(e) {
                if (!$(e.target).closest('#timePickerDropdown, #oraLucrare, #oraInvoire, .time-picker-toggle').length) {
                    hideTimePicker();
                }
            });
        }

        // Hide time picker dropdown
        function hideTimePicker() {
            $('#timePickerDropdown').removeClass('show');
            $(document).off('click.timepicker');
        }

        // Update input value with selected time
        function updateTimeValue() {
            var formattedTime = selectedHour.toString().padStart(2, '0') + ':' +
                selectedMinute.toString().padStart(2, '0');

            // Get the active input ID and update it
            var activeInputId = $('#timePickerDropdown').data('active-input') || 'oraLucrare';
            $('#' + activeInputId).val(formattedTime);
        }

        // Add click handlers for both time inputs
        $('.time-picker-toggle, #oraLucrare, #oraInvoire').on('click', function() {
            showTimePicker(this);
        });

        // Hour selection
        $(document).on('click', '.time-picker-hour', function() {
            $('.time-picker-hour').removeClass('selected');
            $(this).addClass('selected');
            selectedHour = parseInt($(this).text(), 10);
        });

        // Minute selection
        $(document).on('click', '.time-picker-minute', function() {
            $('.time-picker-minute').removeClass('selected');
            $(this).addClass('selected');
            selectedMinute = parseInt($(this).text(), 10);
        });

        // Apply button
        $(document).on('click', '.time-picker-apply', function() {
            updateTimeValue();
            hideTimePicker();
        });

        // Cancel button - reset to default values
        $(document).on('click', '.time-picker-cancel', function() {
            // Reset time to default (current time + 30 minutes)
            var now = new Date();
            now.setMinutes(now.getMinutes() + 30);
            var hours = now.getHours().toString().padStart(2, '0');
            var mins = now.getMinutes().toString().padStart(2, '0');
            var defaultTime = hours + ':' + mins;

            // Get the active input ID and update it
            var activeInputId = $('#timePickerDropdown').data('active-input') || 'oraLucrare';
            $('#' + activeInputId).val(defaultTime);

            // Reset duration to 30 minutes
            $('#durata').val('30_minute').trigger('change');

            // Hide the time picker
            hideTimePicker();
        });

    } catch (e) {
        console.error('Error initializing time picker:', e);
    }

    $(".hourPicker").on("change", function () {
        var selectedTime = $(this).val();
        $("#oraLucrare").val(selectedTime);
        // Add visual feedback
        $(this).addClass('is-valid');
    });

    // Set minimum date to today
    var today = new Date();
    var tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    // Set default date to tomorrow
    var defaultDate = formatDateRo(tomorrow);
    $('#dataLucrare').html(defaultDate);

    // Animation for datepicker
    $(document).on('show', '.datepicker', function() {
        // Add animation class
        setTimeout(function() {
            $('.datepicker').addClass('animate__animated animate__fadeInDown');
        }, 50);
    }).on('hide', '.datepicker', function() {
        $('.datepicker').removeClass('animate__animated animate__fadeInDown');
    });

    // Initialize the datepicker directly on the input
    try {
        $('#dataLucrare').datepicker({
            weekStart: 1,
            format: "dd.mm.yyyy",
            maxViewMode: 2,
            language: "ro",
            autoclose: true,
            todayHighlight: true,
            startDate: today,
            container: 'body',
            zIndexOffset: 9999,
            templates: {
                leftArrow: '<i class="cil-chevron-left"></i>',
                rightArrow: '<i class="cil-chevron-right"></i>'
            }
        }).on('changeDate', function(e) {
            $(this).addClass('is-valid');
        }).on('hide', function() {
            // When datepicker is closed by clicking outside or pressing Escape
            // Check if the date is the default date (tomorrow)
            var currentDate = $(this).val();
            var defaultDate = formatDateRo(tomorrow);

            if (currentDate === defaultDate) {
                // If date is reset to default, also reset duration to 30 minutes
                $('#durata').val('30_minute').trigger('change');
            }
        });

        // Set default date
        var defaultDate = formatDateRo(tomorrow);
        $('#dataLucrare').datepicker('update', defaultDate);
        $('#dataLucrare').val(defaultDate);
    } catch (e) {
        console.error('Error initializing datepicker:', e);
        // Fallback to regular input
        $('#dataLucrare').attr('type', 'date');
    }

    // Button click opens the datepicker
    $(document).off('click', '.calendarLucrare').on('click', '.calendarLucrare', function (e) {
        e.preventDefault();
        e.stopImmediatePropagation();
        try {
            $('#dataLucrare').datepicker('show');

            // Add a cancel button to the datepicker if it doesn't exist
            if ($('.datepicker-cancel-btn').length === 0) {
                setTimeout(function() {
                    var cancelBtn = $('<button type="button" class="btn btn-sm btn-secondary datepicker-cancel-btn">Anulează</button>');
                    $('.datepicker-days').append(cancelBtn);

                    // Add click handler for the cancel button
                    $(document).on('click', '.datepicker-cancel-btn', function() {
                        // Reset date to default (tomorrow)
                        var defaultDate = formatDateRo(tomorrow);
                        $('#dataLucrare').datepicker('update', defaultDate);
                        $('#dataLucrare').val(defaultDate);

                        // Reset duration to 30 minutes
                        $('#durata').val('30_minute').trigger('change');

                        // Hide the datepicker
                        $('#dataLucrare').datepicker('hide');
                    });
                }, 100);
            }
        } catch (e) {
            console.error('Error showing datepicker:', e);
            // Fallback - focus on the input
            $('#dataLucrare').focus();
        }
    });

    // Handle form submission with loading state
    $('.invoiri-form').on('submit', function(e) {
        e.preventDefault();

        // Validate form
        var numePrenume = $('#numePrenume').val();
        var functia = $('#functia').val();
        var structura = $('#structura').val();
        var durata = $('#durata').val();
        var dataInvoirii = $('#dataLucrare').text();
        var oraInceput = $('#oraLucrare').val();

        // Reset validation
        $('.is-invalid').removeClass('is-invalid');

        // Validate each field
        var isValid = true;

        if (!numePrenume) {
            $('#numePrenume').addClass('is-invalid');
            isValid = false;
        }

        if (!functia) {
            $('#functia').next('.select2-container').addClass('is-invalid');
            isValid = false;
        }

        if (!structura) {
            $('#structura').next('.select2-container').addClass('is-invalid');
            isValid = false;
        }

        if (!durata) {
            $('#durata').next('.select2-container').addClass('is-invalid');
            isValid = false;
        }

        if (!dataInvoirii) {
            $('#dataLucrare').addClass('is-invalid');
            isValid = false;
        }

        if (!oraInceput) {
            $('#oraLucrare').addClass('is-invalid');
            isValid = false;
        }

        if (!isValid) {
            Swal.fire({
                icon: 'error',
                title: 'Eroare',
                text: 'Toate câmpurile sunt obligatorii!',
                confirmButtonColor: '#3085d6'
            });
            return false;
        }

        // No validation for durata - all options are valid now

        // Show loading state
        var submitBtn = $(this).find('button[type="submit"]');
        var originalBtnText = submitBtn.html();
        submitBtn.html('<i class="cil-sync cil-spin me-2"></i>Se procesează...').prop('disabled', true);

        // Submit form via AJAX
        $.ajax({
            url: 'controller/invoiri.php',
            type: 'POST',
            data: {
                id_user: $('#user_id').val(),
                nume_prenume: numePrenume,
                functia: functia,
                id_structura: structura,
                durata: durata,
                data_invoirii: dataInvoirii,
                ora_inceput: oraInceput
            },
            dataType: 'json',
            success: function(response) {
                // Reset button state
                submitBtn.html(originalBtnText).prop('disabled', false);

                if (response.status === 'ok') {
                    Swal.fire({
                        icon: 'success',
                        title: 'Succes!',
                        text: response.message,
                        confirmButtonColor: '#3085d6',
                        confirmButtonText: '<i class="cil-check me-2"></i>OK'
                    }).then(function() {
                        // Show loading spinner
                        Swal.fire({
                            title: 'Se actualizează...',
                            html: 'Vă rugăm să așteptați',
                            allowOutsideClick: false,
                            didOpen: () => {
                                Swal.showLoading();
                            }
                        });
                        // Reload page
                        window.location.reload();
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Eroare',
                        text: response.message,
                        confirmButtonColor: '#3085d6'
                    });
                }
            },
            error: function() {
                // Reset button state
                submitBtn.html(originalBtnText).prop('disabled', false);

                Swal.fire({
                    icon: 'error',
                    title: 'Eroare',
                    text: 'A apărut o eroare la procesarea cererii. Vă rugăm să încercați din nou.',
                    confirmButtonColor: '#3085d6'
                });
            }
        });
    });

    // Add enhanced animations to the history table
    setTimeout(function() {
        // Define an array of animation types for variety
        var animations = [
            'animate__fadeInUp',
            'animate__fadeInRight',
            'animate__fadeInDown',
            'animate__fadeInLeft',
            'animate__zoomIn'
        ];

        $('.table tbody tr').each(function(index) {
            var $row = $(this);

            // Select animation type based on index (cycle through animations)
            var animationClass = animations[index % animations.length];

            // Add animation with improved staggered delay
            $row.addClass('animate__animated ' + animationClass)
                .css({
                    'animation-delay': (index * 0.08) + 's',
                    'animation-duration': '0.6s'
                });

            // Add a subtle highlight effect after the main animation
            setTimeout(function() {
                $row.addClass('animate__highlighted');

                // Remove highlight after a delay
                setTimeout(function() {
                    $row.removeClass('animate__highlighted');
                }, 1500);
            }, (index * 80) + 600);
        });
    }, 300);
});

// JavaScript to enhance the time input functionality
$(document).ready(function() {
    // Initialize Select2 with custom options
    $('.select2').select2({
        width: '100%',
        dropdownParent: $('body'),
        minimumResultsForSearch: 10
    });

    // Initialize Select2 without search functionality
    $('.select2-no-search').select2({
        width: '100%',
        dropdownParent: $('body'),
        minimumResultsForSearch: Infinity
    });

    // Initialize datepicker
    $('.datepicker').datepicker({
        format: 'dd.mm.yyyy',
        autoclose: true,
        todayHighlight: true,
        startDate: new Date(),
        language: 'ro'
    });

    // Handle time input validation
    $('.time-input').on('input', function() {
        let value = $(this).val();
        value = value.replace(/[^0-9]/g, '');

        const maxValue = $(this).hasClass('hour-input') ? 23 : 59;

        if (value === '') {
            $(this).val('');
        } else {
            const numValue = parseInt(value, 10);
            if (numValue > maxValue) {
                $(this).val(maxValue);
            } else {
                $(this).val(numValue);
            }
        }
    });

    // Auto move to minutes after hours input is complete
    $('.hour-input').on('input', function() {
        if ($(this).val().length >= 2) {
            $(this).closest('.time-input-container').find('.minute-input').focus();
        }
    });

    // Format hours and minutes with leading zeros when losing focus
    $('.time-input').on('blur', function() {
        if ($(this).val() !== '') {
            const value = parseInt($(this).val(), 10);
            $(this).val(value.toString().padStart(2, '0'));
        } else {
            $(this).val('00');
        }
    });

    // Submit handler for the form
    $('.invoiri-form').on('submit', function(e) {
        e.preventDefault();

        // Combine hour and minute inputs into a single time value
        const hour = $('.hour-input').val() || '00';
        const minute = $('.minute-input').val() || '00';
        const timeValue = `${hour}:${minute}`;

        // You can now use timeValue in your form submission
        // Rest of your form submit code...

        // Show success message
        Swal.fire({
            title: 'Succes!',
            text: 'Cererea de învoire a fost trimisă cu succes.',
            icon: 'success',
            confirmButtonText: 'OK',
            confirmButtonColor: '#3c4b64'
        });
    });
});