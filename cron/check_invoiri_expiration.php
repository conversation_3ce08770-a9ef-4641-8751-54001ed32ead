<?php
// Script to check for expired leave requests and update their status
require_once '../inc/cfg_functions.php';
global $pdo;

// Get all pending leave requests that have been waiting for more than 30 minutes
$stmt = $pdo->prepare("SELECT ia.*, i.id_user, i.data_invoirii, i.ora_inceput, i.durata_ore, i.functia, i.id_structura, i.status as invoiri_status,
                      u.nume, u.prenume, u.email
                      FROM mj.invoiri_aprobari ia
                      JOIN mj.invoiri i ON ia.id_invoiri = i.id_invoiri
                      JOIN users u ON i.id_user = u.uid
                      WHERE ia.status = 'in_asteptare' 
                      AND TIMESTAMPDIFF(MINUTE, ia.data_notificare, NOW()) > 30");
$stmt->execute();
$expired_approvals = $stmt->fetchAll(PDO::FETCH_ASSOC);

foreach ($expired_approvals as $approval) {
    // Update approval status to expired
    $stmt = $pdo->prepare("UPDATE mj.invoiri_aprobari SET status = 'expirata', data_raspuns = NOW() WHERE id_aprobare = :id_aprobare");
    $stmt->execute(['id_aprobare' => $approval['id_aprobare']]);
    
    // Check if all approvals for this leave request are expired or rejected
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM mj.invoiri_aprobari WHERE id_invoiri = :id_invoiri AND status IN ('in_asteptare', 'aprobata')");
    $stmt->execute(['id_invoiri' => $approval['id_invoiri']]);
    $remaining_approvals = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($remaining_approvals == 0) {
        // Update invoiri status to expired
        $stmt = $pdo->prepare("UPDATE mj.invoiri SET status = 'expirata', data_actualizare = NOW() WHERE id_invoiri = :id_invoiri");
        $stmt->execute(['id_invoiri' => $approval['id_invoiri']]);
        
        // Send notification to employee
        $data_invoirii = date('d.m.Y', strtotime($approval['data_invoirii']));
        $ora_inceput = date('H:i', strtotime($approval['ora_inceput']));
        $ora_sfarsit = date('H:i', strtotime("+{$approval['durata_ore']} hours", strtotime($approval['ora_inceput'])));
        
        $subject = "Actualizare solicitare învoire - Expirată";
        
        $body = "
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; }
                .container { width: 600px; margin: 0 auto; }
                .header { background-color: #f5f5f5; padding: 20px; text-align: center; }
                .content { padding: 20px; }
                .footer { background-color: #f5f5f5; padding: 10px; text-align: center; font-size: 12px; }
                .details { margin: 20px 0; }
                table { width: 100%; border-collapse: collapse; }
                table, th, td { border: 1px solid #ddd; }
                th, td { padding: 10px; text-align: left; }
                th { background-color: #f2f2f2; }
                .expired { color: orange; font-weight: bold; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h2>Actualizare solicitare învoire</h2>
                </div>
                <div class='content'>
                    <p>Stimată/Stimate {$approval['nume']} {$approval['prenume']},</p>
                    <p>Solicitarea dumneavoastră de învoire a <span class='expired'>EXPIRAT</span> deoarece nu a fost aprobată în intervalul de 30 de minute.</p>
                    
                    <div class='details'>
                        <table>
                            <tr>
                                <th>Data:</th>
                                <td>{$data_invoirii}</td>
                            </tr>
                            <tr>
                                <th>Interval orar:</th>
                                <td>{$ora_inceput} - {$ora_sfarsit}</td>
                            </tr>
                            <tr>
                                <th>Durata:</th>
                                <td>{$approval['durata_ore']} ore</td>
                            </tr>
                            <tr>
                                <th>Status:</th>
                                <td><span class='expired'>Expirată</span></td>
                            </tr>
                        </table>
                    </div>
                </div>
                <div class='footer'>
                    <p>Acest email a fost generat automat. Vă rugăm să nu răspundeți la acest email.</p>
                </div>
            </div>
        </body>
        </html>
        ";
        
        // Send email to employee
        $stmt = $pdo->prepare("INSERT INTO scan.email_queue (to_email, subject, body) VALUES (:to_email, :subject, :body)");
        $stmt->execute([
            ':to_email' => $approval['email'],
            ':subject' => $subject,
            ':body' => $body
        ]);
    }
}

echo "Expired leave requests check completed at " . date('Y-m-d H:i:s') . "\n";
