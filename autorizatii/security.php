<?php
// security.php
//session_start();
require 'db.php';			
function getAuthenticatedUser($pdo, $user) {
    if (!$user) {
        die("Error 4: Acces neautorizat. Utilizatorul $user nu poate fi identificat.");
    }

    $stmt = $pdo->prepare("
        SELECT Id, StructureId, IsAdmin, IsWorker, IsBoss, Account
        FROM sic.users 
        WHERE Account = :account AND IsDeleted = 0
    ");
    $stmt->execute(['account' => $user]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$result) {
        die(" Error 5: Acces neautorizat. Utilizatorul $user nu este înregistrat la nivelul aplicatiei sau contul este șters.");
    }

    $_SESSION['user_id'] = $result['Id'] ?? null;
    $_SESSION['user_structure'] = $result['StructureId'] ?? null;
    $_SESSION['IsAdmin'] = $result['IsAdmin'] ?? 0;
    $_SESSION['IsWorker'] = $result['IsWorker'] ?? 0;
    $_SESSION['IsBoss'] = $result['IsBoss'] ?? 0; 
    return $result;
}
?>
