 <?php
	require_once 'security.php';
require '../inc/cfg_functions.php';
    // Variabilă pentru mesajul de autentificare
// Initializare Aura.Session (cred)
$cale_sesiune = '../assets/vendors/session/autoload.php';
require_once $cale_sesiune;

use Aura\Session\SessionFactory;

$session_factory = new SessionFactory();
$session = $session_factory->newInstance($_COOKIE); // Aura.Session se ocupă singur de $_SESSION

// Creăm un segment pentru datele utilizatorului
$sesiune = $session->getSegment('Vendor\Package\ClassName');

// Verificare sesiune 
$session_uid = $sesiune->get("SICloggedIn", 0);

// Variabile inițiale
$loginMessage = "";
$login_post = 0;
$_SESSION['SICloggedIn'] = 0;
$username = $password = null;

    if (isset($session_uid) && $session_uid > 0) {
        echo "<a href='mj.php'>Click pt pagina urmatoare</a>";
        exit;
    } else if ($_SERVER["REQUEST_METHOD"] == "POST") {
//session_start(); ///aici am dubii 
				$username = htmlspecialchars($_POST['uname'] ?? '', ENT_QUOTES, 'UTF-8');
				$password = $_POST['psw'] ?? '';

				if (loginLDAPS($username, $password)) {
					echo "<script>document.getElementById('id01').style.display = 'none';</script>";

					session_regenerate_id(true);
					$_SESSION['SICloggedIn'] = 1;
					$_SESSION['ad_account'] = $username;
					getAuthenticatedUser($pdo, $username);
					header("Location: home.php");
					//echo "a intrat aici";
					$loginMessage = "<script>alert('succes!'); document.getElementById('id01').style.display = 'none';</script>";
				} else {
					$_SESSION['SICloggedIn'] = 0;
					/* header("Location: login.php"); */
					$loginMessage = "<script>alert('eroare aici!'); document.getElementById('id01').style.display = 'none';</script>";
					//echo "a intrat pe negativ";
				}
    }

    ?>



<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LOGIN Page</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<style>
    body {font-family: Arial, Helvetica, sans-serif;}

    /* Full-width input fields */
    input[type=text], input[type=password] {
        width: 100%;
        padding: 12px 20px;
        margin: 8px 0;
        display: inline-block;
        border: 1px solid #ccc;
        box-sizing: border-box;
    }

    /* Set a style for all buttons */
    button {
        background-color: #04AA6D;
        color: white;
        padding: 14px 20px;
        margin: 8px 0;
        border: none;
        cursor: pointer;
        width: 100%;
    }

    button:hover {
        opacity: 0.8;
    }

    /* Extra styles for the cancel button */
    .cancelbtn {
        width: auto;
        padding: 10px 18px;
        background-color: #f44336;
    }

    /* Center the image and position the close button */
    .imgcontainer {
        text-align: center;
        margin: 24px 0 12px 0;
        position: relative;
    }

    img.avatar {
        width: 40%;
        border-radius: 50%;
    }

    .container {
        padding: 16px;
    }

    span.psw {
        float: right;
        padding-top: 16px;
    }

    /* The Modal (background) */
    .modal {
        display: none; /* Hidden by default */
        position: fixed; /* Stay in place */
        z-index: 1; /* Sit on top */
        left: 0;
        top: 0;
        width: 100%; /* Full width */
        height: 100%; /* Full height */
        overflow: auto; /* Enable scroll if needed */
        background-color: rgb(0,0,0); /* Fallback color */
        background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
        padding-top: 60px;
    }

    /* Modal Content/Box */
    .modal-content {
        background-color: #fefefe;
        margin: 5% auto 15% auto; /* 5% from the top, 15% from the bottom and centered */
        border: 1px solid #888;
        width: 80%; /* Could be more or less, depending on screen size */
    }

    /* The Close Button (x) */
    .close {
        position: absolute;
        right: 25px;
        top: 0;
        color: #000;
        font-size: 35px;
        font-weight: bold;
    }

    .close:hover,
    .close:focus {
        color: red;
        cursor: pointer;
    }

    /* Add Zoom Animation */
    .animate {
        -webkit-animation: animatezoom 0.6s;
        animation: animatezoom 0.6s
    }

    @-webkit-keyframes animatezoom {
        from {-webkit-transform: scale(0)}
        to {-webkit-transform: scale(1)}
    }

    @keyframes animatezoom {
        from {transform: scale(0)}
        to {transform: scale(1)}
    }

    /* Change styles for span and cancel button on extra small screens */
    @media screen and (max-width: 300px) {
        span.psw {
            display: block;
            float: none;
        }
        .cancelbtn {
            width: 100%;
        }
    }
</style>
<body>
	<?php if ($_SESSION['SICloggedIn']=1){
	include 'navbar.php';}  ?>
	
<meta name="viewport" content="width=device-width, initial-scale=1">

<!--     cod preluat de pe https://www.w3schools.com/-->
   
    <html lang="ro">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
    </head>
    <body>
	<div class="container mt-5">
    <h1>Why did the detective always sign his reports with "<b>S. I. C. </b> "?
    </h1>
    <h3>&nbsp; </h3>
    <h1>
    Because it stood for Solving Impossible Cases!
    </h1>
    <table style="float:right">
        <tr>
            <th><button style="width:10px;height: 10px;background-color:#e8d3f0; "></button></th>
            <th><button style="width:20px;height: 20px;background-color:#938499; "></button></th>
            <th><button style="width:30px;height: 30px;background-color:#e8d3f0; "></button></th>
            <th><button onclick="document.getElementById('id01').style.display='block'" style="width:150px;height: 100px;background-color:#61b88b;" >Login</button></th>
            <th><button style="width:30px;height: 30px;background-color:#938499; "></button></th>
            <th><button style="width:20px;height: 20px;background-color:#e8d3f0; "></button></th>
            <th><button style="width:10px;height: 10px;background-color:#938499; "></button></th>
            <th><img src="detective.jpg" alt="SIC" width="400" height="500" style="float:right"></th>

        </tr>
    </table>
	</div>
	
    <div id="id01" class="modal">
        <form class="modal-content animate" method="post">
            <div class="imgcontainer">
                <span onclick="document.getElementById('id01').style.display='none'" class="close" title="Close Modal">&times;</span>
                <img src="Avatar.jpg" alt="Avatar" width="200" height="200">
            </div>

            <div class="container">
                <div class="form-group">
                    <label for="uname"><b>Cont de utilizator</b></label>
                    <input type="text" id="uname" name="uname" placeholder="..." required>
                </div>
                <div class="form-group">
                    <label for="psw"><b>Parola</b></label>
                    <input type="password" id="psw" name="psw" placeholder="..." required>
                </div>
                <div class="form-group">
                    <input type="checkbox" id="remember" name="remember" checked>
                    <label for="remember"> Remember me</label>
                </div>
                <div class="form-group">
                    <button type="submit" style="width:30%">Login</button>
                    <button type="button" onclick="document.getElementById('id01').style.display='none'" class="cancelbtn" style="width:30%">Cancel</button>
                </div>
            </div>
        </form>
    </div>

    <script>
        // Închide modalul când se face clic în afara lui
        window.onclick = function(event) {
            var modal = document.getElementById('id01');
            if (event.target === modal) {
                modal.style.display = "none";
            }
        }
    </script>

</body>
</html>

