<?php
require 'security.php';
require_once '../inc/cfg_functions.php';
//require 'functions.php';

//sendEmail();

// Obține utilizatorul autentificat
//$user = getAuthenticatedUser($GLOBALS['pdo']);

// Verifică permisiunile pentru acces
$isBoss = $_SESSION['IsBoss'] ?? false;
$isAdmin = $_SESSION['IsAdmin'] ?? false;
$isWorker = $_SESSION['IsWorker'] ?? false;
$userId = $_SESSION['user_id'] ?? 0;

// Logică de permisiuni
if (!$isAdmin && !$isWorker && (!$isBoss || (!$isAdmin && !$isWorker))) {
    header("Location: home.php");
    exit();
}

// Obține parametrii de căutare
$search = $_GET['search'] ?? '';

// Fetch data for display
$query = "
    SELECT 
        a.Id,
        CONCAT(p.Name, ' ', p.Firstname) AS NumePrenume,
        d.ShortName AS Departament,
        t.ShortName AS Tip,
        l.ShortName AS Nivel,
        l.Id AS NivelId,
        s.Name AS Serie,
        a.Number AS Numar,
        a.ReleaseDate AS DataEliberarii,
        a.ExpirationDate AS DataExpirarii,
		a.Obs as Obs,
		a.NotSendMail as NotSendMail
    FROM sic.auth a
    JOIN sic.person p ON a.IdPerson = p.Id
    JOIN sic.departments d ON p.Department = d.Id
    JOIN sic.types t ON a.IdType = t.Id
    JOIN sic.levels l ON a.IdLevel = l.Id
    JOIN sic.series s ON a.IdSerie = s.Id
    WHERE a.IsDeleted = 0 
      AND a.IsRevoked = 0 
      AND p.IsDeleted = 0 
      AND d.IsMJ = 0  -- Asigură-te că departamentul este din MJ
      AND l.IsNational = 0  -- Nivelul este național
      AND l.IsUE = 0        -- Nu este din UE
      AND l.IsNATO = 1      -- Nu este din NATO

";

if (!empty($search)) {
    $query .= " AND (p.Name LIKE :search OR p.Firstname LIKE :search OR d.ShortName LIKE :search OR t.ShortName LIKE :search OR s.Name LIKE :search OR a.Number LIKE :search)";
}

$query .= " ORDER BY a.ExpirationDate ASC";

$stmt = $GLOBALS['pdo']->prepare($query);
if (!empty($search)) {
    $stmt->bindValue(':search', '%' . $search . '%', PDO::PARAM_STR);
}
$stmt->execute();
$requests = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $Id = $_POST['Id'];

    if ($_POST['action'] === 'update') {
        $Number = $_POST['Number'];
        $ReleaseDate = $_POST['ReleaseDate'];
        $ExpirationDate = $_POST['ExpirationDate'];
		$Obs = $_POST['Obs'];
		$NotSendMail = $_POST['NotSendMail'];
		
        $stmt = $GLOBALS['pdo']->prepare("UPDATE sic.auth SET Number = ?, ReleaseDate = ?, ExpirationDate = ?, Obs = ?, NotSendMail = ?, LastModifiedBy = ?, LastModifiedDate= NOW() WHERE Id = ?");
        $stmt->execute([$Number, $ReleaseDate, $ExpirationDate, $Obs, $NotSendMail, $userId, $Id]);
        echo "success";
        exit;
    }

    if ($_POST['action'] === 'revoke') {
        $stmt = $GLOBALS['pdo']->prepare("UPDATE sic.auth SET IsRevoked = 1, IsRevokedBy = ?, IsRevokedDate = NOW() WHERE Id = ?");
        $stmt->execute([$userId,$Id]);
        echo "success";
        exit;
    }

    if ($_POST['action'] === 'delete') {
        if (!$isAdmin) {
            echo "unauthorized";
            exit;
        }
        $stmt = $GLOBALS['pdo']->prepare("UPDATE sic.auth SET IsDeleted = 1, IsDeletedBy = ?, IsDeletedDate = NOW() WHERE Id = ?");
        $stmt->execute([$userId,$Id]);
        echo "success";
        exit;
    }
}

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NATO</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
<?php include 'navbar.php'; ?>
<div class="container mt-5">
    <h3>Listă certificate NATO</h3>

    <!-- Căutare -->
    <div class="mb-3">
        <input type="text" id="search" class="form-control" placeholder="Caută un certificat...">
    </div>

    <table style="font-size:80%"  class="table table-bordered table-striped">
        <thead>
        <tr>
            <th>Nume Prenume</th>
            <th>Departament</th>
            <th>Tip</th>
            <th>Nivel</th>
            <th>Serie</th>
            <th>Număr</th>
            <th>Data Eliberării</th>
            <th>Data Expirării</th>
			<th>Observații</th>
			<th>NU trimite email!</th>
            <th>Acțiuni</th>
        </tr>
        </thead>
        <tbody id="table-body">
        <?php if (!empty($requests)): ?>
            <?php foreach ($requests as $request): ?>
                <?php
                // Calculăm diferența în zile între data curentă și data expirării
                $expirationDate = new DateTime($request['DataExpirarii']);
                $currentDate = new DateTime();
                $interval = $currentDate->diff($expirationDate);
                $daysLeft = (int)$interval->format('%r%a');

                // Determinăm clasa CSS pentru evidențiere doar pentru NivelId = 4
                $rowClass = '';
                if ($request['NivelId'] == 4) {
                    if ($daysLeft <= 30 && $daysLeft > 15) {
                        $rowClass = 'table-warning'; // Galben
                    } elseif ($daysLeft <= 15 && $daysLeft >= 0) {
                        $rowClass = 'table-danger'; // Roșu
                    }
                }
                elseif ($request['NivelId'] == 3) {
                    if ($daysLeft <= 60 && $daysLeft > 30) {
                        $rowClass = 'table-warning'; // Galben
                    } elseif ($daysLeft <= 30 && $daysLeft >= 0) {
                        $rowClass = 'table-danger'; // Roșu
                    }
                }
                elseif ($request['NivelId'] == 2) {
                    if ($daysLeft <= 90 && $daysLeft > 60) {
                        $rowClass = 'table-warning'; // Galben
                    } elseif ($daysLeft <= 60 && $daysLeft >= 0) {
                        $rowClass = 'table-danger'; // Roșu
                    }
                }
                elseif ($request['NivelId'] == 1) {
                    if ($daysLeft <= 120 && $daysLeft > 90) {
                        $rowClass = 'table-warning'; // Galben
                    } elseif ($daysLeft <= 90 && $daysLeft >= 0) {
                        $rowClass = 'table-danger'; // Roșu
                    }
                }
                ?>
                                <tr class="<?= $rowClass ?>" data-id="<?= $request['Id'] ?>">
                    <td><?= htmlspecialchars($request['NumePrenume']) ?></td>
                    <td><?= htmlspecialchars($request['Departament']) ?></td>
                    <td><?= htmlspecialchars($request['Tip']) ?></td>
                    <td><?= htmlspecialchars($request['Nivel']) ?></td>
                    <td><?= htmlspecialchars($request['Serie']) ?></td>
                    <td><input type="number" class="form-control" style="font-size:100%"  value="<?= htmlspecialchars($request['Numar']) ?>" data-field="Number"></td>
                    <td>
                        <input type="date" class="form-control" style="font-size:90%" 
                               value="<?= !empty($request['DataEliberarii']) ? substr($request['DataEliberarii'], 0, 10) : '' ?>"
                               data-field="ReleaseDate">
                    </td>
                    <td>
                        <input type="date" class="form-control"  style="font-size:90%" 
                               value="<?= !empty($request['DataExpirarii']) ? substr($request['DataExpirarii'], 0, 10) : '' ?>"
                               data-field="ExpirationDate">
                    </td>
					
					
					<td><input type="text" class="form-control" style="font-size:100%" value="<?= htmlspecialchars($request['Obs'] ?? '') ?>" data-field="Obs"></td>
					<td> <input type="checkbox" class="mail-checkbox" data-field="NotSendMail" <?= $request['NotSendMail'] ? 'checked' : '' ?>></td>

                    <td>
                        <button style="font-size:80%" class="btn btn-primary btn-sm save-btn">Salvează</button>
                        <button style="font-size:80%" class="btn btn-warning btn-sm revoke-btn">Revocă</button>
                        <?php if ($isAdmin): ?>
                            <button style="font-size:80%" class="btn btn-danger btn-sm delete-btn">Șterge</button>
                        <?php endif; ?>
                    </td>
                </tr>
            <?php endforeach; ?>
        <?php else: ?>
            <tr>
                <td colspan="9" class="text-center">Nu s-au găsit rezultate.</td>
            </tr>
        <?php endif; ?>
        </tbody>
    </table>

    <!-- Aici scriu termenele de atenționare -->
    <span>Autorizațiile vor expira în termen de:</span>
    <div class="mt-4">
        <div class="d-flex justify-content-start align-items-center">
            <div class="me-3">
                <div class="p-2 bg-danger text-white">&nbsp;</div>
            </div>
            <span>Secret de servciu - maxim 15 zile   /</span>
            <span>Secret -  maxim 30 zile   /</span>
            <span>Strict Secret - maxim 60 zile   /</span>
            <span>Strict Secret de Importanță Deosebită - maxim 90 zile.</span>
        </div>
        <div class="d-flex justify-content-start align-items-center mt-2">
            <div class="me-3">
                <div class="p-2 bg-warning">&nbsp;</div>
            </div>
            <span>Secret de servciu - maxim 30 zile   /</span>
            <span>Secret - maxim 60 zile   /</span>
            <span>Strict Secret - maxim 90 zile   /</span>
            <span>Strict Secret de Importanță Deosebită - maxim 120 zile.</span>
        </div>
    </div>
    <span><b>Procedura de verificare</b> dureazã 30( S), 60( SS), 90( SSID) de zile lucrătoare.<span>
</div>

<script>
    $(document).ready(function () {
        // Căutare în timp real
        $('#search').on('input', function () {
            const search = $(this).val();
            $.get('', { search: search }, function (data) {
                const tableBody = $(data).find('#table-body').html();
                $('#table-body').html(tableBody);
            });
        });

        // Salvează modificările
        $(document).on('click', '.save-btn', function () {
            const row = $(this).closest('tr');
            const Id = row.data('id');
            const Number = row.find('[data-field="Number"]').val();
            const ReleaseDate = row.find('[data-field="ReleaseDate"]').val();
            const ExpirationDate = row.find('[data-field="ExpirationDate"]').val();
			const Obs = row.find('[data-field="Obs"]').val();
			const NotSendMail = row.find('[data-field="NotSendMail"]').is(':checked') ? 1 : 0;
			
            $.post('', { action: 'update', Id: Id, Number: Number, ReleaseDate: ReleaseDate, ExpirationDate: ExpirationDate,, Obs: Obs, NotSendMail: NotSendMail }, function (response) {
                alert('Modificările au fost salvate cu succes!');
            });
        });

        // Revocă cererea
        $(document).on('click', '.revoke-btn', function () {
            const Id = $(this).closest('tr').data('id');

            $.post('', { action: 'revoke', Id: Id }, function (response) {
                alert('Cererea a fost revocată.');
                location.reload();
            });
        });

        // Șterge cererea
        $(document).on('click', '.delete-btn', function () {
            const Id = $(this).closest('tr').data('id');

            $.post('', { action: 'delete', Id: Id }, function (response) {
                if (response === 'unauthorized') {
                    alert('Nu aveți permisiunea de a șterge această cerere.');
                } else {
                    alert('Cererea a fost ștearsă.');
                    location.reload();
                }
            });
        });
    });
</script>

</body>
</html>
