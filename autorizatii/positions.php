<?php
require 'db.php';

// Fetch positions
if (isset($_GET['action']) && $_GET['action'] === 'fetch') {
    // HTML-ul generat pentru răspuns
    echo '<table class="table table-bordered">';
    echo '<thead>
            <tr>
                <th>Denumire</th>
                <th>Conducere</th>
                <th>Inactivă</th>
                <th>Actions</th>
            </tr>
          </thead>';
    echo '<tbody>';

    // Rând gol pentru completare de date noi
    echo '<tr data-id="new">
            <td><input type="text" class="form-control" data-field="Name" placeholder="Name"></td>
            <td><input type="checkbox" data-field="IsCP"></td>
            <td></td>
            <td>
                <button class="btn btn-success btn-sm save-position-btn">Save</button>
            </td>
          </tr>';

    // Rânduri existente din baza de date
    $stmt = $GLOBALS['pdo']->query("SELECT *, CASE WHEN IsDeleted = 0 THEN 'No' ELSE 'Yes' END AS Deleted FROM Positions");
    $positions = $stmt->fetchAll(PDO::FETCH_ASSOC);

    foreach ($positions as $position) {
        echo '<tr data-id="' . $position['Id'] . '">';
        echo '<td><input type="text" class="form-control" value="' . htmlspecialchars($position['Name']) . '" data-field="Name"></td>';
        echo '<td><input type="checkbox" ' . ($position['IsCP'] ? 'checked' : '') . ' data-field="IsCP"></td>';
        echo '<td>' . $position['Deleted'] . '</td>';
        echo '<td>
                <button class="btn btn-primary btn-sm save-position-btn">Save</button>
                <button class="btn btn-danger btn-sm delete-position-btn">Delete</button>
              </td>';
        echo '</tr>';
    }

    echo '</tbody></table>';
    exit;
}

// Insert position
if (isset($_POST['action']) && $_POST['action'] === 'insert') {
    $stmt = $GLOBALS['pdo']->prepare("INSERT INTO Positions (Name, IsCP, IsAddedBy, IsAddedDate) VALUES (?, ?, 1, NOW())");
    $stmt->execute([
        $_POST['Name'],
        $_POST['IsCP'] ? 1 : 0
    ]);
    echo $GLOBALS['pdo']->lastInsertId();
    exit;
}

// Update position
if (isset($_POST['action']) && $_POST['action'] === 'update') {
    $stmt = $GLOBALS['pdo']->prepare("UPDATE Positions SET Name = ?, IsCP = ?, LastModifiedBy = 1, LastModifiedDate = NOW() WHERE Id = ?");
    $stmt->execute([
        $_POST['Name'],
        $_POST['IsCP'] ? 1 : 0,
        $_POST['Id']
    ]);
    echo 'success';
    exit;
}

// Soft delete
if (isset($_POST['action']) && $_POST['action'] === 'delete') {
    $stmt = $GLOBALS['pdo']->prepare("UPDATE Positions SET IsDeleted = 1, IsDeletedBy = 1, IsDeletedDate = NOW() WHERE Id = ?");
    $stmt->execute([$_POST['Id']]);
    echo 'success';
    exit;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Positions</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
<div class="container mt-4">
    <h4>Positions Management</h4>
    <div id="positions-table"></div>
</div>

<script>
    $(document).ready(function () {
        // Funcția pentru încărcarea tabelului Positions
        function loadPositions() {
            $.get('positions.php?action=fetch', function (data) {
                $('#positions-table').html(data);
            });
        }
        loadPositions();

        // Salvarea unui position nou sau modificat
        $(document).on('click', '.save-position-btn', function () {
            const row = $(this).closest('tr');
            const id = row.data('id');
            const action = id === 'new' ? 'insert' : 'update';

            const data = {
                action: action,
                Id: id !== 'new' ? id : undefined,
                Name: row.find('[data-field="Name"]').val(),
                IsCP: row.find('[data-field="IsCP"]').is(':checked') ? 1 : 0
            };

            $.post('positions.php', data, function () {
                loadPositions();
            });
        });

        // Ștergerea unui position
        $(document).on('click', '.delete-position-btn', function () {
            const id = $(this).closest('tr').data('id');

            $.post('positions.php', { action: 'delete', Id: id }, function () {
                loadPositions();
            });
        });
    });
</script>
</body>
</html>