<?php
require 'db.php';

// Fetch departments
if (isset($_GET['action']) && $_GET['action'] === 'fetch') {
    // HTML-ul generat pentru răspuns
    echo '<table class="table table-bordered">';
    echo '<thead>
            <tr>
                <th>Denumire</th>
                <th>Abreviere</th>
                <th>MJ</th>
                <th><PERSON><PERSON></th>
                <th><PERSON><PERSON>ț<PERSON>ni</th>
            </tr>
          </thead>';
    echo '<tbody>';

    // Rând gol pentru completare de date noi
    echo '<tr data-id="new">
            <td><input type="text" class="form-control" data-field="Name" placeholder="Denumire"></td>
            <td><input type="text" class="form-control" data-field="ShortName" placeholder="Abreviere"></td>
            <td><input type="checkbox" data-field="IsMJ"></td>
            <td></td>
            <td>
              <button class="btn btn-success btn-sm save-btn" data-section="departments">Save</button>

            </td>
          </tr>';

    // Rânduri existente din baza de date
    $stmt = $GLOBALS['pdo']->query("SELECT *, CASE WHEN IsDeleted = 0 THEN 'Nu' ELSE 'Da' END AS Șters FROM Departments");
    $departments = $stmt->fetchAll(PDO::FETCH_ASSOC);

    foreach ($departments as $dept) {
        echo '<tr data-id="' . $dept['Id'] . '">';
        echo '<td><input type="text" class="form-control" value="' . htmlspecialchars($dept['Name']) . '" data-field="Name"></td>';
        echo '<td><input type="text" class="form-control" value="' . htmlspecialchars($dept['ShortName']) . '" data-field="ShortName"></td>';
        echo '<td><input type="checkbox" ' . ($dept['IsMJ'] ? 'checked' : '') . ' data-field="IsMJ"></td>';
        echo '<td>' . $dept['Șters'] . '</td>';
        echo '<td>
<button class="btn btn-success btn-sm save-btn" data-section="departments">Save</button>
<button class="btn btn-danger btn-sm delete-btn" data-section="departments">Delete</button>

              </td>';
        echo '</tr>';
    }

    echo '</tbody></table>';
    exit;
}

// Insert department
if (isset($_POST['action']) && $_POST['action'] === 'insert') {
    $stmt = $GLOBALS['pdo']->prepare("INSERT INTO Departments (Name, ShortName, IsMJ, IsAddedBy, IsAddedDate) VALUES (?, ?, ?, 1, NOW())");
    $stmt->execute([
        $_POST['Name'],
        $_POST['ShortName'],
        $_POST['IsMJ'] ? 1 : 0
    ]);
    echo $GLOBALS['pdo']->lastInsertId();
    exit;
}

// Update department
if (isset($_POST['action']) && $_POST['action'] === 'update') {
    $stmt = $GLOBALS['pdo']->prepare("UPDATE Departments SET Name = ?, ShortName = ?, IsMJ = ?, LastModifiedBy = 1, LastModifiedDate = NOW() WHERE Id = ?");
    $stmt->execute([
        $_POST['Name'],
        $_POST['ShortName'],
        $_POST['IsMJ'] ? 1 : 0,
        $_POST['Id']
    ]);
    echo 'success';
    exit;
}

// Soft delete
if (isset($_POST['action']) && $_POST['action'] === 'delete') {
    $stmt = $GLOBALS['pdo']->prepare("UPDATE Departments SET IsDeleted = 1, IsDeletedBy = 1, IsDeletedDate = NOW() WHERE Id = ?");
    $stmt->execute([$_POST['Id']]);
    echo 'success';
    exit;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Departments</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
<div class="container mt-4">
    <h4>Departments Management</h4>
    <div id="departments-table"></div>
</div>

<script>
    $(document).ready(function () {
        // Funcția pentru încărcarea tabelului
        function loadDepartments() {
            $.get('departments.php?action=fetch', function (data) {
                $('#departments-table').html(data);
            });
        }
        loadDepartments();

        // Salvarea unui departament nou
        $(document).on('click', '.save-department-btn', function () {
            const row = $(this).closest('tr');
            const id = row.data('id');
            const data = {
                action: id === 'new' ? 'insert' : 'update',
                Id: id !== 'new' ? id : undefined,
                Name: row.find('[data-field="Name"]').val(),
                ShortName: row.find('[data-field="ShortName"]').val(),
                IsMJ:  0
            };

            $.post('departments.php', data, function () {
                loadDepartments();
            });
        });

        // Ștergerea unui departament
        $(document).on('click', '.delete-department-btn', function () {
            const row = $(this).closest('tr');
            const id = row.data('id');

            $.post('departments.php', { action: 'delete', Id: id }, function () {
                loadDepartments();
            });
        });
    });
</script>
</body>
</html>