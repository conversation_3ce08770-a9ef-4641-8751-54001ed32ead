<?php
require 'db.php';
require 'functions.php';
//global $pdo;

$query = "
    SELECT 
        p.Id as PersonId,
        a.Id,
        CONCAT(p.Name, ' ', p.Firstname) AS NumePrenume,
        d.Id AS DepartamentId,
        d.ShortName AS Departament,
        pos.Name AS Position,
        p.Email as Email,
        t.ShortName AS Tip,
        l.ShortName AS Nivel,
        l.Id AS NivelId,
        s.Name AS Serie,
        a.Number AS Numar,
        a.ReleaseDate AS DataEliberarii,
        a.ExpirationDate AS DataExpirarii
    FROM sic.auth a
    JOIN sic.person p ON a.IdPerson = p.Id
    JOIN sic.departments d ON p.Department = d.Id
    JOIN sic.positions pos ON p.Position = pos.Id
    JOIN sic.types t ON a.IdType = t.Id
    JOIN sic.levels l ON a.IdLevel = l.Id
    JOIN sic.series s ON a.IdSerie = s.Id
    WHERE a.IsDeleted = 0 
      AND a.IsRevoked = 0 
      AND p.IsDeleted = 0 
      AND d.IsMJ = 1 
      AND l.IsNational = 1  
      AND l.IsUE = 0      
      AND l.IsNATO = 0     
      AND a.ExpirationDate<=Date_add(current_date(), INTERVAL 60 DAY) ";
$stmt = $GLOBALS['pdo']->prepare($query);
$stmt->execute();
$persoane = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Obținem lista tuturor șefilor de departamente
$query_sefi = "
    SELECT 
        p.Id AS SefId,
        CONCAT(p.Name, ' ', p.Firstname) AS SefNume,
        p.Email AS SefEmail,
        p.Department AS DepartamentId
    FROM sic.person p
    WHERE p.IsBoss = 1 AND p.IsDeleted = 0
";
$stmt_sefi = $GLOBALS['pdo']->prepare($query_sefi);
$stmt_sefi->execute();
$sefi_departamente = $stmt_sefi->fetchAll(PDO::FETCH_ASSOC);

// Transformăm lista șefilor într-un array asociativ
$sefi_map = [];
foreach ($sefi_departamente as $sef) {
    $departamentId = $sef['DepartamentId'];
    if (!isset($sefi_map[$departamentId])) {
        $sefi_map[$departamentId] = [];
    }
    $sefi_map[$departamentId][] = [
        'Nume' => $sef['SefNume'],
        'Email' => $sef['SefEmail']
    ];
}

//  Grupăm persoanele pe departamente
$departamente = [];
foreach ($persoane as $persoana) {
    $departamentId = $persoana['DepartamentId'];
    $departamentNume = $persoana['Departament'];

    if (!isset($departamente[$departamentId])) {
        $departamente[$departamentId] = [
            'Sefi' => $sefi_map[$departamentId] ?? [],
            'NumeDepartament' => $departamentNume,
            'Angajati' => []
        ];
    }

    $departamente[$departamentId]['Angajati'][] = [
        'Id' => $persoana['PersonId'],
        'NumePrenume' => $persoana['NumePrenume'],
        'Position' => $persoana['Position'],
		'DataEliberarii' => $persoana['DataEliberarii'],
        'DataExpirarii' => $persoana['DataExpirarii'],
        'Email' => $persoana['Email']
    ];
}

// Afișăm previzualizarea emailurilor și trimitem efectiv

echo "<h2>Preview Emailuri</h2>";

$SICEmail = "<EMAIL>";
$SICMessage = "<h3>Autorizatii MJ care urmeaza sa expire:</h3>";
$SICMessage .= "<table border='1' cellpadding='5' cellspacing='0'>";
$SICMessage .= "<tr><th>ID</th><th>Nume</th><th>Functie</th><th>Departament</th><th>Data Eliberarii</th><th>Data Expirarii</th></tr>";
$i=0;
foreach ($departamente as $deptId => $info) {
    $numeDepartament = $info['NumeDepartament'];

    if (empty($info['Sefi'])) {
        continue;
    }

    $emailuriSefi = array_column($info['Sefi'], 'Email');
    $emailTo = implode(',', $emailuriSefi);

    foreach ($info['Angajati'] as $angajat) {

		$emailTo=$emailTo;
        // Construim mesajul pentru fiecare angajat
        $subject = "Notificare expirare autorizație - {$angajat['NumePrenume']}";
        $message = "<h3>Autorizația {$angajat['NumePrenume']} din departamentul {$numeDepartament} urmează să expire. </h3>";
        $message .= "<p><strong>Funcție:</strong> {$angajat['Position']}</p>";
        $emailCC = $angajat['Email'];

        //Previzualizare email
        echo "<h3>Email pentru șefi și {$angajat['NumePrenume']}</h3>";
        echo "<p><strong>Subiect:</strong> {$subject}</p>";
        echo "<p><strong>Destinatari (To: - Șefii):</strong> {$emailTo}</p>";
        echo "<p><strong>CC (Angajatul):</strong> {$emailCC}</p>";
        echo $message;
        echo "<hr>";
$i++;
	   echo "<h1 style='color:red'>RADU $i</h1> $emailTo, $subject, $message, 'documente/angajament.docx', DE AICI, $emailCC, STOP";
	    sicEmail($emailTo, $subject, $message, 'documente/angajament.docx',$emailCC );
		// Trimiterea emailului cu atașament (opțional)

        // Adăugăm și la emailul SIC
        $SICMessage .= "<tr>
                                <td>{$angajat['Id']}</td>
                                <td>{$angajat['NumePrenume']}</td>
                                <td>{$angajat['Position']}</td>
                                <td>{$numeDepartament}</td>
                            </tr>";
    }
}

// Email către sic
$SICMessage .= "</table>";

echo "<h3>Email pentru SIC</h3>";
echo "<p><strong>Subiect:</strong> Lista completa a autorizatiilor</p>";
echo "<p><strong>Destinatar (To: - SIC):</strong> {$SICEmail}</p>";
echo $SICMessage;
echo "<hr>";

sendEmail($SICEmail, "Lista completa a autorizatiilor care urmeaza sa expire", $SICMessage, 'documente/raport.pdf');

echo "<p><strong>Toate emailurile au fost trimise!</strong></p>";
?>


<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Listă Cereri</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
<?php include 'navbar.php'; ?>
<div class="container mt-5">
    <h3>Listă Cereri</h3>




</div>
</body>
</html>


//foreach($persoane as $p)
//{
//    $continut = "
//    <!DOCTYPE html>
//        <html>
//        <head>
//            <title>Serviciul Informații Clasificate</title>
//        </head>
//        <body>
//          <p>Buna ziua, autorizatia dumneavoastra SSV<br>Va expira <span style='color:red'>MAINE</span>.</p>
//          <p>La revedere</p>
//        </body>
//    </html>";
//
//    $atasamente = ['atasamente/angajament.docx', 'atasamente/confidentialitate.docx', 'atasamente/procedura.pdf'];
//    scEmail($continut, 'angajament', '<EMAIL>', $atasamente);
//  //  scEmail($continut, 'angajament', '<EMAIL>', $atasamente);
//}