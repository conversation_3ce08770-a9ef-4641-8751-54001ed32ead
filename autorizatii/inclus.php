<?php
// Include fișierele necesare
require 'db.php';
require_once 'security.php';

// Obține contul utilizatorului autentificat pe stație
//$loggedStationAccount = getenv('USERNAME') ?? ($_SERVER['AUTH_USER'] ?? 'N/A');
if(!isset($_SESSION['ad_account'])){
    header('Location: home.php');
}
// Setează contul în sesiune dacă nu este deja setat
//if (!isset($_SESSION['ad_account'])) {
//    $_SESSION['ad_account'] = $loggedStationAccount;
//}

// Obține utilizatorul din baza de date
//$user = getAuthenticatedUser($GLOBALS['pdo']);
$user = $_SESSION['ad_account'];

// Determinăm tipul utilizatorului
$isBoss = $user['IsBoss'] ?? false;
$isAdmin = $user['IsAdmin'] ?? false;
$isWorker = $user['IsWorker'] ?? false;
?>
