<?php
require 'db.php';
require_once 'security.php';
require_once '../inc/cfg_functions.php';

// Verifică permisiunile pentru acces
$isBoss = $_SESSION['IsBoss'] ?? false;
$isAdmin = $_SESSION['IsAdmin'] ?? false;
$isWorker = $_SESSION['IsWorker'] ?? false;


// Logică de permisiuni
if (!$isAdmin && !$isWorker && (!$isBoss || (!$isAdmin && !$isWorker))) {
    header("Location: home.php");
    exit();
}

// Fetch dropdown data
$persons = $GLOBALS['pdo']->query("SELECT Id, CONCAT(Firstname, ' ', Name) AS FullName FROM sic.person WHERE IsDeleted = 0 ORDER BY Name ASC")->fetchAll(PDO::FETCH_ASSOC);
$types = $GLOBALS['pdo']->query("SELECT Id, Name FROM sic.types")->fetchAll(PDO::FETCH_ASSOC);
$levels = $GLOBALS['pdo']->query("SELECT Id, Name FROM sic.levels")->fetchAll(PDO::FETCH_ASSOC);

// Fetch serie name based on IdLevel (AJAX)
if (isset($_GET['action']) && $_GET['action'] === 'fetchSerie' && isset($_GET['IdLevel'])) {
    $IdLevel = $_GET['IdLevel'];
    $stmt = $GLOBALS['pdo']->prepare("SELECT Id, Name FROM sic.series WHERE IdLevel = ?");
    $stmt->execute([$IdLevel]);
    $serie = $stmt->fetch(PDO::FETCH_ASSOC);
    echo json_encode($serie);
    exit;
}

// Handle form submission
$message = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $IdPerson = $_POST['IdPerson'];
    $IdType = $_POST['IdType'];
    $IdLevel = $_POST['IdLevel'];
    $IdSerie = $_POST['IdSerie'];
    $Number = $_POST['Number'];
    $ReleaseDate = $_POST['ReleaseDate'];
    $ExpirationDate = $_POST['ExpirationDate'];
	$Obs= $_POST['Obs'];
	
	//echo $IdPerson;
	$ustmt = $GLOBALS['pdo']->prepare("SELECT Department FROM sic.person WHERE IsDeleted = 0 AND Id = ?");
	$ustmt->execute([$IdPerson]);

	$IdDepartment = $ustmt->fetchColumn(); // Preia direct valoarea unică
	//echo $IdDepartment; // Afișează valoarea corectă

	
/* $ustmt = $GLOBALS['pdo']->prepare("SELECT Department FROM sic.person WHERE IsDeleted = 0 AND Id = ?");
$ustmt->execute([$IdPerson]);

	$result = $ustmt ->fetch(PDO::FETCH_COLUMN);
	$IdDepartment = $result[2] ?? null;
	echo $IdDepartment; */
	
	
	//$IdDepartment = .....aici trebuie sa adaug departamentul in care este persoana acum
    $IsAddedBy = $_SESSION['user_id']; // Utilizatorul autentificat
    // Verificare pentru autorizații existente
	// $checkStmt = $GLOBALS['pdo']->prepare("SELECT Id, Number FROM sic.auth WHERE IdPerson = ? AND IdLevel = ? AND IsDeleted = 0 AND IsRevoked = 0 AND ExpirationDate >= CURDATE()");
    $checkStmt = $GLOBALS['pdo']->prepare("SELECT Id, Number FROM sic.auth WHERE IdPerson = ? AND IsDeleted = 0 AND IsRevoked = 0 AND ExpirationDate >= CURDATE()");
    //$checkStmt->execute([$IdPerson, $IdLevel]);
	$checkStmt->execute([$IdPerson]);
    $existingAuth = $checkStmt->fetch(PDO::FETCH_ASSOC);

    if ($existingAuth) {
        // Revocare autorizație existentă
        $revokeStmt = $GLOBALS['pdo']->prepare("UPDATE sic.auth SET IsRevoked = 1, IsRevokedBy = ?, IsRevokedDate = NOW() WHERE Id = ?");
        $revokeStmt->execute([$_SESSION['user_id'], $existingAuth['Id']]);

        // Mesaj de avertizare
        $message = "<div class='alert alert-warning mt-3 text-center'>Autorizația existentă (" . htmlspecialchars($existingAuth['Number']) . " Număr: " . htmlspecialchars($existingAuth['Number']) . ") a fost revocată.</div>";
    }

    // Inserarea în tabelă
    $stmt = $GLOBALS['pdo']->prepare("INSERT INTO sic.auth (
        IdPerson, IdSerie, IdType, IdLevel, Number, 
        ReleaseDate, ExpirationDate, IsRevoked, IsRevokedBy, 
        IsRevokedDate, IsAddedBy, IsAddedDate, 
        IsDeleted, IsDeletedBy, IsDeletedDate, 
        LastModifiedBy, LastModifiedDate,IdDepartment,Obs
    ) VALUES (
        ?, ?, ?, ?, ?, 
        ?, ?, 0, NULL, 
        NULL, ?, NOW(), 
        0, NULL, NULL, 
        NULL, NULL,?,?
    )");
    $stmt->execute([
        $IdPerson, $IdSerie, $IdType, $IdLevel, $Number,
        $ReleaseDate, $ExpirationDate, $IsAddedBy,$IdDepartment,$Obs
    ]);

    $message .= "<div class='alert alert-success mt-3 text-center'>Autorizația a fost adăugată cu succes!</div>";
}
?>


<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Adaugă Autorizație</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
<?php include 'navbar.php'; ?>
<div class="container mt-5">
    <h3>Adaugă o Autorizație</h3>
    <form method="POST" action="">
        <div class="mb-3">
            <label for="IdPerson" class="form-label">Persoană</label>
            <select class="form-control" name="IdPerson" required>
                <option value="">-- Selectează Persoană --</option>
                <?php foreach ($persons as $person): ?>
                    <option value="<?= $person['Id'] ?>"><?= htmlspecialchars($person['FullName']) ?></option>
                <?php endforeach; ?>
            </select>
        </div>

        <div class="mb-3">
            <label for="IdType" class="form-label">Tip</label>
            <select class="form-control" name="IdType" required>
                <option value="">-- Selectează Tip --</option>
                <?php foreach ($types as $type): ?>
                    <option value="<?= $type['Id'] ?>"><?= htmlspecialchars($type['Name']) ?></option>
                <?php endforeach; ?>
            </select>
        </div>

        <div class="mb-3">
            <label for="IdLevel" class="form-label">Nivel</label>
            <select class="form-control" name="IdLevel" id="IdLevel" required>
                <option value="">-- Selectează Nivel --</option>
                <?php foreach ($levels as $level): ?>
                    <option value="<?= $level['Id'] ?>"><?= htmlspecialchars($level['Name']) ?></option>
                <?php endforeach; ?>
            </select>
        </div>

        <div class="mb-3">
            <label for="serie" class="form-label">Serie</label>
            <input type="text" class="form-control" id="serieDisplay" readonly>
            <input type="hidden" class="form-control" id="serie" name="IdSerie" readonly>
        </div>

        <div class="mb-3">
            <label for="Number" class="form-label">Număr</label>
            <input type="number" class="form-control" name="Number" required>
        </div>

        <div class="mb-3">
            <label for="ReleaseDate" class="form-label">Data Emiterii</label>
            <input type="date" class="form-control" name="ReleaseDate" id="ReleaseDate" required>
        </div>

        <div class="mb-3">
            <label for="ExpirationDate" class="form-label">Data Expirării</label>
            <input type="date" class="form-control" name="ExpirationDate" id="ExpirationDate" required>
        </div>
		<div class="mb-3">
            <label for="Obs" class="form-label">Observații</label>
            <input type="string" class="form-control" name="Obs" required>
        </div>

        <button type="submit" class="btn btn-primary">Adaugă Cerere</button>
    </form>

    <!-- Mesaj de succes -->
    <?php if (!empty($message)) echo $message; ?>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const releaseDateInput = document.getElementById('ReleaseDate');
        const expirationDateInput = document.getElementById('ExpirationDate');
        const levelInput = document.getElementById('IdLevel');
        const serieInput = document.getElementById('serie');
        const serieDisplay = document.getElementById('serieDisplay');

        // Precompletare ExpirationDate când se selectează ReleaseDate
        releaseDateInput.addEventListener('change', function () {
            const releaseDateValue = releaseDateInput.value;

            if (releaseDateValue) {
                // Calculăm data expirării (+4 ani)
                const releaseDate = new Date(releaseDateValue);
                const expirationDate = new Date(releaseDate.setFullYear(releaseDate.getFullYear() + 4));

                // Formatăm data în format ISO pentru câmpul date (yyyy-mm-dd)
                const formattedExpirationDate = expirationDate.toISOString().split('T')[0];

                // Setăm valoarea precompletată în câmpul ExpirationDate
                expirationDateInput.value = formattedExpirationDate;
            }
        });

        // Precompletare serie în funcție de nivel
        levelInput.addEventListener('change', function () {
            const IdLevel = levelInput.value;

            if (IdLevel) {
                $.get('auth.php', { action: 'fetchSerie', IdLevel: IdLevel }, function (response) {
                    const data = JSON.parse(response);
                    if (data && data.Id && data.Name) {
                        serieInput.value = data.Id;          // Stochează ID-ul seriei
                        serieDisplay.value = data.Name;      // Afișează numele seriei
                    } else {
                        serieInput.value = '';
                        serieDisplay.value = '';
                    }
                });
            } else {
                serieInput.value = '';
                serieDisplay.value = '';
            }
        });
    });
</script>
</body>
</html>
