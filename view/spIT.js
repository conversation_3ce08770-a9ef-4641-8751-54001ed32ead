const Toast = Swal.mixin({
    toast: true,
    position: "top-end",
    showConfirmButton: false,
    timer: 3000,
    timerProgressBar: true,
    didOpen: (toast) => {
        toast.onmouseenter = Swal.stopTimer;
        toast.onmouseleave = Swal.resumeTimer;
    }
});

function ascunde() {
    $(document).find('input[coloana], .select2').each(function () {
        $(this).hide().addClass('ascuns').removeClass('afisat');
        if ($('.select2').hasClass("select2-hidden-accessible")) {
            $(this).select2('destroy');
        }
    });
    $(document).find('span.select2-search, span.select2-results').hide();
    $(document).find('button.butonEdit').remove();
}

//todo de trimis datele referitoare la structura ca sa aduca persoanele din tabel
function editStructura(dis) {
    var tr = dis.closest('tr'),
        inputHidden = tr.find('input[type="hidden"]'),
        tabel = inputHidden.attr('tabel'),
        pk = inputHidden.attr('pk'),
        id_rowBaza = inputHidden.attr('id_row');

    var formData = new FormData();
    formData.append('modifStructura', true);
    formData.append('tabel', tabel);
    formData.append('pk', pk);
    formData.append('id_rowBaza', id_rowBaza);

    $.ajax({
        method: 'post',
        processData: false,
        contentType: false,
        cache: false,
        data: formData,
        enctype: 'multipart/form-data',
        url: 'controller/sp_it_just.php',
        success: function (data, status, jqXHR) {
            $('#modalFullscreen').modal('toggle').find('div.modal-body').html(data.message);
            $('#modalFullscreen').find('h5.modal-title').text(dis.text().trim());
        },
        error: function (r) {
            Swal.fire({
                icon: "error",
                title: "Oops...",
                text: r.message,
                // showConfirmButton: false,
                footer: 'Ceva nu a mers bine!'
            });
        }
    });

}


function sendEditData(dis) {
    var tr = dis.closest('tr'),
        td = dis.closest('td');

    if (session_id_structura == 287) {
        // Ministerul Justitiei poate modifica si pt restul instantelor din tarar
    } else if (!tr.hasClass('user-editable-row')) {
        Swal.fire({
            icon: "error",
            title: "Nu aveți permisiunea de a edita această înregistrare"
        });
        return;
    }

    var inputHidden = td.find('input[type="text"].ascuns, select.ascuns'),
        tabel = inputHidden.attr('tabel'),
        pk = inputHidden.attr('pk'),
        id_rowBaza = inputHidden.attr('id_row'),
        input = $(document).find('div.swal2-html-container').find('input'),
        select = $(document).find('div.swal2-html-container').find('select');

    (typeof input.attr('coloana') === "undefined") ? coloana = select.attr('coloana') : coloana = input.attr('coloana');

    var formData = new FormData();
    formData.append('changeVal', true);
    formData.append('tabel', tabel);
    formData.append('pk', pk);
    formData.append('id_rowBaza', id_rowBaza);
    formData.append('coloana', coloana);

    input.each(function () {
        formData.append('input[]', $(this).val());
    });

    select.each(function () {
        formData.append('input[]', $(this).val());
    });

    $.ajax({
        method: 'post',
        processData: false,
        contentType: false,
        cache: false,
        data: formData,
        url: 'controller/sp_it_just.php',
        success: function (data) {
            Toast.fire({
                icon: data.status === 'ok' ? 'success' : 'error',
                title: data.message
            });
            td.html('OK');
        },
        error: function (r) {
            Swal.fire({
                icon: "error",
                title: "Oops...",
                text: r.message
            });
        }
    });
}


function swalInput(obj) {
    var divs = obj.find('div');
    var html = '';

    divs.each(function () {
        var input = $(this).find('input').clone().removeClass('ascuns').css('display', 'block');;
        var select = $(this).find('select').clone().removeClass('ascuns').css('display', 'block');
        var inputColoana = input.attr('coloana');
        var selectColoana = select.attr('coloana');

        if (input.length) {
            if (inputColoana) {
                html += `<span class="coloana">${inputColoana}</span>`.replace(/id_/g, '').replace(/_/g, ' ');
            }
            html += input.prop('outerHTML');
        }

        if (select.length) {
            if (selectColoana) {
                html += `<span class="coloana">${selectColoana}</span>`.replace(/id_/g, '').replace(/_/g, ' ');
            }
            html += select.prop('outerHTML');
        }
    });

    Swal.fire({
        html: html,
        showDenyButton: true,
        denyButtonText: 'Anulare',
        confirmButtonText: 'Modificare',
        focusConfirm: false,
        didOpen: () => {
            $('#swal2-html-container').css({
                'display': 'flex',
                'flex-direction': 'column',
                'align-items': 'center'
            });
            $('#swal2-html-container').find('span.coloana').each(function () {
                $(this).css({'text-transform': 'capitalize'});
            });
            $('div.swal2-actions > button.swal2-confirm').removeClass('swal2-confirm').addClass('btn btn-ghost-dark btn-lg butonEdit');
        },
        preConfirm: () => {
            return divs.map(function () {
                return {
                    input: $(this).find('input').val(),
                    select: $(this).find('select').val()
                };
            }).get();
        }
    }).then((result) => {
        if (result.isConfirmed) {
            sendEditData(obj);
        }
    });
}


$(document).ready(function () {

    $(document).off('keydown').on('keydown', function (e) {
        if (e.key == "Escape") {
            ascunde();
        }
    });

    $(document).off('click', 'button[data-bs-dismiss="modal"]').on('click', 'button[data-bs-dismiss="modal"]', function () {
        $('#modalFullscreen').modal('toggle');
    });

    $(document).off('click', 'td').on('click', 'td:not(#modalFullscreen td)', function (e) {
        e.preventDefault();
        e.stopImmediatePropagation();

        var dis = $(this),
            td = dis.closest('td'),
            tr = dis.closest('tr');

        var rowStructureId = tr.attr('data-structure-id');
        if (session_id_structura == 287) {
            // Ministerul Justitiei poate modifica si pt restul instantelor din tarar
        } else if (rowStructureId != session_id_structura && !session_id_instanta_subordonata.some(item => item.id === parseInt(rowStructureId)) && session_id_structura != 287) {
            Toast.fire({
                icon: "error",
                title: "Nu aveți permisiunea de a edita această înregistrare"
            });
            return;
        }
        if (td.find('input.ascuns').length || td.find('select.ascuns').length) {
            swalInput(dis);
            return;
        } else {
            editStructura(dis);
        }
    });

    $(document).off('click', 'button.butonEdit').on('click', 'button.butonEdit', function () {
        sendEditData($(this));
    });


    $(document).off('click', '.persoanaDel').on('click', '.persoanaDel', function () {
        var dis = $(this),
            numePersoana = dis.attr('numePersoana'),
            idPersoana = dis.attr('idPersoana');

        formData = new FormData();
        formData.append('delPersoana', true);
        formData.append('idPersoana', idPersoana);

        Swal.fire({
            title: "Stergeti pe " + numePersoana + " din cartea de telefon, <b>permanent</b>?",
            text: "Actiune ireversibila",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Da, sterge!"
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    method: 'post',
                    processData: false,
                    contentType: false,
                    cache: false,
                    data: formData,
                    enctype: 'multipart/form-data',
                    url: 'controller/sp_it_just.php',
                    success: function (data, status, jqXHR) {
                        Swal.fire({
                            icon: data.status,
                            title: data.message,
                            showConfirmButton: false,
                            timer: 1000
                        }).then((result) => {
                            dis.closest('tr').hide();
                            // $('tr[tabel="carte_telefon"][pk="id_carte_telefon"][id_row="' + idPersoana + '"]').hide();
                            $('input[type="hidden"][tabel="carte_telefon"][pk="id_carte_telefon"][id_row="' + idPersoana + '"]').closest('tr').hide();
                        });
                    },
                    error: function (r) {
                        Swal.fire({
                            icon: "error",
                            title: "Oops...",
                            text: r.message,
                            // showConfirmButton: false,
                            footer: 'Ceva nu a mers bine!'
                        });
                    }
                });
            }
        });
    });

    $(document).off('click', '.persoanaAddForm').on('click', '.persoanaAddForm', function () {
        var dis = $(this),
            idStructura = dis.attr('idStructura');
        var formData = new FormData();
        formData.append('persoanaAddForm', true);
        formData.append('idStructura', idStructura);

        $.ajax({
            method: 'post',
            processData: false,
            contentType: false,
            cache: false,
            data: formData,
            enctype: 'multipart/form-data',
            url: 'controller/sp_it_just.php',
            success: function (data, status, jqXHR) {
                dis.closest('table').find('tbody').append(data.message);
                $(document).find(".modal-body").animate({scrollTop: $(".modal-body > table").height()}, 1000);
            },
            error: function (r) {
                Swal.fire({
                    icon: "error",
                    title: "Oops...",
                    text: r.message,
                    // showConfirmButton: false,
                    footer: 'Ceva nu a mers bine!'
                });
            }
        });

    });

    $(document).off('click', '.persoanaAddBtn').on('click', '.persoanaAddBtn', function () {
        var dis = $(this),
            tr = dis.closest('tr'),
            nume = tr.find('input.nume').val(),
            functie = tr.find('select.functie').val(),
            interior = tr.find('input.interior').val(),
            mobil = tr.find('input.mobil').val(),
            fax = tr.find('input.fax').val(),
            email = tr.find('input.email').val(),
            obs = tr.find('input.obs').val(),
            idInstanta = dis.attr('idInstanta'),
            formData = new FormData();

        formData.append('persoanaAddBtn', true);
        formData.append('idInstanta', idInstanta);
        formData.append('nume', nume);
        formData.append('functie', functie);
        formData.append('interior', interior);
        formData.append('mobil', mobil);
        formData.append('fax', fax);
        formData.append('email', email);
        formData.append('obs', obs);

        $.ajax({
            method: 'post',
            processData: false,
            contentType: false,
            cache: false,
            data: formData,
            enctype: 'multipart/form-data',
            url: 'controller/sp_it_just.php',
            success: function (data, status, jqXHR) {
                if (data.status == 'ok') {
                    tr.hide().after('<tr><td>' + nume + '</td><td colspan="7">' + $('#modalFullscreen').find('.modal-title').text() + '</td><td>OK</td></tr>');
                } else {
                    Swal.fire({
                        icon: "error",
                        text: data.message,
                        // showConfirmButton: false,
                    });
                }
            },
            error: function (r) {
                Swal.fire({
                    icon: "error",
                    title: "Oops...",
                    text: r.message,
                    // showConfirmButton: false,
                    footer: 'Ceva nu a mers bine!'
                });
            }
        });

    });

});
