function hide() {
    $(document).find('input[coloana], .select2').each(function () {
        $(this).hide().addClass('ascuns').removeClass('afisat');
        if ($('.select2').hasClass("select2-hidden-accessible")) {
            $(this).select2('destroy');
        }
    });
    $(document).find('span.select2-search, span.select2-results').hide();
    $(document).find('button.butonEdit').remove();
}

function editStructura(dis) {
    var tr = dis.closest('tr'),
        inputHidden = tr.find('input[type="hidden"]'),
        tabel = inputHidden.attr('tabel'),
        pk = inputHidden.attr('pk'),
        id_rowBaza = inputHidden.attr('id_row');

    var formData = new FormData();
    formData.append('modifStructura', true);
    formData.append('tabel', tabel);
    formData.append('pk', pk);
    formData.append('id_rowBaza', id_rowBaza);

    $.ajax({
        method: 'post',
        processData: false,
        contentType: false,
        cache: false,
        data: formData,
        enctype: 'multipart/form-data',
        url: 'controller/carte-telefon.php',
        success: function (data, status, jqXHR) {
            $('#modalFullscreen').modal('toggle').find('div.modal-body').html(data.message);
            $('#modalFullscreen').find('h5.modal-title').text(dis.text().trim());
            // $(document).find('div.modal-body').closest('.selectSchimbaStructura').select2();
            // $(document).off('click', 'td').on('click', 'td', function () {
            //     $(this).find('div > select.selectSchimbaStructura').select2();
            // });
        },
        error: function (r) {
            Swal.fire({
                icon: "error",
                title: "Oops...",
                text: r.message,
                // showConfirmButton: false,
                footer: 'Ceva nu a mers bine!'
            });
        }
    });

}

function sendEditData(dis) {
    var inputHidden = dis.closest('tr').find('input[type="hidden"]'),
        tabel = inputHidden.attr('tabel'),
        pk = inputHidden.attr('pk'),
        id_rowBaza = inputHidden.attr('id_row'),
        input = $(document).find('div.swal2-html-container').find('input'),
        select = $(document).find('div.swal2-html-container').find('select'),
        addHiddenInput = 0;
    console.log(dis);

    (typeof input.attr('coloana') === "undefined") ? coloana = select.attr('coloana') : coloana = input.attr('coloana');

    var formData = new FormData();
    formData.append('changeVal', true);
    formData.append('tabel', tabel);
    formData.append('pk', pk);
    formData.append('id_rowBaza', id_rowBaza);
    $(input).each(function (i, field) {
        formData.append('input[]', field.value);
    });
    if (input.length == 0) {
        $(select).each(function (i, field) {
            formData.append('input[]', field.value);
        });
    }
    formData.append('coloana', coloana);

    $.ajax({
        method: 'post',
        processData: false,
        contentType: false,
        cache: false,
        data: formData,
        enctype: 'multipart/form-data',
        url: 'controller/carte-telefon.php',
        success: function (data, status, jqXHR) {
            (coloana == 'etaj') ? dis.html("OK<input type='hidden' id='radu' tabel='carte_telefon' pk='id_carte_telefon' id_row='" + id_rowBaza + "'>") : dis.html('OK');

            const Toast = Swal.mixin({
                toast: true,
                position: "top-end",
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                didOpen: (toast) => {
                    toast.onmouseenter = Swal.stopTimer;
                    toast.onmouseleave = Swal.resumeTimer;
                }
            });
            Toast.fire({
                icon: "success",
                title: data.message
            });

        },
        error: function (r) {
            Swal.fire({
                icon: "error",
                title: "Oops...",
                text: r.message,
                // showConfirmButton: false,
                footer: 'Ceva nu a mers bine!'
            });
        }
    });
}


function swalInput(obj) {
    var divs = obj.find('div');
    var html = '';

    divs.each(function () {
        var input = $(this).find('input').clone().removeClass('ascuns');
        var select = $(this).find('select').clone().removeClass('ascuns');
        var inputColoana = input.attr('coloana');
        var selectColoana = select.attr('coloana');

        if (input.length) {
            if (inputColoana) {
                html += `<span class="coloana">${inputColoana}</span>`.replace(/id_/g, '').replace(/_/g, ' ');
            }
            html += input.prop('outerHTML');
        }

        if (select.length) {
            if (selectColoana) {
                html += `<span class="coloana">${selectColoana}</span>`.replace(/id_/g, '').replace(/_/g, ' ');
            }
            html += select.prop('outerHTML');
        }
    });

    Swal.fire({
        html: html,
        showDenyButton: true,
        denyButtonText: 'Anulare',
        confirmButtonText: 'Modificare',
        focusConfirm: false,
        didOpen: () => {
            $('#swal2-html-container').css({
                'display': 'flex',
                'flex-direction': 'column',
                'align-items': 'center'
            });
            $('#swal2-html-container').find('span.coloana').each(function () {
                $(this).css({'text-transform': 'capitalize'});
            });
            $('div.swal2-actions > button.swal2-confirm').removeClass('swal2-confirm').addClass('btn btn-ghost-dark btn-lg butonEdit');
        },
        preConfirm: () => {
            return divs.map(function () {
                return {
                    input: $(this).find('input').val(),
                    select: $(this).find('select').val()
                };
            }).get();
        }
    }).then((result) => {
        if (result.isConfirmed) {
            sendEditData(obj);
        }
    });
}


$(document).ready(function () {

    $('tr.carte_telefon, tr.z_structuri, tr.z_ordonatori').on({
        mouseenter: function () {
            $(this).fadeTo(200, 0.35);
            $(this).css('cursor', 'pointer');
        },
        mouseleave: function () {
            $(this).fadeTo(200, 1);
            $(this).css('cursor', 'default');
        }
    }, 'td');

    $(document).off('click', 'td').on('click', 'td', function (e) {
        e.preventDefault();
        e.stopImmediatePropagation();
        var dis = $(this),
            td = dis.closest('td'),
            tdInput = td.find('input.ascuns'),
            tdSelect = td.find('select.ascuns');

        if (td.find('input[class="editStructura"]').length) {
            editStructura(dis);
            return;
        }
        if (td.find('input.ascuns').length || td.find('select.ascuns').length) {
            swalInput(dis);
            return;
        }


        // tdInput.each(function () {
        //     $(this).show().removeClass('ascuns').addClass('afisat');
        // });
        // tdSelect.each(function () {
        //     $(this).addClass('afisat').select2({width: '100%'}).select2('open');
        // });
        // if (
        //     (tdInput.length || tdSelect.length)
        //     && td.find('button.butonEdit').length === 0
        // ) {
        //     td.find('div > input').focus().select();
        //     td.append('<button class="butonEdit btn btn-dark">OK</button>');
        // }
    });

    $(document).off('click', 'button.butonEdit').on('click', 'button.butonEdit', function () {
        sendEditData($(this));
    });

    $(document).off('keypress', 'input.afisat').on('keypress', 'input.afisat', function (e) {
        (e.which == 13) ? sendEditData($(this)) : null;
    });

    $(document).off('keydown').on('keydown', function (e) {
        if (e.key == "Escape") {
            hide();
        }
    });


    $(document).off('change', '.selectSchimbaStructura').on('change', '.selectSchimbaStructura', function () {
        var dis = $(this),
            idPersoana = dis.attr('idPersoana'),
            nouaStructura = dis.find('option:selected');

        formData = new FormData();
        formData.append('changeStructuraPersoana', true);
        formData.append('idPersoana', idPersoana);
        formData.append('nouaStructuraID', nouaStructura.val());
        $.ajax({
            method: 'post',
            processData: false,
            contentType: false,
            cache: false,
            data: formData,
            enctype: 'multipart/form-data',
            url: 'controller/carte-telefon.php',
            success: function (data, status, jqXHR) {
                Swal.fire({
                    icon: 'success',
                    title: 'Persoană <b>mutată în</b> ' + nouaStructura.text() + '!',
                    showConfirmButton: true
                }).then((result) => {
                    dis.closest('tr').hide();
                    $('tr[tabel="carte_telefon"][pk="id_carte_telefon"][id_row="' + idPersoana + '"]').hide();
                });
            },
            error: function (r) {
                Swal.fire({
                    icon: "error",
                    title: "Oops...",
                    text: r.message,
                    // showConfirmButton: false,
                    footer: 'Ceva nu a mers bine!'
                });
            }
        })


    });

    $(document).off('click', '.persoanaDel').on('click', '.persoanaDel', function () {
        var dis = $(this),
            numePersoana = dis.attr('numePersoana'),
            idPersoana = dis.attr('idPersoana');

        formData = new FormData();
        formData.append('delPersoana', true);
        formData.append('idPersoana', idPersoana);

        Swal.fire({
            title: "Stergeti pe " + numePersoana + " din cartea de telefon, <b>permanent</b>?",
            text: "Actiune ireversibila",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Da, sterge!"
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    method: 'post',
                    processData: false,
                    contentType: false,
                    cache: false,
                    data: formData,
                    enctype: 'multipart/form-data',
                    url: 'controller/carte-telefon.php',
                    success: function (data, status, jqXHR) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Persoana stearsa!',
                            showConfirmButton: false,
                            timer: 650
                        }).then((result) => {
                            dis.closest('tr').hide();
                            // $('tr[tabel="carte_telefon"][pk="id_carte_telefon"][id_row="' + idPersoana + '"]').hide();
                            $('input[type="hidden"][tabel="carte_telefon"][pk="id_carte_telefon"][id_row="' + idPersoana + '"]').closest('tr').hide();
                        });
                    },
                    error: function (r) {
                        Swal.fire({
                            icon: "error",
                            title: "Oops...",
                            text: r.message,
                            // showConfirmButton: false,
                            footer: 'Ceva nu a mers bine!'
                        });
                    }
                });
            }
        });
    });

    $(document).off('click', '.persoanaAddForm').on('click', '.persoanaAddForm', function () {
        var dis = $(this),
            idStructura = dis.attr('idStructura');
        var formData = new FormData();
        formData.append('persoanaAddForm', true);
        formData.append('idStructura', idStructura);

        $.ajax({
            method: 'post',
            processData: false,
            contentType: false,
            cache: false,
            data: formData,
            enctype: 'multipart/form-data',
            url: 'controller/carte-telefon.php',
            success: function (data, status, jqXHR) {
                dis.closest('table').find('tbody').append(data.message);
                $(document).find(".modal-body").animate({scrollTop: $(".modal-body > table").height()}, 1000);
            },
            error: function (r) {
                Swal.fire({
                    icon: "error",
                    title: "Oops...",
                    text: r.message,
                    // showConfirmButton: false,
                    footer: 'Ceva nu a mers bine!'
                });
            }
        });

    });

    $(document).off('click', '.persoanaAddBtn').on('click', '.persoanaAddBtn', function () {
        var dis = $(this),
            tr = dis.closest('tr'),
            nume = tr.find('input.nume').val(),
            etaj = tr.find('input.etaj').val(),
            camera = tr.find('input.camera').val(),
            functie = tr.find('select.functie').val(),
            interior = tr.find('input.interior').val(),
            mobil = tr.find('input.mobil').val(),
            fax = tr.find('input.fax').val(),
            email = tr.find('input.email').val(),
            obs = tr.find('input.obs').val(),
            idStructura = dis.attr('idStructura'),
            formData = new FormData();

        formData.append('persoanaAddBtn', true);
        formData.append('idStructura', idStructura);
        formData.append('nume', nume);
        formData.append('etaj', etaj);
        formData.append('camera', camera);
        formData.append('functie', functie);
        formData.append('interior', interior);
        formData.append('mobil', mobil);
        formData.append('fax', fax);
        formData.append('email', email);
        formData.append('obs', obs);

        $.ajax({
            method: 'post',
            processData: false,
            contentType: false,
            cache: false,
            data: formData,
            enctype: 'multipart/form-data',
            url: 'controller/carte-telefon.php',
            success: function (data, status, jqXHR) {
                if (data.status == 'ok') {
                    tr.hide().after('<tr><td>' + nume + '</td><td colspan="7">' + $('#modalFullscreen').find('.modal-title').text() + '</td><td>OK</td></tr>');
                } else {
                    Swal.fire({
                        icon: "error",
                        text: data.message,
                        // showConfirmButton: false,
                    });
                }
            },
            error: function (r) {
                Swal.fire({
                    icon: "error",
                    title: "Oops...",
                    text: r.message,
                    // showConfirmButton: false,
                    footer: 'Ceva nu a mers bine!'
                });
            }
        });

    });


    $(document).off('click', 'button[data-bs-dismiss="modal"]').on('click', 'button[data-bs-dismiss="modal"]', function () {
        $('#modalFullscreen').modal('toggle');
    });


});
