/* Custom styles for dtiResetPw.php */

/* Form styling */
.reset-form {
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    background-color: #fff;
    margin-bottom: 2rem;
}

.reset-form .form-group {
    margin-bottom: 1.5rem;
}

.reset-form label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    display: block;
}

.reset-form .form-control {
    border-radius: 4px;
    padding: 0.5rem 0.75rem;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.reset-form .form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Radio button styling */
.reset-form .form-check {
    padding: 0.5rem;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.reset-form .form-check:hover {
    background-color: #f8f9fa;
}

.reset-form .form-check-input {
    margin-top: 0.3rem;
}

/* Button styling */
.btn-reset {
    padding: 0.5rem 1.5rem;
    border-radius: 4px;
    font-weight: 500;
    transition: all 0.3s ease;
    margin-right: 0.5rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-reset:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-reset:active {
    transform: translateY(0);
}

.btn-reset img, .btn-reset svg {
    margin-left: 0.5rem;
}

/* Calendar styling */
.datepicker {
    padding: 0.5rem;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    animation: fadeIn 0.3s ease;
}

.datepicker table tr td.day:hover,
.datepicker table tr td.focused {
    background-color: #e9ecef;
    border-radius: 50%;
}

.datepicker table tr td.active {
    border-radius: 50%;
    background-color: #4f5d73 !important;
}

.datepicker .datepicker-switch:hover,
.datepicker .prev:hover,
.datepicker .next:hover {
    background-color: #f8f9fa;
    border-radius: 4px;
}

/* Calendar icon styling */
.calendar-icon {
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.calendar-icon:hover {
    background-color: #f8f9fa;
    transform: scale(1.1);
}

/* Date display styling */
#dataLucrare {
    padding: 0.375rem 0.75rem;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #ced4da;
    min-width: 100px;
    display: inline-block;
    margin: 0 0.5rem;
}

/* Validation styling */
.is-valid {
    border-color: #28a745 !important;
    padding-right: calc(1.5em + 0.75rem) !important;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e") !important;
    background-repeat: no-repeat !important;
    background-position: right calc(0.375em + 0.1875rem) center !important;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem) !important;
}

.is-invalid {
    border-color: #dc3545 !important;
    padding-right: calc(1.5em + 0.75rem) !important;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23dc3545' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e") !important;
    background-repeat: no-repeat !important;
    background-position: right calc(0.375em + 0.1875rem) center !important;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem) !important;
}

.invalid-feedback {
    display: none;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 80%;
    color: #dc3545;
}

.was-validated .form-control:invalid ~ .invalid-feedback,
.form-control.is-invalid ~ .invalid-feedback {
    display: block;
}

/* Tooltip styling */
.tooltip-icon {
    color: #6c757d;
    margin-left: 0.25rem;
    cursor: pointer;
}

/* Animation effects */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.animate-pulse {
    animation: pulse 1.5s infinite;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .reset-form {
        padding: 1rem;
    }
    
    .form-row {
        flex-direction: column;
    }
    
    .form-group {
        margin-bottom: 1rem;
    }
    
    .btn-reset {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

/* Statistics table styling */
.stats-table {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow: hidden;
}

.stats-table thead {
    background-color: #4f5d73;
    color: white;
}

.stats-table tbody tr {
    transition: all 0.3s ease;
}

.stats-table tbody tr:hover {
    background-color: rgba(79, 93, 115, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Card styling */
.card {
    border: none;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    font-weight: 600;
}

/* Help text styling */
.help-text {
    color: #6c757d;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Form section styling */
.form-section {
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-section:last-child {
    border-bottom: none;
    padding-bottom: 0;
    margin-bottom: 0;
}

/* Form section title */
.form-section-title {
    font-weight: 600;
    margin-bottom: 1rem;
    color: #4f5d73;
}
