<?php
session_start();

// Inițializare date în sesiune dacă nu există
if (!isset($_SESSION['events'])) {
    $_SESSION['events'] = [];
}

if (!isset($_SESSION['current_event'])) {
    $_SESSION['current_event'] = null;
}

// Procesare formulare
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    switch ($action) {
        case 'create_event':
            $event_id = uniqid();
            $_SESSION['events'][$event_id] = [
                'name' => $_POST['event_name'],
                'type' => $_POST['event_type'],
                'date' => $_POST['event_date'],
                'location' => $_POST['location'],
                'floors' => [
                    'parter' => ['name' => 'Parter', 'tables' => [], 'elements' => []],
                    'etaj1' => ['name' => 'Etaj 1', 'tables' => [], 'elements' => []],
                    'balcon' => ['name' => 'Balcon', 'tables' => [], 'elements' => []]
                ]
            ];
            $_SESSION['current_event'] = $event_id;
            break;

        case 'select_event':
            $_SESSION['current_event'] = $_POST['event_id'];
            break;

        case 'add_table':
            if ($_SESSION['current_event']) {
                $table_id = uniqid();
                $floor = $_POST['floor'];

                // Calculează poziția automată pentru a evita suprapunerea
                $position = calculateNextPosition($floor, 'table');

                $_SESSION['events'][$_SESSION['current_event']]['floors'][$floor]['tables'][$table_id] = [
                    'number' => $_POST['table_number'],
                    'custom_name' => $_POST['table_custom_name'] ?? '',
                    'shape' => $_POST['table_shape'],
                    'capacity' => (int)$_POST['table_capacity'],
                    'guests' => array_fill(0, (int)$_POST['table_capacity'], ''),
                    'notes' => '',
                    'x' => $position['x'],
                    'y' => $position['y']
                ];
            }
            break;

        case 'add_element':
            if ($_SESSION['current_event']) {
                $element_id = uniqid();
                $floor = $_POST['floor'];

                // Calculează poziția automată pentru a evita suprapunerea
                $position = calculateNextPosition($floor, 'element');

                $_SESSION['events'][$_SESSION['current_event']]['floors'][$floor]['elements'][$element_id] = [
                    'type' => $_POST['element_type'],
                    'name' => $_POST['element_name'] ?? '',
                    'x' => $position['x'],
                    'y' => $position['y']
                ];
            }
            break;

        case 'update_guests':
            if ($_SESSION['current_event']) {
                $table_id = $_POST['table_id'];
                $floor = $_POST['floor'];
                $guests = $_POST['guests'] ?? [];
                $_SESSION['events'][$_SESSION['current_event']]['floors'][$floor]['tables'][$table_id]['guests'] = $guests;
                $_SESSION['events'][$_SESSION['current_event']]['floors'][$floor]['tables'][$table_id]['notes'] = $_POST['notes'] ?? '';
                $_SESSION['events'][$_SESSION['current_event']]['floors'][$floor]['tables'][$table_id]['custom_name'] = $_POST['table_custom_name'] ?? '';
            }
            break;

        case 'delete_table':
            if ($_SESSION['current_event']) {
                $table_id = $_POST['table_id'];
                $floor = $_POST['floor'];
                unset($_SESSION['events'][$_SESSION['current_event']]['floors'][$floor]['tables'][$table_id]);
            }
            break;

        case 'delete_element':
            if ($_SESSION['current_event']) {
                $element_id = $_POST['element_id'];
                $floor = $_POST['floor'];
                unset($_SESSION['events'][$_SESSION['current_event']]['floors'][$floor]['elements'][$element_id]);
            }
            break;

        case 'update_position':
            if ($_SESSION['current_event']) {
                $item_id = $_POST['item_id'];
                $floor = $_POST['floor'];
                $item_type = $_POST['item_type'];
                $x = (int)$_POST['x'];
                $y = (int)$_POST['y'];

                if ($item_type === 'table') {
                    $_SESSION['events'][$_SESSION['current_event']]['floors'][$floor]['tables'][$item_id]['x'] = $x;
                    $_SESSION['events'][$_SESSION['current_event']]['floors'][$floor]['tables'][$item_id]['y'] = $y;
                } else {
                    // Asigură-te că există cheia 'elements'
                    if (!isset($_SESSION['events'][$_SESSION['current_event']]['floors'][$floor]['elements'])) {
                        $_SESSION['events'][$_SESSION['current_event']]['floors'][$floor]['elements'] = [];
                    }
                    $_SESSION['events'][$_SESSION['current_event']]['floors'][$floor]['elements'][$item_id]['x'] = $x;
                    $_SESSION['events'][$_SESSION['current_event']]['floors'][$floor]['elements'][$item_id]['y'] = $y;
                }
            }
            break;

        case 'update_element':
            if ($_SESSION['current_event']) {
                $element_id = $_POST['element_id'];
                $floor = $_POST['floor'];

                // Asigură-te că există cheia 'elements'
                if (!isset($_SESSION['events'][$_SESSION['current_event']]['floors'][$floor]['elements'])) {
                    $_SESSION['events'][$_SESSION['current_event']]['floors'][$floor]['elements'] = [];
                }

                $_SESSION['events'][$_SESSION['current_event']]['floors'][$floor]['elements'][$element_id]['type'] = $_POST['element_type'];
                $_SESSION['events'][$_SESSION['current_event']]['floors'][$floor]['elements'][$element_id]['name'] = $_POST['element_name'] ?? '';
            }
            break;
    }
}

// Funcție pentru calcularea poziției următoare pentru a evita suprapunerea
function calculateNextPosition($floor, $type) {
    if (!$_SESSION['current_event']) {
        return ['x' => 50, 'y' => 50];
    }

    $current_floor = $_SESSION['events'][$_SESSION['current_event']]['floors'][$floor];
    $occupied_positions = [];

    // Colectează toate pozițiile ocupate
    foreach ($current_floor['tables'] as $table) {
        $size = 60 + ($table['capacity'] * 3);
        $occupied_positions[] = [
            'x' => $table['x'],
            'y' => $table['y'],
            'width' => $size,
            'height' => $size
        ];
    }

    foreach ($current_floor['elements'] as $element) {
        $size = getElementSize($element['type']);
        $occupied_positions[] = [
            'x' => $element['x'],
            'y' => $element['y'],
            'width' => $size['width'],
            'height' => $size['height']
        ];
    }

    // Caută o poziție liberă cu precizie mai mare
    $grid_size = 20; // Distanța mai mică între elemente pentru precizie
    $margin = 10;

    for ($row = 0; $row < 30; $row++) {
        for ($col = 0; $col < 40; $col++) {
            $x = $margin + ($col * $grid_size);
            $y = $margin + ($row * $grid_size);

            $size = $type === 'table' ? 80 : getElementSize('default');
            $width = is_array($size) ? $size['width'] : $size;
            $height = is_array($size) ? $size['height'] : $size;

            $collision = false;
            foreach ($occupied_positions as $pos) {
                if ($x < $pos['x'] + $pos['width'] + 10 &&
                    $x + $width + 10 > $pos['x'] &&
                    $y < $pos['y'] + $pos['height'] + 10 &&
                    $y + $height + 10 > $pos['y']) {
                    $collision = true;
                    break;
                }
            }

            if (!$collision) {
                return ['x' => $x, 'y' => $y];
            }
        }
    }

    // Dacă nu găsește o poziție liberă, returnează o poziție aleatorie
    return ['x' => rand(50, 300), 'y' => rand(50, 200)];
}

// Funcție pentru a obține dimensiunea elementelor
function getElementSize($type) {
    $sizes = [
        'masa_miri' => ['width' => 120, 'height' => 80],
        'scena' => ['width' => 150, 'height' => 100],
        'cabina_foto' => ['width' => 80, 'height' => 80],
        'masa_foto_video' => ['width' => 100, 'height' => 60],
        'candy_bar' => ['width' => 120, 'height' => 60],
        'bar_bauturi' => ['width' => 140, 'height' => 60],
        'altul' => ['width' => 80, 'height' => 60],
        'default' => ['width' => 80, 'height' => 60]
    ];

    return $sizes[$type] ?? $sizes['default'];
}

// Asigură-te că toate evenimentele au cheile necesare pentru compatibilitate
if ($_SESSION['current_event'] && isset($_SESSION['events'][$_SESSION['current_event']])) {
    foreach ($_SESSION['events'][$_SESSION['current_event']]['floors'] as $floor_id => &$floor) {
        if (!isset($floor['elements'])) {
            $floor['elements'] = [];
        }

        // Adaugă custom_name pentru mesele existente care nu au acest câmp
        foreach ($floor['tables'] as $table_id => &$table) {
            if (!isset($table['custom_name'])) {
                $table['custom_name'] = '';
            }
        }
        unset($table); // Eliberează referința
    }
    unset($floor); // Eliberează referința
}

$current_event = $_SESSION['current_event'] ? $_SESSION['events'][$_SESSION['current_event']] : null;
?>

<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Planificator Mese Evenimente</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .header h1 {
            color: #667eea;
            margin-bottom: 10px;
            font-size: 2.5em;
        }

        .event-selector {
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn-secondary {
            background: linear-gradient(45deg, #f093fb, #f5576c);
        }

        .btn-danger {
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
        }

        .card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }

        .card-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            font-size: 1.2em;
            font-weight: bold;
        }

        .card-body {
            padding: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #555;
        }

        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e1e1;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
        }

        .tabs {
            display: flex;
            background: #f8f9fa;
            border-radius: 15px 15px 0 0;
            overflow: hidden;
        }

        .tab {
            flex: 1;
            padding: 15px 20px;
            text-align: center;
            cursor: pointer;
            background: #e9ecef;
            border: none;
            transition: all 0.3s ease;
            font-weight: bold;
        }

        .tab.active {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .tab-content {
            display: none;
            padding: 30px;
            background: white;
            border-radius: 0 0 15px 15px;
        }

        .tab-content.active {
            display: block;
        }

        .floor-layout {
            position: relative;
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 15px;
            min-height: 400px;
            margin-bottom: 30px;
        }

        .table-item {
            position: absolute;
            background: white;
            border: 3px solid #667eea;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: move;
            font-weight: bold;
            color: #667eea;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .table-item:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .table-square {
            border-radius: 15px;
        }

        .table-rectangle {
            border-radius: 25px;
        }

        .element-item {
            position: absolute;
            background: white;
            border: 3px solid #f093fb;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: move;
            font-weight: bold;
            color: #f093fb;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
            font-size: 12px;
            padding: 5px;
        }

        .element-item:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .element-masa-miri {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            border-color: #ffd700;
            color: #8b6914;
        }

        .element-scena {
            background: linear-gradient(45deg, #9c27b0, #e91e63);
            border-color: #9c27b0;
            color: white;
        }

        .element-cabina-foto {
            background: linear-gradient(45deg, #00bcd4, #4fc3f7);
            border-color: #00bcd4;
            color: white;
        }

        .element-masa-foto-video {
            background: linear-gradient(45deg, #ff9800, #ffb74d);
            border-color: #ff9800;
            color: white;
        }

        .element-candy-bar {
            background: linear-gradient(45deg, #e91e63, #f48fb1);
            border-color: #e91e63;
            color: white;
        }

        .element-bar-bauturi {
            background: linear-gradient(45deg, #3f51b5, #7986cb);
            border-color: #3f51b5;
            color: white;
        }

        .element-altul {
            background: linear-gradient(45deg, #607d8b, #90a4ae);
            border-color: #607d8b;
            color: white;
        }

        .guest-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .guest-input {
            padding: 8px 12px;
            border: 2px solid #e1e1e1;
            border-radius: 8px;
            transition: border-color 0.3s ease;
        }

        .guest-input:focus {
            outline: none;
            border-color: #667eea;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
        }

        .stat-label {
            color: #666;
            margin-top: 5px;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #000;
        }

        .row {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        .col {
            flex: 1;
            min-width: 200px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .event-selector {
                flex-direction: column;
                align-items: stretch;
            }

            .tabs {
                flex-direction: column;
            }

            .stats {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 Planificator Mese Evenimente</h1>
            <div class="event-selector">
                <?php if (empty($_SESSION['events'])): ?>
                    <p>Nu există evenimente create. Creează primul eveniment!</p>
                <?php else: ?>
                    <form method="POST" style="display: inline;">
                        <select name="event_id" class="form-control" style="width: auto; display: inline-block;" onchange="this.form.submit()">
                            <option value="">Selectează eveniment</option>
                            <?php foreach ($_SESSION['events'] as $id => $event): ?>
                                <option value="<?= $id ?>" <?= $_SESSION['current_event'] === $id ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($event['name']) ?> (<?= $event['date'] ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <input type="hidden" name="action" value="select_event">
                    </form>
                <?php endif; ?>
                <button class="btn" onclick="showCreateEventModal()">➕ Eveniment Nou</button>
            </div>
        </div>

        <?php if ($current_event): ?>
            <div class="stats">
                <?php
                $total_tables = 0;
                $total_guests = 0;
                $occupied_seats = 0;

                foreach ($current_event['floors'] as $floor) {
                    $total_tables += count($floor['tables']);
                    foreach ($floor['tables'] as $table) {
                        $total_guests += $table['capacity'];
                        $occupied_seats += count(array_filter($table['guests']));
                    }
                }
                ?>
                <div class="stat-card">
                    <div class="stat-number"><?= $total_tables ?></div>
                    <div class="stat-label">Mese Total</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?= $total_guests ?></div>
                    <div class="stat-label">Locuri Total</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?= $occupied_seats ?></div>
                    <div class="stat-label">Locuri Ocupate</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?= $total_guests - $occupied_seats ?></div>
                    <div class="stat-label">Locuri Libere</div>
                </div>
            </div>

            <div class="card">
                <div class="tabs">
                    <button class="tab active" onclick="showTab('parter')">🏢 Parter</button>
                    <button class="tab" onclick="showTab('etaj1')">🏗️ Etaj 1</button>
                    <button class="tab" onclick="showTab('balcon')">🌅 Balcon</button>
                </div>

                <?php foreach ($current_event['floors'] as $floor_id => $floor): ?>
                <div id="<?= $floor_id ?>" class="tab-content <?= $floor_id === 'parter' ? 'active' : '' ?>">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; flex-wrap: wrap; gap: 10px;">
                        <h3><?= $floor['name'] ?></h3>
                        <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                            <button class="btn" onclick="showAddTableModal('<?= $floor_id ?>')">🪑 Adaugă Masă</button>
                            <button class="btn btn-secondary" onclick="showAddElementModal('<?= $floor_id ?>')">✨ Adaugă Element</button>
                        </div>
                    </div>

                    <div class="floor-layout" id="layout-<?= $floor_id ?>">
                        <?php foreach ($floor['tables'] as $table_id => $table): ?>
                            <div class="table-item table-<?= $table['shape'] ?>"
                                 style="left: <?= $table['x'] ?>px; top: <?= $table['y'] ?>px;
                                        width: <?= 60 + ($table['capacity'] * 3) ?>px;
                                        height: <?= 60 + ($table['capacity'] * 3) ?>px;"
                                 onclick="showTableModal('<?= $table_id ?>', '<?= $floor_id ?>')"
                                 draggable="true"
                                 data-item-id="<?= $table_id ?>"
                                 data-item-type="table"
                                 data-floor="<?= $floor_id ?>">
                                <div>
                                    <?php if (!empty($table['custom_name'])): ?>
                                        <div style="font-size: 11px; font-weight: bold;"><?= htmlspecialchars($table['custom_name']) ?></div>
                                        <div style="font-size: 10px;">Masa <?= $table['number'] ?></div>
                                    <?php else: ?>
                                        <div style="font-size: 14px;">Masa <?= $table['number'] ?></div>
                                    <?php endif; ?>
                                    <div style="font-size: 10px;"><?= count(array_filter($table['guests'])) ?>/<?= $table['capacity'] ?></div>
                                </div>
                            </div>
                        <?php endforeach; ?>

                        <?php
                        // Verifică dacă există cheia 'elements' pentru compatibilitate cu evenimente vechi
                        $elements = isset($floor['elements']) ? $floor['elements'] : [];
                        foreach ($elements as $element_id => $element):
                            $size = getElementSize($element['type']);
                            $element_names = [
                                'masa_miri' => '👰🤵 Masa Mirilor',
                                'scena' => '🎤 Scenă',
                                'cabina_foto' => '📸 Cabina Foto',
                                'masa_foto_video' => '🎥 Masa Foto-Video',
                                'candy_bar' => '🍭 Candy Bar',
                                'bar_bauturi' => '🍸 Bar Băuturi',
                                'altul' => $element['name'] ?: '📦 Element'
                            ];
                        ?>
                            <div class="element-item element-<?= str_replace('_', '-', $element['type']) ?>"
                                 style="left: <?= $element['x'] ?>px; top: <?= $element['y'] ?>px;
                                        width: <?= $size['width'] ?>px;
                                        height: <?= $size['height'] ?>px;"
                                 onclick="showElementModal('<?= $element_id ?>', '<?= $floor_id ?>')"
                                 draggable="true"
                                 data-item-id="<?= $element_id ?>"
                                 data-item-type="element"
                                 data-floor="<?= $floor_id ?>">
                                <div><?= $element_names[$element['type']] ?></div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <div class="card">
                <div class="card-body" style="text-align: center; padding: 60px;">
                    <h2>Bine ai venit! 🎊</h2>
                    <p style="margin: 20px 0; font-size: 1.1em; color: #666;">
                        Creează primul tău eveniment pentru a începe să planifici aranjarea meselor.
                    </p>
                    <button class="btn" onclick="showCreateEventModal()">🎉 Creează Primul Eveniment</button>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Modal Creare Eveniment -->
    <div id="createEventModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="hideModal('createEventModal')">&times;</span>
            <h2>📅 Eveniment Nou</h2>
            <form method="POST">
                <input type="hidden" name="action" value="create_event">
                <div class="row">
                    <div class="col">
                        <div class="form-group">
                            <label>Nume Eveniment</label>
                            <input type="text" name="event_name" class="form-control" placeholder="ex: Nunta Maria & Ion" required>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label>Tip Eveniment</label>
                            <select name="event_type" class="form-control" required>
                                <option value="nunta">💒 Nuntă</option>
                                <option value="botez">👶 Botez</option>
                                <option value="aniversare">🎂 Aniversare</option>
                                <option value="corporate">🏢 Corporate</option>
                                <option value="altul">🎪 Altul</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <div class="form-group">
                            <label>Data Eveniment</label>
                            <input type="date" name="event_date" class="form-control" required>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label>Locația</label>
                            <input type="text" name="location" class="form-control" placeholder="ex: Restaurant Royal" required>
                        </div>
                    </div>
                </div>
                <button type="submit" class="btn" style="width: 100%; margin-top: 20px;">✨ Creează Eveniment</button>
            </form>
        </div>
    </div>

    <!-- Modal Adaugare Masa -->
    <div id="addTableModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="hideModal('addTableModal')">&times;</span>
            <h2>🪑 Masă Nouă</h2>
            <form method="POST">
                <input type="hidden" name="action" value="add_table">
                <input type="hidden" name="floor" id="modal-floor">
                <input type="hidden" name="x_position" value="50">
                <input type="hidden" name="y_position" value="50">

                <div class="row">
                    <div class="col">
                        <div class="form-group">
                            <label>Numărul Mesei</label>
                            <input type="number" name="table_number" class="form-control" min="1" required>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label>Forma Mesei</label>
                            <select name="table_shape" class="form-control" required>
                                <option value="round">⭕ Rotundă</option>
                                <option value="square">⬜ Pătrată</option>
                                <option value="rectangle">▭ Dreptunghiulară</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label>Nume Personalizat (opțional)</label>
                    <input type="text" name="table_custom_name" class="form-control" placeholder="ex: Masa mirilor, Masa socri, Masa prieteni, etc.">
                    <small style="color: #666; font-size: 12px;">Lasă gol pentru a afișa doar numărul mesei</small>
                </div>
                <div class="form-group">
                    <label>Numărul de Persoane</label>
                    <select name="table_capacity" class="form-control" required>
                        <?php for ($i = 4; $i <= 16; $i++): ?>
                            <option value="<?= $i ?>" <?= $i === 8 ? 'selected' : '' ?>><?= $i ?> persoane</option>
                        <?php endfor; ?>
                    </select>
                </div>
                <button type="submit" class="btn" style="width: 100%; margin-top: 20px;">➕ Adaugă Masa</button>
            </form>
        </div>
    </div>

    <!-- Modal Editare Masa -->
    <div id="tableModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="hideModal('tableModal')">&times;</span>
            <h2 id="table-modal-title">✏️ Editează Masa</h2>
            <form method="POST">
                <input type="hidden" name="action" value="update_guests">
                <input type="hidden" name="table_id" id="edit-table-id">
                <input type="hidden" name="floor" id="edit-floor">

                <div class="form-group">
                    <label>Nume Personalizat (opțional)</label>
                    <input type="text" name="table_custom_name" id="table-custom-name" class="form-control" placeholder="ex: Masa mirilor, Masa socri, Masa prieteni, etc.">
                    <small style="color: #666; font-size: 12px;">Lasă gol pentru a afișa doar numărul mesei</small>
                </div>

                <div class="form-group">
                    <label>Invitați</label>
                    <div id="guests-container" class="guest-list"></div>
                </div>

                <div class="form-group">
                    <label>Notițe (meniu special, alergii, etc.)</label>
                    <textarea name="notes" id="table-notes" class="form-control" rows="3" placeholder="ex: Meniu vegetarian pentru Maria, Ion nu mănâncă fructe de mare..."></textarea>
                </div>

                <div style="display: flex; gap: 10px; margin-top: 20px;">
                    <button type="submit" class="btn" style="flex: 1;">💾 Salvează</button>
                    <button type="button" class="btn btn-danger" onclick="deleteTable()" style="flex: 1;">🗑️ Șterge Masa</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Modal Adaugare Element -->
    <div id="addElementModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="hideModal('addElementModal')">&times;</span>
            <h2>✨ Element Nou</h2>
            <form method="POST">
                <input type="hidden" name="action" value="add_element">
                <input type="hidden" name="floor" id="element-modal-floor">

                <div class="form-group">
                    <label>Tipul Elementului</label>
                    <select name="element_type" id="element-type" class="form-control" required onchange="toggleCustomName()">
                        <option value="masa_miri">👰🤵 Masa Mirilor</option>
                        <option value="scena">🎤 Scenă Formație</option>
                        <option value="cabina_foto">📸 Cabina Foto</option>
                        <option value="masa_foto_video">🎥 Masa Foto-Video</option>
                        <option value="candy_bar">🍭 Candy Bar</option>
                        <option value="bar_bauturi">🍸 Bar Băuturi</option>
                        <option value="altul">📦 Altul (personalizat)</option>
                    </select>
                </div>

                <div class="form-group" id="custom-name-group" style="display: none;">
                    <label>Nume Personalizat</label>
                    <input type="text" name="element_name" id="element-name" class="form-control" placeholder="ex: Masa cadouri, DJ booth, etc.">
                </div>

                <button type="submit" class="btn" style="width: 100%; margin-top: 20px;">✨ Adaugă Element</button>
            </form>
        </div>
    </div>

    <!-- Modal Editare Element -->
    <div id="elementModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="hideModal('elementModal')">&times;</span>
            <h2 id="element-modal-title">✏️ Editează Element</h2>
            <form method="POST">
                <input type="hidden" name="action" value="update_element">
                <input type="hidden" name="element_id" id="edit-element-id">
                <input type="hidden" name="floor" id="edit-element-floor">

                <div class="form-group">
                    <label>Tipul Elementului</label>
                    <select name="element_type" id="edit-element-type" class="form-control" required onchange="toggleEditCustomName()">
                        <option value="masa_miri">👰🤵 Masa Mirilor</option>
                        <option value="scena">🎤 Scenă Formație</option>
                        <option value="cabina_foto">📸 Cabina Foto</option>
                        <option value="masa_foto_video">🎥 Masa Foto-Video</option>
                        <option value="candy_bar">🍭 Candy Bar</option>
                        <option value="bar_bauturi">🍸 Bar Băuturi</option>
                        <option value="altul">📦 Altul (personalizat)</option>
                    </select>
                </div>

                <div class="form-group" id="edit-custom-name-group" style="display: none;">
                    <label>Nume Personalizat</label>
                    <input type="text" name="element_name" id="edit-element-name" class="form-control" placeholder="ex: Masa cadouri, DJ booth, etc.">
                </div>

                <div style="display: flex; gap: 10px; margin-top: 20px;">
                    <button type="submit" class="btn" style="flex: 1;">💾 Salvează</button>
                    <button type="button" class="btn btn-danger" onclick="deleteElement()" style="flex: 1;">🗑️ Șterge Element</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        let currentTableId = '';
        let currentFloor = '';
        let currentElementId = '';
        let draggedItem = null;

        function showTab(tabId) {
            // Ascunde toate tab-urile
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Afișează tab-ul selectat
            document.getElementById(tabId).classList.add('active');
            event.target.classList.add('active');
        }

        function showCreateEventModal() {
            document.getElementById('createEventModal').style.display = 'block';
        }

        function showAddTableModal(floor) {
            document.getElementById('modal-floor').value = floor;
            document.getElementById('addTableModal').style.display = 'block';
        }

        function showAddElementModal(floor) {
            document.getElementById('element-modal-floor').value = floor;
            document.getElementById('addElementModal').style.display = 'block';
        }

        function toggleCustomName() {
            const elementType = document.getElementById('element-type').value;
            const customNameGroup = document.getElementById('custom-name-group');

            if (elementType === 'altul') {
                customNameGroup.style.display = 'block';
                document.getElementById('element-name').required = true;
            } else {
                customNameGroup.style.display = 'none';
                document.getElementById('element-name').required = false;
            }
        }

        function toggleEditCustomName() {
            const elementType = document.getElementById('edit-element-type').value;
            const customNameGroup = document.getElementById('edit-custom-name-group');

            if (elementType === 'altul') {
                customNameGroup.style.display = 'block';
                document.getElementById('edit-element-name').required = true;
            } else {
                customNameGroup.style.display = 'none';
                document.getElementById('edit-element-name').required = false;
            }
        }

        function showTableModal(tableId, floor) {
            currentTableId = tableId;
            currentFloor = floor;

            // Încarcă datele mesei
            fetch(`get_table_data.php?table_id=${tableId}&floor=${floor}`)
                .then(response => response.json())
                .then(data => {
                    document.getElementById('edit-table-id').value = tableId;
                    document.getElementById('edit-floor').value = floor;
                    document.getElementById('table-modal-title').textContent = `✏️ Masa ${data.number}`;
                    document.getElementById('table-notes').value = data.notes || '';
                    document.getElementById('table-custom-name').value = data.custom_name || '';

                    // Generează câmpurile pentru invitați
                    const container = document.getElementById('guests-container');
                    container.innerHTML = '';

                    for (let i = 0; i < data.capacity; i++) {
                        const input = document.createElement('input');
                        input.type = 'text';
                        input.name = `guests[${i}]`;
                        input.value = data.guests[i] || '';
                        input.placeholder = `Locul ${i + 1}`;
                        input.className = 'guest-input';
                        container.appendChild(input);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    // Fallback pentru când nu există fișierul PHP separat
                    document.getElementById('edit-table-id').value = tableId;
                    document.getElementById('edit-floor').value = floor;
                    document.getElementById('table-custom-name').value = '';
                });

            document.getElementById('tableModal').style.display = 'block';
        }

        function showElementModal(elementId, floor) {
            currentElementId = elementId;
            currentFloor = floor;

            // Aici ar trebui să încarci datele elementului din sesiune
            // Pentru simplitate, setez doar ID-urile
            document.getElementById('edit-element-id').value = elementId;
            document.getElementById('edit-element-floor').value = floor;
            document.getElementById('element-modal-title').textContent = `✏️ Editează Element`;

            document.getElementById('elementModal').style.display = 'block';
        }

        function hideModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        function deleteTable() {
            if (confirm('Ești sigur că vrei să ștergi această masă?')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete_table">
                    <input type="hidden" name="table_id" value="${currentTableId}">
                    <input type="hidden" name="floor" value="${currentFloor}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        function deleteElement() {
            if (confirm('Ești sigur că vrei să ștergi acest element?')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete_element">
                    <input type="hidden" name="element_id" value="${currentElementId}">
                    <input type="hidden" name="floor" value="${currentFloor}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        // Închide modalele când se dă click în afara lor
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }

        // Drag and drop complet funcțional
        document.addEventListener('DOMContentLoaded', function() {
            initializeDragAndDrop();
        });

        // Reinitializează drag & drop după adăugarea de noi elemente
        function reinitializeDragAndDrop() {
            // Înlătură event listeners existente pentru a evita duplicarea
            const draggableItems = document.querySelectorAll('.table-item, .element-item');
            draggableItems.forEach(item => {
                item.removeEventListener('dragstart', handleDragStart);
                item.removeEventListener('dragend', handleDragEnd);
            });

            // Reinitializează
            initializeDragAndDrop();
        }

        function initializeDragAndDrop() {
            // Adaugă event listeners pentru toate elementele draggable
            const draggableItems = document.querySelectorAll('.table-item, .element-item');
            const floorLayouts = document.querySelectorAll('.floor-layout');

            draggableItems.forEach(item => {
                item.addEventListener('dragstart', handleDragStart);
                item.addEventListener('dragend', handleDragEnd);

                // Adaugă suport pentru touch events (mobile)
                item.addEventListener('touchstart', handleTouchStart, { passive: false });
                item.addEventListener('touchmove', handleTouchMove, { passive: false });
                item.addEventListener('touchend', handleTouchEnd, { passive: false });
            });

            floorLayouts.forEach(layout => {
                layout.addEventListener('dragover', handleDragOver);
                layout.addEventListener('drop', handleDrop);
            });
        }

        function handleDragStart(e) {
            draggedItem = this;
            this.style.opacity = '0.5';

            // Stochează informațiile despre elementul tras
            e.dataTransfer.setData('text/plain', JSON.stringify({
                itemId: this.dataset.itemId,
                itemType: this.dataset.itemType,
                floor: this.dataset.floor
            }));
        }

        function handleDragEnd(e) {
            this.style.opacity = '1';
            draggedItem = null;
        }

        function handleDragOver(e) {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'move';
        }

        function handleDrop(e) {
            e.preventDefault();

            if (!draggedItem) return;

            const rect = this.getBoundingClientRect();

            // Calculează poziția relativ la centrul elementului tras
            const itemRect = draggedItem.getBoundingClientRect();
            const offsetX = e.clientX - (itemRect.left + itemRect.width / 2);
            const offsetY = e.clientY - (itemRect.top + itemRect.height / 2);

            // Poziția nouă cu precizie de 1px
            let x = e.clientX - rect.left - (draggedItem.offsetWidth / 2);
            let y = e.clientY - rect.top - (draggedItem.offsetHeight / 2);

            // Asigură-te că elementul rămâne în limitele containerului cu margini de siguranță
            const margin = 5;
            const maxX = this.offsetWidth - draggedItem.offsetWidth - margin;
            const maxY = this.offsetHeight - draggedItem.offsetHeight - margin;

            const finalX = Math.max(margin, Math.min(x, maxX));
            const finalY = Math.max(margin, Math.min(y, maxY));

            // Actualizează poziția vizual cu precizie maximă
            draggedItem.style.left = Math.round(finalX) + 'px';
            draggedItem.style.top = Math.round(finalY) + 'px';

            // Trimite poziția nouă la server cu coordonate precise
            updateItemPosition(
                draggedItem.dataset.itemId,
                draggedItem.dataset.itemType,
                draggedItem.dataset.floor,
                Math.round(finalX),
                Math.round(finalY)
            );
        }

        function updateItemPosition(itemId, itemType, floor, x, y) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.style.display = 'none';
            form.innerHTML = `
                <input type="hidden" name="action" value="update_position">
                <input type="hidden" name="item_id" value="${itemId}">
                <input type="hidden" name="item_type" value="${itemType}">
                <input type="hidden" name="floor" value="${floor}">
                <input type="hidden" name="x" value="${Math.round(x)}">
                <input type="hidden" name="y" value="${Math.round(y)}">
            `;

            document.body.appendChild(form);
            form.submit();
        }

        // Touch events pentru dispozitive mobile
        let touchItem = null;
        let touchOffset = { x: 0, y: 0 };

        function handleTouchStart(e) {
            touchItem = this;
            this.style.opacity = '0.7';
            this.style.zIndex = '1000';

            const touch = e.touches[0];
            const rect = this.getBoundingClientRect();
            touchOffset.x = touch.clientX - rect.left;
            touchOffset.y = touch.clientY - rect.top;

            e.preventDefault();
        }

        function handleTouchMove(e) {
            if (!touchItem) return;

            const touch = e.touches[0];
            const containerRect = touchItem.closest('.floor-layout').getBoundingClientRect();

            // Calculează poziția nouă cu precizie maximă
            let x = touch.clientX - containerRect.left - touchOffset.x;
            let y = touch.clientY - containerRect.top - touchOffset.y;

            // Limitările containerului
            const margin = 5;
            const maxX = touchItem.closest('.floor-layout').offsetWidth - touchItem.offsetWidth - margin;
            const maxY = touchItem.closest('.floor-layout').offsetHeight - touchItem.offsetHeight - margin;

            x = Math.max(margin, Math.min(x, maxX));
            y = Math.max(margin, Math.min(y, maxY));

            // Actualizează poziția în timp real
            touchItem.style.left = Math.round(x) + 'px';
            touchItem.style.top = Math.round(y) + 'px';

            e.preventDefault();
        }

        function handleTouchEnd(e) {
            if (!touchItem) return;

            touchItem.style.opacity = '1';
            touchItem.style.zIndex = 'auto';

            // Salvează poziția finală
            const finalX = parseInt(touchItem.style.left);
            const finalY = parseInt(touchItem.style.top);

            updateItemPosition(
                touchItem.dataset.itemId,
                touchItem.dataset.itemType,
                touchItem.dataset.floor,
                finalX,
                finalY
            );

            touchItem = null;
            e.preventDefault();
        }
    </script>
</body>
</html>