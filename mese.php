<?php
session_start();

// Inițializare date în sesiune dacă nu există
if (!isset($_SESSION['events'])) {
    $_SESSION['events'] = [];
}

if (!isset($_SESSION['current_event'])) {
    $_SESSION['current_event'] = null;
}

// Procesare formulare
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    switch ($action) {
        case 'create_event':
            $event_id = uniqid();
            $_SESSION['events'][$event_id] = [
                'name' => $_POST['event_name'],
                'type' => $_POST['event_type'],
                'date' => $_POST['event_date'],
                'location' => $_POST['location'],
                'floors' => [
                    'parter' => ['name' => 'Parter', 'tables' => [], 'elements' => []],
                    'etaj1' => ['name' => 'Etaj 1', 'tables' => [], 'elements' => []],
                    'balcon' => ['name' => 'Balcon', 'tables' => [], 'elements' => []]
                ]
            ];
            $_SESSION['current_event'] = $event_id;
            break;

        case 'select_event':
            $_SESSION['current_event'] = $_POST['event_id'];
            break;

        case 'add_table':
            if ($_SESSION['current_event']) {
                $table_id = uniqid();
                $floor = $_POST['floor'];

                // Calculăm o poziție liberă pentru noua masă
                $position = findFreePosition($floor, 'table');

                $_SESSION['events'][$_SESSION['current_event']]['floors'][$floor]['tables'][$table_id] = [
                    'number' => $_POST['table_number'],
                    'shape' => $_POST['table_shape'],
                    'capacity' => (int)$_POST['table_capacity'],
                    'guests' => array_fill(0, (int)$_POST['table_capacity'], ''),
                    'notes' => '',
                    'x' => $position['x'],
                    'y' => $position['y']
                ];
            }
            break;

        case 'add_element':
            if ($_SESSION['current_event']) {
                $element_id = uniqid();
                $floor = $_POST['floor'];

                // Calculăm o poziție liberă pentru noul element
                $position = findFreePosition($floor, 'element');

                $_SESSION['events'][$_SESSION['current_event']]['floors'][$floor]['elements'][$element_id] = [
                    'type' => $_POST['element_type'],
                    'name' => $_POST['element_name'],
                    'x' => $position['x'],
                    'y' => $position['y']
                ];
            }
            break;

        case 'update_position':
            if ($_SESSION['current_event']) {
                $item_id = $_POST['item_id'];
                $floor = $_POST['floor'];
                $item_type = $_POST['item_type'];
                $x = (int)$_POST['x'];
                $y = (int)$_POST['y'];

                if ($item_type === 'table') {
                    $_SESSION['events'][$_SESSION['current_event']]['floors'][$floor]['tables'][$item_id]['x'] = $x;
                    $_SESSION['events'][$_SESSION['current_event']]['floors'][$floor]['tables'][$item_id]['y'] = $y;
                } else {
                    $_SESSION['events'][$_SESSION['current_event']]['floors'][$floor]['elements'][$item_id]['x'] = $x;
                    $_SESSION['events'][$_SESSION['current_event']]['floors'][$floor]['elements'][$item_id]['y'] = $y;
                }
            }
            break;

        case 'update_guests':
            if ($_SESSION['current_event']) {
                $table_id = $_POST['table_id'];
                $floor = $_POST['floor'];
                $guests = $_POST['guests'] ?? [];
                $_SESSION['events'][$_SESSION['current_event']]['floors'][$floor]['tables'][$table_id]['guests'] = $guests;
                $_SESSION['events'][$_SESSION['current_event']]['floors'][$floor]['tables'][$table_id]['notes'] = $_POST['notes'] ?? '';
            }
            break;

        case 'delete_table':
            if ($_SESSION['current_event']) {
                $table_id = $_POST['table_id'];
                $floor = $_POST['floor'];
                unset($_SESSION['events'][$_SESSION['current_event']]['floors'][$floor]['tables'][$table_id]);
            }
            break;

        case 'delete_element':
            if ($_SESSION['current_event']) {
                $element_id = $_POST['element_id'];
                $floor = $_POST['floor'];
                unset($_SESSION['events'][$_SESSION['current_event']]['floors'][$floor]['elements'][$element_id]);
            }
            break;
    }
}

// Funcție pentru găsirea unei poziții libere
function findFreePosition($floor, $type) {
    if (!$_SESSION['current_event']) return ['x' => 50, 'y' => 50];

    $current_event = $_SESSION['events'][$_SESSION['current_event']];
    $floor_data = $current_event['floors'][$floor];

    $occupied_positions = [];

    // Colectăm pozițiile ocupate de mese
    foreach ($floor_data['tables'] as $table) {
        $size = 60 + ($table['capacity'] * 3);
        $occupied_positions[] = [
            'x' => $table['x'],
            'y' => $table['y'],
            'width' => $size,
            'height' => $size
        ];
    }

    // Colectăm pozițiile ocupate de elemente
    foreach ($floor_data['elements'] as $element) {
        $occupied_positions[] = [
            'x' => $element['x'],
            'y' => $element['y'],
            'width' => 120,
            'height' => 80
        ];
    }

    // Căutăm o poziție liberă
    $margin = 20;
    $grid_size = 140; // Distanța între poziții

    for ($row = 0; $row < 3; $row++) {
        for ($col = 0; $col < 6; $col++) {
            $x = 50 + ($col * $grid_size);
            $y = 50 + ($row * $grid_size);

            $is_free = true;
            foreach ($occupied_positions as $pos) {
                if ($x < $pos['x'] + $pos['width'] + $margin &&
                    $x + 120 > $pos['x'] - $margin &&
                    $y < $pos['y'] + $pos['height'] + $margin &&
                    $y + 80 > $pos['y'] - $margin) {
                    $is_free = false;
                    break;
                }
            }

            if ($is_free) {
                return ['x' => $x, 'y' => $y];
            }
        }
    }

    // Dacă nu găsim o poziție în grid, returnăm o poziție random
    return ['x' => rand(50, 400), 'y' => rand(50, 200)];
}

$current_event = $_SESSION['current_event'] ? $_SESSION['events'][$_SESSION['current_event']] : null;
?>

<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Planificator Mese Evenimente</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .header h1 {
            color: #667eea;
            margin-bottom: 10px;
            font-size: 2.5em;
        }

        .event-selector {
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn-secondary {
            background: linear-gradient(45deg, #f093fb, #f5576c);
        }

        .btn-danger {
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
        }

        .btn-small {
            padding: 8px 16px;
            font-size: 12px;
        }

        .card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }

        .card-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            font-size: 1.2em;
            font-weight: bold;
        }

        .card-body {
            padding: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #555;
        }

        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e1e1;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
        }

        .tabs {
            display: flex;
            background: #f8f9fa;
            border-radius: 15px 15px 0 0;
            overflow: hidden;
        }

        .tab {
            flex: 1;
            padding: 15px 20px;
            text-align: center;
            cursor: pointer;
            background: #e9ecef;
            border: none;
            transition: all 0.3s ease;
            font-weight: bold;
        }

        .tab.active {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .tab-content {
            display: none;
            padding: 30px;
            background: white;
            border-radius: 0 0 15px 15px;
        }

        .tab-content.active {
            display: block;
        }

        .floor-controls {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .floor-layout {
            position: relative;
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 15px;
            min-height: 500px;
            margin-bottom: 30px;
            overflow: hidden;
        }

        .draggable-item {
            position: absolute;
            cursor: move;
            transition: all 0.2s ease;
            user-select: none;
            z-index: 10;
        }

        .draggable-item:hover {
            transform: scale(1.05);
            z-index: 20;
        }

        .draggable-item.dragging {
            transform: rotate(5deg) scale(1.1);
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            z-index: 100;
        }

        .table-item {
            background: white;
            border: 3px solid #667eea;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #667eea;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .table-square {
            border-radius: 15px;
        }

        .table-rectangle {
            border-radius: 25px;
        }

        .special-element {
            background: linear-gradient(45deg, #f093fb, #f5576c);
            color: white;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            min-width: 120px;
            min-height: 80px;
            padding: 10px;
        }

        .element-masa-miri { background: linear-gradient(45deg, #ffd700, #ffed4e); color: #333; }
        .element-scena { background: linear-gradient(45deg, #ff6b6b, #ee5a52); }
        .element-cabina-foto { background: linear-gradient(45deg, #4ecdc4, #44a08d); }
        .element-masa-foto-video { background: linear-gradient(45deg, #a8edea, #fed6e3); color: #333; }
        .element-candy-bar { background: linear-gradient(45deg, #ff9a9e, #fecfef); color: #333; }
        .element-bar-bauturi { background: linear-gradient(45deg, #667eea, #764ba2); }
        .element-intrare { background: linear-gradient(45deg, #654ea3, #eaafc8); }
        .element-toaleta { background: linear-gradient(45deg, #bdc3c7, #2c3e50); }
        .element-bucatarie { background: linear-gradient(45deg, #f7971e, #ffd200); color: #333; }

        .guest-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .guest-input {
            padding: 8px 12px;
            border: 2px solid #e1e1e1;
            border-radius: 8px;
            transition: border-color 0.3s ease;
        }

        .guest-input:focus {
            outline: none;
            border-color: #667eea;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
        }

        .stat-label {
            color: #666;
            margin-top: 5px;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #000;
        }

        .row {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        .col {
            flex: 1;
            min-width: 200px;
        }

        .item-controls {
            position: absolute;
            top: -35px;
            right: -10px;
            display: none;
            gap: 5px;
        }

        .draggable-item:hover .item-controls {
            display: flex;
        }

        .control-btn {
            background: rgba(255,255,255,0.9);
            border: none;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .event-selector {
                flex-direction: column;
                align-items: stretch;
            }

            .tabs {
                flex-direction: column;
            }

            .stats {
                grid-template-columns: 1fr;
            }

            .floor-controls {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
<div class="container">
    <div class="header">
        <h1>🎉 Planificator Mese Evenimente</h1>
        <div class="event-selector">
            <?php if (empty($_SESSION['events'])): ?>
                <p>Nu există evenimente create. Creează primul eveniment!</p>
            <?php else: ?>
                <form method="POST" style="display: inline;">
                    <select name="event_id" class="form-control" style="width: auto; display: inline-block;" onchange="this.form.submit()">
                        <option value="">Selectează eveniment</option>
                        <?php foreach ($_SESSION['events'] as $id => $event): ?>
                            <option value="<?= $id ?>" <?= $_SESSION['current_event'] === $id ? 'selected' : '' ?>>
                                <?= htmlspecialchars($event['name']) ?> (<?= $event['date'] ?>)
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <input type="hidden" name="action" value="select_event">
                </form>
            <?php endif; ?>
            <button class="btn" onclick="showCreateEventModal()">➕ Eveniment Nou</button>
        </div>
    </div>

    <?php if ($current_event): ?>
        <div class="stats">
            <?php
            $total_tables = 0;
            $total_guests = 0;
            $occupied_seats = 0;
            $total_elements = 0;

            foreach ($current_event['floors'] as $floor) {
                $total_tables += count($floor['tables']);
                $total_elements += count($floor['elements']);
                foreach ($floor['tables'] as $table) {
                    $total_guests += $table['capacity'];
                    $occupied_seats += count(array_filter($table['guests']));
                }
            }
            ?>
            <div class="stat-card">
                <div class="stat-number"><?= $total_tables ?></div>
                <div class="stat-label">Mese Total</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?= $total_guests ?></div>
                <div class="stat-label">Locuri Total</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?= $occupied_seats ?></div>
                <div class="stat-label">Locuri Ocupate</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?= $total_elements ?></div>
                <div class="stat-label">Elemente Speciale</div>
            </div>
        </div>

        <div class="card">
            <div class="tabs">
                <button class="tab active" onclick="showTab('parter')">🏢 Parter</button>
                <button class="tab" onclick="showTab('etaj1')">🏗️ Etaj 1</button>
                <button class="tab" onclick="showTab('balcon')">🌅 Balcon</button>
            </div>

            <?php foreach ($current_event['floors'] as $floor_id => $floor): ?>
                <div id="<?= $floor_id ?>" class="tab-content <?= $floor_id === 'parter' ? 'active' : '' ?>">
                    <div class="floor-controls">
                        <button class="btn" onclick="showAddTableModal('<?= $floor_id ?>')">🪑 Adaugă Masă</button>
                        <button class="btn btn-secondary" onclick="showAddElementModal('<?= $floor_id ?>')">✨ Element Special</button>
                    </div>

                    <div class="floor-layout" id="layout-<?= $floor_id ?>"
                         ondrop="drop(event, '<?= $floor_id ?>')"
                         ondragover="allowDrop(event)">

                        <!-- Mese -->
                        <?php foreach ($floor['tables'] as $table_id => $table): ?>
                            <div class="draggable-item table-item table-<?= $table['shape'] ?>"
                                 style="left: <?= $table['x'] ?>px; top: <?= $table['y'] ?>px;
                                         width: <?= 60 + ($table['capacity'] * 3) ?>px;
                                         height: <?= 60 + ($table['capacity'] * 3) ?>px;"
                                 draggable="true"
                                 ondragstart="dragStart(event, '<?= $table_id ?>', 'table', '<?= $floor_id ?>')"
                                 ondblclick="showTableModal('<?= $table_id ?>', '<?= $floor_id ?>')">
                                <div>
                                    <div style="font-size: 14px;">Masa <?= $table['number'] ?></div>
                                    <div style="font-size: 10px;"><?= count(array_filter($table['guests'])) ?>/<?= $table['capacity'] ?></div>
                                </div>
                                <div class="item-controls">
                                    <button class="control-btn" onclick="showTableModal('<?= $table_id ?>', '<?= $floor_id ?>')" title="Editează">✏️</button>
                                    <button class="control-btn" onclick="deleteItem('<?= $table_id ?>', 'table', '<?= $floor_id ?>')" title="Șterge">🗑️</button>
                                </div>
                            </div>
                        <?php endforeach; ?>

                        <!-- Elemente speciale -->
                        <?php foreach ($floor['elements'] as $element_id => $element): ?>
                            <div class="draggable-item special-element element-<?= $element['type'] ?>"
                                 style="left: <?= $element['x'] ?>px; top: <?= $element['y'] ?>px;"
                                 draggable="true"
                                 ondragstart="dragStart(event, '<?= $element_id ?>', 'element', '<?= $floor_id ?>')">
                                <?= htmlspecialchars($element['name']) ?>
                                <div class="item-controls">
                                    <button class="control-btn" onclick="deleteItem('<?= $element_id ?>', 'element', '<?= $floor_id ?>')" title="Șterge">🗑️</button>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php else: ?>
        <div class="card">
            <div class="card-body" style="text-align: center; padding: 60px;">
                <h2>Bine ai venit! 🎊</h2>
                <p style="margin: 20px 0; font-size: 1.1em; color: #666;">
                    Creează primul tău eveniment pentru a începe să planifici aranjarea meselor.
                </p>
                <button class="btn" onclick="showCreateEventModal()">🎉 Creează Primul Eveniment</button>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Modal Creare Eveniment -->
<div id="createEventModal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="hideModal('createEventModal')">&times;</span>
        <h2>📅 Eveniment Nou</h2>
        <form method="POST">
            <input type="hidden" name="action" value="create_event">
            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label>Nume Eveniment</label>
                        <input type="text" name="event_name" class="form-control" placeholder="ex: Nunta Maria & Ion" required>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label>Tip Eveniment</label>
                        <select name="event_type" class="form-control" required>
                            <option value="nunta">💒 Nuntă</option>
                            <option value="botez">👶 Botez</option>
                            <option value="aniversare">🎂 Aniversare</option>
                            <option value="corporate">🏢 Corporate</option>
                            <option value="altul">🎪 Altul</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label>Data Eveniment</label>
                        <input type="date" name="event_date" class="form-control" required>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label>Locația</label>
                        <input type="text" name="location" class="form-control" placeholder="ex: Restaurant Royal" required>
                    </div>
                </div>
            </div>
            <button type="submit" class="btn" style="width: 100%; margin-top: 20px;">✨ Creează Eveniment</button>
        </form>
    </div>
</div>

<!-- Modal Adaugare Masa -->
<div id="addTableModal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="hideModal('addTableModal')">&times;</span>
        <h2>🪑 Masă Nouă</h2>
        <form method="POST">
            <input type="hidden" name="action" value="add_table">
            <input type="hidden" name="floor" id="modal-floor">

            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label>Numărul Mesei</label>
                        <input type="number" name="table_number" class="form-control" min="1" required>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label>Forma Mesei</label>
                        <select name="table_shape" class="form-control" required>
                            <option value="round">⭕ Rotundă</option>
                            <option value="square">⬜ Pătrată</option>
                            <option value="rectangle">▭ Dreptunghiulară</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label>Numărul de Persoane</label>
                <select name="table_capacity" class="form-control" required>
                    <?php for ($i = 4; $i <= 16; $i++): ?>
                        <option value="<?= $i ?>" <?= $i === 8 ? 'selected' : '' ?>><?= $i ?> persoane</option>
                    <?php endfor; ?>
                </select>
            </div>
            <button type="submit" class="btn" style="width: 100%; margin-top: 20px;">➕ Adaugă Masa</button>
        </form>
    </div>
</div>

<!-- Modal Adaugare Element Special -->
<div id="addElementModal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="hideModal('addElementModal')">&times;</span>
        <h2>✨ Element Special</h2>
        <form method="POST">
            <input type="hidden" name="action" value="add_element">
            <input type="hidden" name="floor" id="element-modal-floor">

            <div class="form-group">
                <label>Tipul Elementului</label>
                <select name="element_type" class="form-control" onchange="updateElementName()" id="element-type" required>
                    <option value="masa-miri">👑 Masa Mirilor</option>
                    <option value="scena">🎤 Scenă/Formație</option>
                    <option value="cabina-foto">📸 Cabina Foto</option>
                    <option value="masa-foto-video">🎥 Masa Foto-Video</option>
                    <option value="candy-bar">🍭 Candy Bar</option>
                    <option value="bar-bauturi">🍸 Bar Băuturi</option>
                    <option value="intrare">🚪 Intrare</option>
                    <option value="toaleta">🚽 Toaletă</option>
                    <option value="bucatarie">👨‍🍳 Bucătărie</option>
                </select>
            </div>

            <div class="form-group">
                <label>Numele Elementului</label>
                <input type="text" name="element_name" id="element-name" class="form-control" placeholder="ex: Masa Mirilor" required>
            </div>

            <button type="submit" class="btn" style="width: 100%; margin-top: 20px;">✨ Adaugă Element</button>
        </form>
    </div>
</div>

<!-- Modal Editare Masa -->
<div id="tableModal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="hideModal('tableModal')">&times;</span>
        <h2 id="table-modal-title">✏️ Editează Masa</h2>
        <form method="POST">
            <input type="hidden" name="action" value="update_guests">
            <input type="hidden" name="table_id" id="edit-table-id">
            <input type="hidden" name="floor" id="edit-floor">

            <div class="form-group">
                <label>Invitați</label>
                <div id="guests-container" class="guest-list"></div>
            </div>

            <div class="form-group">
                <label>Notițe (meniu special, alergii, etc.)</label>
                <textarea name="notes" id="table-notes" class="form-control" rows="3" placeholder="ex: Meniu vegetarian pentru Maria, Ion nu mănâncă fructe de mare..."></textarea>
            </div>

            <div style="display: flex; gap: 10px; margin-top: 20px;">
                <button type="submit" class="btn" style="flex: 1;">💾 Salvează</button>
                <button type="button" class="btn btn-danger" onclick="deleteItem(document.getElementById('edit-table-id').value, 'table', document.getElementById('edit-floor').value)" style="flex: 1;">🗑️ Șterge Masa</button>
            </div>
        </form>
    </div>
</div>

<script>
    let currentTableId = '';
    let currentFloor = '';
    let draggedElement = null;
    let draggedType = '';
    let draggedFloor = '';

    function showTab(tabId) {
        // Ascunde toate tab-urile
        document.querySelectorAll('.tab-content').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelectorAll('.tab').forEach(tab => {
            tab.classList.remove('active');
        });

        // Afișează tab-ul selectat
        document.getElementById(tabId).classList.add('active');
        event.target.classList.add('active');
    }

    function showCreateEventModal() {
        document.getElementById('createEventModal').style.display = 'block';
    }

    function showAddTableModal(floor) {
        document.getElementById('modal-floor').value = floor;
        document.getElementById('addTableModal').style.display = 'block';
    }

    function showAddElementModal(floor) {
        document.getElementById('element-modal-floor').value = floor;
        updateElementName(); // Setează numele implicit
        document.getElementById('addElementModal').style.display = 'block';
    }

    function updateElementName() {
        const typeSelect = document.getElementById('element-type');
        const nameInput = document.getElementById('element-name');
        const typeText = typeSelect.options[typeSelect.selectedIndex].text;
        nameInput.value = typeText.substring(2); // Elimină emoji-ul
    }

    function showTableModal(tableId, floor) {
        currentTableId = tableId;
        currentFloor = floor;

        // Simulăm încărcarea datelor mesei din sesiune
        // În realitate, ar trebui să facem un request AJAX sau să avem datele în JavaScript
        document.getElementById('edit-table-id').value = tableId;
        document.getElementById('edit-floor').value = floor;
        document.getElementById('table-modal-title').textContent = `✏️ Editează Masa`;

        // Pentru demonstrație, generăm câmpurile pentru invitați
        // În implementarea reală, aceste date ar veni din server
        const container = document.getElementById('guests-container');
        container.innerHTML = '';

        // Generăm 8 câmpuri implicit (pot fi personalizate)
        for (let i = 0; i < 8; i++) {
            const input = document.createElement('input');
            input.type = 'text';
            input.name = `guests[${i}]`;
            input.placeholder = `Locul ${i + 1}`;
            input.className = 'guest-input';
            container.appendChild(input);
        }

        document.getElementById('tableModal').style.display = 'block';
    }

    function hideModal(modalId) {
        document.getElementById(modalId).style.display = 'none';
    }

    function deleteItem(itemId, itemType, floor) {
        const itemName = itemType === 'table' ? 'masa' : 'elementul';
        if (confirm(`Ești sigur că vrei să ștergi ${itemName}?`)) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = `
                    <input type="hidden" name="action" value="delete_${itemType}">
                    <input type="hidden" name="${itemType}_id" value="${itemId}">
                    <input type="hidden" name="floor" value="${floor}">
                `;
            document.body.appendChild(form);
            form.submit();
        }
    }

    // Funcții Drag & Drop
    function dragStart(event, itemId, itemType, floor) {
        draggedElement = itemId;
        draggedType = itemType;
        draggedFloor = floor;
        event.target.classList.add('dragging');

        // Stocăm offsetul pentru poziționare precisă
        const rect = event.target.getBoundingClientRect();
        const layoutRect = event.target.parentElement.getBoundingClientRect();
        event.dataTransfer.setData('text/plain', '');
        event.dataTransfer.effectAllowed = 'move';
    }

    function allowDrop(event) {
        event.preventDefault();
    }

    function drop(event, floor) {
        event.preventDefault();

        if (!draggedElement) return;

        const layout = document.getElementById(`layout-${floor}`);
        const rect = layout.getBoundingClientRect();

        // Calculăm poziția relativă la container
        const x = Math.max(0, Math.min(event.clientX - rect.left - 60, rect.width - 120));
        const y = Math.max(0, Math.min(event.clientY - rect.top - 40, rect.height - 80));

        // Trimitem poziția la server
        updateItemPosition(draggedElement, draggedType, floor, x, y);

        // Resetăm variabilele de drag
        const draggedEl = document.querySelector('.dragging');
        if (draggedEl) {
            draggedEl.classList.remove('dragging');
        }

        draggedElement = null;
        draggedType = '';
        draggedFloor = '';
    }

    function updateItemPosition(itemId, itemType, floor, x, y) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.style.display = 'none';
        form.innerHTML = `
                <input type="hidden" name="action" value="update_position">
                <input type="hidden" name="item_id" value="${itemId}">
                <input type="hidden" name="item_type" value="${itemType}">
                <input type="hidden" name="floor" value="${floor}">
                <input type="hidden" name="x" value="${Math.round(x)}">
                <input type="hidden" name="y" value="${Math.round(y)}">
            `;
        document.body.appendChild(form);
        form.submit();
    }

    // Event listeners pentru drag end
    document.addEventListener('dragend', function(event) {
        event.target.classList.remove('dragging');
    });

    // Închide modalele când se dă click în afara lor
    window.onclick = function(event) {
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        });
    }

    // Previne comportamentul implicit de drag pe imagini și alte elemente
    document.addEventListener('DOMContentLoaded', function() {
        document.querySelectorAll('img').forEach(img => {
            img.addEventListener('dragstart', function(e) {
                e.preventDefault();
            });
        });
    });

    // Adaugă effect de hover pentru zonele de drop
    document.addEventListener('DOMContentLoaded', function() {
        const layouts = document.querySelectorAll('.floor-layout');
        layouts.forEach(layout => {
            layout.addEventListener('dragover', function(event) {
                event.preventDefault();
                this.style.backgroundColor = '#e3f2fd';
            });

            layout.addEventListener('dragleave', function(event) {
                this.style.backgroundColor = '#f8f9fa';
            });

            layout.addEventListener('drop', function(event) {
                this.style.backgroundColor = '#f8f9fa';
            });
        });
    });
</script>
</body>
</html>