<link rel="stylesheet" href="assets/css/invoiri-custom.css">
<link rel="stylesheet" href="assets/css/select2-modal-fix.css">
<!-- Make sure select2 CSS is loaded after Bootstrap -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css">

<!-- Enhanced animation styles -->
<style>
    /* Table animation enhancements */
    .table-responsive {
        overflow: hidden; /* Ensure animations don't cause horizontal scrollbars */
    }

    /* Highlight effect for new rows */
    .animate__highlighted {
        animation: highlightPulse 1.5s ease-in-out;
    }

    @keyframes highlightPulse {
        0% { background-color: transparent; }
        30% { background-color: rgba(13, 110, 253, 0.1); }
        100% { background-color: transparent; }
    }

    /* Smoother animations */
    .table tbody tr {
        transition: all 0.3s ease-out;
        transform-origin: center;
        backface-visibility: hidden;
        perspective: 1000px;
        will-change: transform, opacity;
        cursor: default;
    }

    /* Interactive hover effects */
    .table tbody tr:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        background-color: rgba(13, 110, 253, 0.03);
    }

    /* Status badge enhancements */
    .table .badge {
        transition: all 0.2s ease;
    }

    .table tr:hover .badge {
        transform: scale(1.05);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    /* Loading overlay enhancements */
    .table-loading-overlay {
        transition: opacity 0.3s ease-out;
        border-radius: 0.25rem;
    }

    /* Spinner enhancements */
    .table-loading-overlay .spinner-border {
        width: 3rem;
        height: 3rem;
        border-width: 0.25rem;
        box-shadow: 0 0 15px rgba(13, 110, 253, 0.3);
    }
</style>
<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

function checkUserExists($email)
{
    global $pdo;
    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = :email");
    $stmt->execute(['email' => $email]);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

function insertUser($nume, $prenume, $email, $status = 'active', $id_structura = NULL, $login_type = 'activedir')
{
    global $pdo;
    $stmt = $pdo->prepare("INSERT INTO users (nume, prenume, email, status, id_structura, login_type, added_on, timestamp)
                           VALUES (:nume, :prenume, :email, :status, :id_structura, :login_type, NOW(), NOW())");
    $stmt->execute([
        'nume' => $nume,
        'prenume' => $prenume,
        'email' => $email,
        'status' => $status,
        'id_structura' => $id_structura,
        'login_type' => $login_type
    ]);
    return $pdo->lastInsertId();
}

function updateUserLogin($uid)
{
    global $pdo;
    $stmt = $pdo->prepare("UPDATE users SET timestamp = NOW() WHERE uid = :uid");
    $stmt->execute(['uid' => $uid]);
}


ob_start();
require_once 'inc/cfg_head.php';
require_once 'inc/cfg_menu.php';
require_once 'inc/cfg_functions.php';
require_once 'mentenanta.php';
global $pdo;

$login_error = '';
$login_success = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['login'])) {
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';

    if (empty($email) || empty($password)) {
        $login_error = 'Vă rugăm completați toate câmpurile.';
    } else {
        $username = explode('@', $email)[0];
        if (loginLDAPS($username, $password)) {
            $user = checkUserExists($email);
            if ($user) {
                updateUserLogin($user['uid']);
            } else {
                $nume = "Nume"; // Should be retrieved from LDAP
                $prenume = "Prenume"; // Should be retrieved from LDAP
                $uid = insertUser($nume, $prenume, $email, 'active', NULL, 'invoiri');
            }
            $_SESSION['invoiri_user_id'] = $uid;
            $_SESSION['invoiri_user_name'] = $user['nume'] . ' ' . $user['prenume'];
            $_SESSION['invoiri_user_email'] = $user['email'];
            $_SESSION['invoiri_user_structura'] = $user['id_structura'];
            $login_success = true;
            header('Location: invoiri.php');
            exit;
        } else {
            $login_error = 'Autentificare eșuată. Verificați datele de conectare.';
        }
    }
}
$is_logged_in = isset($_SESSION['invoiri_user_id']);
?>
<div class="wrapper d-flex flex-column min-vh-100 bg-light">
    <?php include 'inc/cfg_topnav.php'; ?>
    <div class="body flex-grow-1 px-3">

        <div class="body flex-grow-1 px-3">
            <div class="row">
                <div class="col-lg-12">
                    <div class="card mb-4">
                        <div class="card-header">
                            <i class="cil-clock me-2"></i> Formular de solicitare învoire
                        </div>
                        <div class="card-body">
                            <?php
                            if (!$is_logged_in) {
                                ?>
                                <div class="card-body" style="max-width: 400px; margin: 10px auto; text-align: center;">
                                    <h3 class="pb-3">Autentificare</h3>
                                    <?php if ($login_error): ?>
                                        <div class="alert alert-danger" role="alert">
                                            <?php echo $login_error; ?>
                                        </div>
                                    <?php endif; ?>
                                    <form method="POST">
                                        <div class="input-group mb-3">
                                            <span class="input-group-text">
                                              <svg class="icon">
                                                <use xlink:href="vendors/@coreui/icons/svg/free.svg#cil-user"></use>
                                              </svg>
                                            </span>
                                            <input type="email" class="form-control p_input" name="email"
                                                   style="height: auto;" placeholder="User" required>
                                        </div>
                                        <div class="input-group mb-4">
                                            <span class="input-group-text">
                                              <svg class="icon">
                                                <use xlink:href="vendors/@coreui/icons/svg/free.svg#cil-lock-locked"></use>
                                              </svg>
                                            </span>
                                            <input type="password" class="form-control" id="password" name="password"
                                                   style="height: auto;" placeholder="Parolă" required>
                                        </div>
                                        <div class="row">
                                            <div class="col-12">
                                                <button type="submit" name="login" class="btn btn-primary px-4">
                                                    Autentificare
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                                <?php
                            } else { ?>
                                <div class="card mb-4">
                                    <div class="card-body">
                                        <div class="invoiri-form">
                                            <input type="hidden" id="user_id"
                                                   value="<?php echo $_SESSION['invoiri_user_id'] ?? null; ?>">

                                            <div class="row mb-4">
                                                <div class="col-md-4">
                                                    <div class="form-group position-relative">
                                                        <label for="numePrenume" class="form-label">
                                                            <svg class="icon me-2">
                                                                <use xlink:href="vendors/@coreui/icons/svg/free.svg#cil-user"></use>
                                                            </svg>
                                                            Nume și prenume
                                                        </label>
                                                        <div class="input-group">
                                                            <input type="text" class="form-control nume-prenume-input"
                                                                   id="numePrenume"
                                                                   placeholder="Nume Prenume" style="height: auto;"
                                                                   value="<?php echo $_SESSION['invoiri_user_name'] ?? ''; ?>">
                                                            <button class="btn btn-dark nume-prenume-btn" type="button">
                                                                <svg class="icon">
                                                                    <use xlink:href="vendors/@coreui/icons/svg/free.svg#cil-user"></use>
                                                                </svg>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>


                                                <div class="col-md-4">
                                                    <?php
                                                    $selectStructuri = "SELECT * FROM mj.z_structuri zs
                                                            WHERE zs.id_ordonator = 1
                                                            ORDER BY zs.prioritate, zs.id_structura_sup";
                                                    $selectStructuri = $pdo->query($selectStructuri)->fetchAll();
                                                    $optionStructuri = "<select class='form-control select2' id='structura'>
                                                            <option value=''>Alege structura</option>";
                                                    foreach ($selectStructuri as $s) {
                                                        $selected = ($s['idz_structuri'] == $_SESSION['invoiri_user_structura']) ? 'selected' : '';
                                                        $optionStructuri .= "<option value='{$s['idz_structuri']}' $selected>{$s['den']}</option>";
                                                    }
                                                    $optionStructuri .= "</select>"; ?>
                                                    <div class="form-group position-relative">
                                                        <label for="structura" class="form-label">
                                                            <svg class="icon me-2">
                                                                <use xlink:href="vendors/@coreui/icons/svg/free.svg#cil-building"></use>
                                                            </svg>
                                                            Structura
                                                        </label>
                                                        <?php echo $optionStructuri; ?>
                                                    </div>
                                                </div>


                                                <div class="col-md-4">
                                                    <?php
                                                    $optionSuperior = "<select class='form-control select2' id='superior'>";
                                                    $optionSuperior .= "<option value=''>Alege structura înainte</option>";
                                                    $optionSuperior .= "</select>";
                                                    ?>
                                                    <div class="form-group position-relative">
                                                        <label for="superior" class="form-label">
                                                            <svg class="icon me-2">
                                                                <use xlink:href="vendors/@coreui/icons/svg/free.svg#cil-school"></use>
                                                            </svg>
                                                            Superior
                                                        </label>
                                                        <?php echo $optionSuperior; ?>
                                                    </div>
                                                </div>


                                            </div>

                                            <div class="row mb-4">
                                                <div class="col-md-4">
                                                    <div class="form-group position-relative">
                                                        <label for="dataInvoirii" class="form-label">
                                                            <svg class="icon me-2">
                                                                <use xlink:href="vendors/@coreui/icons/svg/free.svg#cil-calendar"></use>
                                                            </svg>
                                                            Data învoirii
                                                        </label>
                                                        <div class="input-group">
                                                            <input type="text" id="dataInvoire"
                                                                   class="form-control datepicker"
                                                                   style="height: auto; z-index: auto !important;"
                                                                   readonly>
                                                            <button class="btn btn-dark calendarLucrare" type="button">
                                                                <svg class="icon">
                                                                    <use xlink:href="vendors/@coreui/icons/svg/free.svg#cil-calendar"></use>
                                                                </svg>
                                                            </button>
                                                        </div>
                                                        <small class="form-text text-muted">Selectați o dată <b>începând cu astăzi</b></small>
                                                    </div>
                                                </div>

                                                <div class="col-md-4">
                                                    <div class="form-group position-relative">
                                                        <label for="oraInceput" class="form-label">
                                                            <svg class="icon me-2">
                                                                <use xlink:href="vendors/@coreui/icons/svg/free.svg#cil-clock"></use>
                                                            </svg>
                                                            Ora de început
                                                        </label>
                                                        <div class="input-group time-picker-toggle">
                                                            <input type="text" class="form-control custom-time-input"
                                                                   style="height: auto;" id="oraInvoire"
                                                                   placeholder="HH:MM"
                                                                   pattern="([01]?[0-9]|2[0-3]):[0-5][0-9]"
                                                                   maxlength="5" readonly>
                                                            <button class="btn btn-dark"
                                                                    type="button">
                                                                <svg class="icon">
                                                                    <use xlink:href="vendors/@coreui/icons/svg/free.svg#cil-clock"></use>
                                                                </svg>
                                                            </button>
                                                        </div>
                                                        <div id="timePickerDropdown" class="time-picker-dropdown">
                                                            <div class="time-picker-container">
                                                                <div class="time-picker-header">
                                                                    <span>Selectează ora</span>
                                                                </div>
                                                                <div class="time-picker-content">
                                                                    <div class="time-picker-hours"></div>
                                                                    <div class="time-picker-minutes"></div>
                                                                </div>
                                                                <div class="time-picker-footer">
                                                                    <button type="button"
                                                                            class="btn btn-sm btn-primary time-picker-apply">
                                                                        Aplică
                                                                    </button>
                                                                    <button type="button"
                                                                            class="btn btn-sm btn-secondary time-picker-cancel">
                                                                        Anulează
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <small class="form-text text-muted">Minim 1 oră în
                                                            viitor</small>
                                                    </div>
                                                </div>


                                                <div class="col-md-4">
                                                    <div class="form-group position-relative">
                                                        <label for="durata" class="form-label">
                                                            <svg class="icon me-2">
                                                                <use xlink:href="vendors/@coreui/icons/svg/free.svg#cil-av-timer"></use>
                                                            </svg>
                                                            Durata învoirii
                                                        </label>
                                                        <select class="form-control select2" id="durata">
                                                            <option value="30_minute" selected>30 minute</option>
                                                            <option value="1_ora">1 oră</option>
                                                            <option value="2_ore">2 ore</option>
                                                            <option value="3_ore">3 ore</option>
                                                            <option value="4_ore">4 ore</option>
                                                        </select>
                                                        <small class="form-text text-muted">Minim 30 minute</small>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row mb-3">
                                                <div class="col-12">
                                                    <div class="alert alert-light border">
                                                        <i class="cil-info me-2"></i>
                                                        <small>În temeiul art. 215 alin. (4) din Regulamentul de
                                                            organizare şi
                                                            funcţionare a Ministerului Justiţiei, aprobat prin Ordinul
                                                            Ministrului Justiţiei
                                                            nr. 1973/C/2022, cu modificările şi completările
                                                            ulterioare</small>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                                                <button type="reset" class="btn btn-outline-secondary">
                                                    <i class="cil-x me-2"></i>Anulează
                                                </button>
                                                <button id="submit" class="btn btn-dark" data-user-id="<?php echo $_SESSION['invoiri_user_id'] ?? null; ?>">
                                                    <i class="cil-paper-plane me-2"></i>Trimite cerere
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Istoric Invoiri -->
                                <div class="card mb-4">
                                    <div class="card-header bg-light">
                                        <i class="cil-history me-2"></i> Istoricul solicitărilor de învoire
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-striped table-hover">
                                                <thead class="table-light">
                                                <tr>
                                                    <th><i class="cil-calendar me-2"></i>Data</th>
                                                    <th><i class="cil-clock me-2"></i>Interval orar</th>
                                                    <th><i class="cil-av-timer me-2"></i>Durata</th>
                                                    <th><i class="cil-check-circle me-2"></i>Status</th>
                                                </tr>
                                                </thead>
                                                <tbody>
                                                <?php
                                                // Get user's leave requests history
                                                $stmt = $pdo->prepare("SELECT * FROM mj.invoiri WHERE id_user = :id_user ORDER BY data_creare DESC LIMIT 10");
                                                $stmt->execute(['id_user' => $_SESSION['invoiri_user_id']]);
                                                $invoiri = $stmt->fetchAll(PDO::FETCH_ASSOC);

                                                if (count($invoiri) > 0) {
                                                    foreach ($invoiri as $invoire) {
                                                        $data_invoirii = date('d.m.Y', strtotime($invoire['data_invoirii']));
                                                        $ora_inceput = date('H:i', strtotime($invoire['ora_inceput']));
                                                        $ora_sfarsit = date('H:i', strtotime("+{$invoire['durata_ore']} hours", strtotime($invoire['ora_inceput'])));
                                                        $durata_ore = $invoire['durata_ore'];
                                                        if ($durata_ore == 0.5) {
                                                            $durata_ore = '30 minute';
                                                        }else{
                                                            $durata_ore.= ' ore';
                                                        }

                                                        $status_class = '';
                                                        $status_text = '';
                                                        $status_icon = '';

                                                        switch ($invoire['status']) {
                                                            case 'in_asteptare':
                                                                $status_class = 'badge bg-warning text-dark';
                                                                $status_text = 'În așteptare';
                                                                $status_icon = 'cil-hourglass';
                                                                break;
                                                            case 'aprobata':
                                                                $status_class = 'badge bg-success';
                                                                $status_text = 'Aprobată';
                                                                $status_icon = 'cil-check';
                                                                break;
                                                            case 'respinsa':
                                                                $status_class = 'badge bg-danger';
                                                                $status_text = 'Respinsă';
                                                                $status_icon = 'cil-x';
                                                                break;
                                                            case 'expirata':
                                                                $status_class = 'badge bg-secondary';
                                                                $status_text = 'Expirată';
                                                                $status_icon = 'cil-ban';
                                                                break;
                                                        }

                                                        echo "<tr>";
                                                        echo "<td>{$data_invoirii}</td>";
                                                        echo "<td>{$ora_inceput} - {$ora_sfarsit}</td>";
                                                        echo "<td>{$durata_ore}</td>";
                                                        echo "<td><span class='{$status_class}'><i class='{$status_icon} me-1'></i>{$status_text}</span></td>";
                                                        echo "</tr>";
                                                    }
                                                } else {
                                                    echo "<tr><td colspan='4' class='text-center text-muted'><i class='cil-info me-2'></i>Nu există solicitări de învoire.</td></tr>";
                                                }
                                                ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                <?php
                            } ?>
                        </div>


                    </div>
                </div>
            </div>
        </div>
    </div>


    <?php
    require_once 'inc/cfg_footer.php';
    ob_end_flush();
    ?>
    <script src="../assets/js/flatpickr.js"></script>
    <script src="../assets/js/bootstrap-datepicker.js"></script>
    <script src="../assets/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/swal.js"></script>
    <script src="../assets/js/invoiri.js"></script>

    <!-- Add Animate.css library for enhanced animations -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <script>
        function getAllSuperiors() {
            $.ajax({
                url: 'controller/invoiri.php',
                type: 'POST',
                data: {action: 'getAllSuperiors'},
                success: function (data) {
                    $('#superior').empty().html(data.message);

                    $('#superior').select2({
                        width: '100%',
                        dropdownParent: $('body'),
                        minimumResultsForSearch: 0
                    });

                    $('#superior').select2('open');
                }
            });
        }

        $(document).ready(function () {
            // Initialize Select2 with improved settings
            $('.select2').select2({
                width: '100%',
                dropdownParent: $('body'),
                minimumResultsForSearch: 0, // Always show search
                dropdownCssClass: 'select2-dropdown-invoiri'
            }).on('select2:open', function () {
                // Close any other open select2 dropdowns - with safety checks
                $('.select2-container--open').not($(this).data('select2').$container).each(function() {
                    var ariaOwns = $(this).attr('aria-owns');
                    if (ariaOwns) {
                        var selectId = ariaOwns.replace('select2-', '').replace('-results', '');
                        $('#' + selectId).select2('close');
                    }
                });

                // Force dropdown to appear above other elements with higher z-index
                $('.select2-container--open').css('z-index', 999999);
                $('.select2-dropdown').css('z-index', 999999);

                // Focus the search field when dropdown opens
                setTimeout(function () {
                    $('.select2-search__field').focus();
                }, 0);
            });

            // Close all select2 dropdowns when a modal opens
            $(document).on('shown.bs.modal', function() {
                $('.select2-container--open').each(function() {
                    var ariaOwns = $(this).attr('aria-owns');
                    if (ariaOwns) {
                        var selectId = ariaOwns.replace('select2-', '').replace('-results', '');
                        $('#' + selectId).select2('close');
                    }
                });
            });

            // Close all select2 dropdowns when SweetAlert opens - using MutationObserver instead of deprecated DOMNodeInserted
            var bodyObserver = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        for (var i = 0; i < mutation.addedNodes.length; i++) {
                            var node = mutation.addedNodes[i];
                            if (node.classList && node.classList.contains('swal2-container')) {
                                // SweetAlert modal was added to the DOM
                                $('.select2-container--open').each(function() {
                                    var ariaOwns = $(this).attr('aria-owns');
                                    if (ariaOwns) {
                                        var selectId = ariaOwns.replace('select2-', '').replace('-results', '');
                                        $('#' + selectId).select2('close');
                                    }
                                });
                            }
                        }
                    }
                });
            });

            // Start observing the body for added nodes
            bodyObserver.observe(document.body, { childList: true });


            $(document).off('change', '#superior').on('change', '#superior', function (e) {
                e.stopPropagation();
                if ($('#superior option:selected').val() == '-1') {
                    getAllSuperiors();
                    return;
                }
            });


            $(document).off('change', '#structura').on('change', '#structura', function (e) {
                e.stopPropagation();
                $.ajax({
                    url: 'controller/invoiri.php',
                    type: 'POST',
                    data: {
                        action: 'getSuperiorsForStructura',
                        id_structura: $('#structura').val(),
                    },
                    success: function (data) {
                        $('#superior').html(data.message);

                        $('#superior').select2({
                            width: '100%',
                            dropdownParent: $('body'),
                            minimumResultsForSearch: 0,
                            dropdownCssClass: 'select2-dropdown-invoiri'
                        });

                        var optionsCount = $('#superior option').length;
                        if (optionsCount === 3) {
                            var singleOptionValue = $('#superior option:eq(1)').val();
                            $('#superior').val(singleOptionValue).trigger('change');
                        } else if (optionsCount === 2) {
                            getAllSuperiors();
                        }
                    }
                });
            });

            $('#dataInvoire').datepicker('destroy').datepicker({
                weekStart: 1,
                format: 'dd.mm.yyyy',
                autoclose: true,
                todayHighlight: true,
                startDate: new Date(),
                language: 'ro'
            });


            // Time input handling
            $(document).on('input', '.minute-input', function () {
                let value = $(this).val();

                // Check if value exists and is not null/undefined
                if (value === undefined || value === null) {
                    return; // Exit if value is undefined or null
                }

                // Replace non-numeric characters - safely convert to string first
                value = String(value).replace(/[^0-9]/g, '');

                if (value === '') {
                    $(this).val('');
                } else {
                    const numValue = parseInt(value, 10);
                    if (numValue > 59) {
                        $(this).val('59');
                    } else {
                        $(this).val(numValue);
                    }
                }
            });

            // Add a similar handler for hours if needed
            $(document).on('input', '.hour-input', function () {
                let value = $(this).val();

                // Check if value exists
                if (value === undefined || value === null) {
                    return;
                }

                // Replace non-numeric characters - safely convert to string first
                value = String(value).replace(/[^0-9]/g, '');

                if (value === '') {
                    $(this).val('');
                } else {
                    const numValue = parseInt(value, 10);
                    if (numValue > 23) {
                        $(this).val('23');
                    } else {
                        $(this).val(numValue);
                    }
                }
            });

            // Format time inputs on blur
            $('.time-input').on('blur', function () {
                if ($(this).val() === '') {
                    $(this).val('00');
                } else {
                    const value = parseInt($(this).val(), 10);
                    $(this).val(value.toString().padStart(2, '0'));
                }
            });

            $(document).off('click', '#submit').on('click', '#submit', function (e) {
                e.preventDefault();
                const hour = $('.hour-input').val() || '00';
                const minute = $('.minute-input').val() || '00';

                let user_id = $(this).attr('data-user-id');
                let numePrenume = $('#numePrenume').val();
                let id_structura = $('#structura').val();
                let superior = $('#superior').val(); // Changed: Get the value, not the jQuery object
                let data_invoire = $('#dataInvoire').val();
                let ora_inceput = $('#oraInvoire').val();
                let durata = $('#durata').val();

                let msg = '';
                if (numePrenume.length == 0) {
                    msg += 'Completați numele și prenumele.<br>';
                }
                if (id_structura.length == 0) {
                    msg += 'Completați structura.<br>';
                }
                if (!superior) { // Changed: Check if the value exists
                    msg += 'Completați superiorul.<br>';
                }
                if (data_invoire.length == 0) {
                    msg += 'Completați data învoirii.<br>';
                }
                if (ora_inceput.length == 0) {
                    msg += 'Completați ora învoirii.<br>';
                }
                if (durata.length == 0) {
                    msg += 'Completați durata învoirii.<br>';
                }
                if (msg.length > 0) {
                    // Close any open select2 dropdowns before showing the modal
                    $('.select2-container--open').each(function() {
                        var ariaOwns = $(this).attr('aria-owns');
                        if (ariaOwns) {
                            var selectId = ariaOwns.replace('select2-', '').replace('-results', '');
                            $('#' + selectId).select2('close');
                        }
                    });

                    Swal.fire({
                        icon: 'error',
                        html: msg,
                        showConfirmButton: true,
                        didOpen: function() {
                            // Ensure all select2 dropdowns are closed when modal opens
                            $('.select2-container--open').each(function() {
                                var ariaOwns = $(this).attr('aria-owns');
                                if (ariaOwns) {
                                    var selectId = ariaOwns.replace('select2-', '').replace('-results', '');
                                    $('#' + selectId).select2('close');
                                }
                            });
                        }
                    });
                    return;
                }

                // Use regular object instead of FormData
                let formData = {
                    id_user: user_id,
                    nume_prenume: numePrenume,
                    id_structura: id_structura,
                    superior: superior,
                    data_invoire: data_invoire,
                    ora_inceput: ora_inceput,
                    durata: durata,
                    action: 'invoiala'
                };

                $.ajax({
                    url: 'controller/invoiri.php',
                    type: 'POST',
                    data: formData,
                    success: function (data) {
                        if(data.status == 'ok') {

                            console.log(data);
                            // Close any open select2 dropdowns before showing the modal
                            $('.select2-container--open').each(function() {
                                var ariaOwns = $(this).attr('aria-owns');
                                if (ariaOwns) {
                                    var selectId = ariaOwns.replace('select2-', '').replace('-results', '');
                                    $('#' + selectId).select2('close');
                                }
                            });

                            // Add successful submission feedback here
                            Swal.fire({
                                icon: 'success',
                                title: 'Cerere trimisă',
                                text: 'Cererea dvs. de învoire a fost trimisă cu succes.',
                                showConfirmButton: true,
                                didOpen: function() {
                                    // Ensure all select2 dropdowns are closed when modal opens
                                    $('.select2-container--open').each(function() {
                                        var ariaOwns = $(this).attr('aria-owns');
                                        if (ariaOwns) {
                                            var selectId = ariaOwns.replace('select2-', '').replace('-results', '');
                                            $('#' + selectId).select2('close');
                                        }
                                    });
                                },
                                willClose: function() {
                                    // Refresh the table with istoric solicitari de invoire
                                    refreshIstoricInvoiri();
                                }
                            });
                        }else if(data.status == 'error'){
                            // Close any open select2 dropdowns before showing the modal
                            $('.select2-container--open').each(function() {
                                var ariaOwns = $(this).attr('aria-owns');
                                if (ariaOwns) {
                                    var selectId = ariaOwns.replace('select2-', '').replace('-results', '');
                                    $('#' + selectId).select2('close');
                                }
                            });

                            Swal.fire({
                                icon: 'error',
                                title: 'Eroare',
                                text: data.message,
                                showConfirmButton: true,
                                didOpen: function() {
                                    // Ensure all select2 dropdowns are closed when modal opens
                                    $('.select2-container--open').each(function() {
                                        var ariaOwns = $(this).attr('aria-owns');
                                        if (ariaOwns) {
                                            var selectId = ariaOwns.replace('select2-', '').replace('-results', '');
                                            $('#' + selectId).select2('close');
                                        }
                                    });
                                }
                            });
                        }
                    },
                    error: function (xhr, status, error) {
                        console.error(error);
                        // Close any open select2 dropdowns before showing the modal
                        $('.select2-container--open').each(function() {
                            var ariaOwns = $(this).attr('aria-owns');
                            if (ariaOwns) {
                                var selectId = ariaOwns.replace('select2-', '').replace('-results', '');
                                $('#' + selectId).select2('close');
                            }
                        });

                        Swal.fire({
                            icon: 'error',
                            title: 'Eroare',
                            text: 'A apărut o eroare la trimiterea cererii.',
                            showConfirmButton: true,
                            didOpen: function() {
                                // Ensure all select2 dropdowns are closed when modal opens
                                $('.select2-container--open').each(function() {
                                    var ariaOwns = $(this).attr('aria-owns');
                                    if (ariaOwns) {
                                        var selectId = ariaOwns.replace('select2-', '').replace('-results', '');
                                        $('#' + selectId).select2('close');
                                    }
                                });
                            }
                        });
                    }
                });
            });


        });

        // Function to refresh the istoric solicitari de invoire table
        function refreshIstoricInvoiri() {
            // Show loading indicator
            var tableBody = $('.table-responsive table tbody');
            var tableContainer = $('.table-responsive');

            // Add a loading overlay
            if ($('.table-loading-overlay').length === 0) {
                tableContainer.css('position', 'relative');
                tableContainer.append('<div class="table-loading-overlay" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-color: rgba(255,255,255,0.7); display: flex; justify-content: center; align-items: center; z-index: 10;"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>');
            } else {
                $('.table-loading-overlay').show();
            }

            $.ajax({
                url: 'controller/invoiri.php',
                type: 'POST',
                data: {
                    action: 'getIstoricInvoiri',
                    id_user: $('#user_id').val()
                },
                dataType: 'json',
                success: function(response) {
                    if(response.status == 'ok') {
                        // Store current data for comparison
                        var oldData = [];
                        tableBody.find('tr').each(function() {
                            var rowData = {};
                            $(this).find('td').each(function(i) {
                                rowData[i] = $(this).text();
                            });
                            oldData.push(rowData);
                        });

                        // Replace the table body with the new data
                        tableBody.html(response.message);

                        // Hide loading overlay with a fade
                        $('.table-loading-overlay').fadeOut(300);

                        // Define an array of animation types
                        var animations = [
                            'animate__fadeInUp',
                            'animate__fadeInRight',
                            'animate__fadeInDown',
                            'animate__fadeInLeft',
                            'animate__zoomIn'
                        ];

                        // Re-apply enhanced animations to the new rows
                        setTimeout(function() {
                            tableBody.find('tr').each(function(index) {
                                var $row = $(this);

                                // Clear any existing animation classes
                                $row.removeClass('animate__animated animate__fadeInUp animate__fadeInRight animate__fadeInDown animate__fadeInLeft animate__zoomIn animate__pulse animate__highlighted');

                                // Select animation type based on index (cycle through animations)
                                var animationClass = animations[index % animations.length];

                                // Add animation with improved staggered delay
                                $row.addClass('animate__animated ' + animationClass)
                                    .css({
                                        'animation-delay': (index * 0.08) + 's',
                                        'animation-duration': '0.6s'
                                    });

                                // Add a subtle highlight effect after the main animation
                                setTimeout(function() {
                                    $row.addClass('animate__highlighted');

                                    // Remove highlight after a delay
                                    setTimeout(function() {
                                        $row.removeClass('animate__highlighted');
                                    }, 1500);
                                }, (index * 80) + 600);
                            });
                        }, 100);
                    } else {
                        // Hide loading overlay if there's an error
                        $('.table-loading-overlay').fadeOut(300);
                        console.error('Error refreshing table:', response.message);
                    }
                },
                error: function(xhr, status, error) {
                    // Hide loading overlay if there's an error
                    $('.table-loading-overlay').fadeOut(300);
                    console.error('AJAX error:', error);
                }
            });
        }
    </script>