<?php
//$ldap_dn = "cn=read-only-admin,dc=example,dc=com";
//$ldap_password = "password";
//$ldap_con = ldap_connect("ldap.forumsys.com");
//ldap_set_option($ldap_con, LDAP_OPT_PROTOCOL_VERSION, 3);
//if(ldap_bind($ldap_con, $ldap_dn, $ldap_password)) {
//    echo "Bind successful!";
//} else {
//    echo "Invalid user/pass or other errors!";
//}
?>

<link href="css/fontawesome.min.css" rel="stylesheet"/>
<?php
require_once 'inc/cfg_functions.php';
$cale_sesiune = 'assets/vendors/session/autoload.php';
require_once $cale_sesiune;
$session_factory = new \Aura\Session\SessionFactory;
$session = $session_factory->newInstance($_COOKIE);
$sesiune = $session->getSegment('Vendor\Package\ClassName');
$self = $_SERVER['PHP_SELF'];
$client_ip = $_SERVER['REMOTE_ADDR'];
$client_port = $_SERVER['REMOTE_PORT'];
$client_device = $_SERVER['HTTP_USER_AGENT'];

$session_uid = $sesiune->get("session_uid"); # try again to get a value from the segment

$login_error = '';
if (isset($session_uid) && $session_uid > 0) {
    header("Location: index.php");
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['login'])) {
    require_once("inc/cfg_pdo.php");
    global $pdo;
    $email = injSql($_POST['username']);
    $password = injSql($_POST['password']);
    (str_contains($email, '@')) ? $emailDomain = explode('@', $email)[1] : $emailDomain = '';

    if ($emailDomain != 'just.ro') {
        $login_error = 'E-mail nepermis. Folosiți contul de @just.ro.';
    } else if (empty($email) || empty($password)) {
        $login_error = 'Vă rugăm completați toate câmpurile.';
    } else {
        $userInfoLDAP = loginMJAppsLDAP($email, $password);

//        $userInfoLDAP = [];
//        $userInfoLDAP['givenname'][0] = 'Adina';
//        $userInfoLDAP['sn'][0] = 'Bura';
//        $userInfoLDAP['title'][0] = 'Specialist IT Sef';
//        $userInfoLDAP['organizational_units'][0] = 'Tribunalul Alba';

//        foreach ($userInfoLDAP as $attribute => $values) {
//            if (is_array($values) && $attribute != 'organizational_units') {
//                echo $attribute . ": ";
//                for ($i = 0; $i < $values['count']; $i++) {
//                    echo $values[$i] . " ";
//                }
//                echo "<br><br>";
//            }
//        }

        if ($userInfoLDAP === true) {
            $login_error = 'Autentificare eșuată (fara detalii in AD).';//User authenticated successfully dar fara date/detalii despre user in AD
        } else if (!is_array($userInfoLDAP)) {
            $login_error = 'Autentificare eșuată! User sau parolă greșite.';
        } else {
            //login success
            $loggedBefore = loginCheckUserExists($email);//nu exista in db
            (isset($loggedBefore['uid'])) ? $idUser = $loggedBefore['uid'] : $idUser = 0;
            (isset($loggedBefore['prenume'])) ? $prenume = $loggedBefore['prenume'] : $prenume = null;
            (isset($loggedBefore['nume'])) ? $nume = $loggedBefore['nume'] : $nume = null;
            (isset($loggedBefore['functie'])) ? $functie = $loggedBefore['functie'] : $functie = null;
            (isset($loggedBefore['id_structura'])) ? $idInstanta = $loggedBefore['id_structura'] : $idInstanta = 0;
            (isset($loggedBefore['numeInstanta'])) ? $numeInstanta = $loggedBefore['numeInstanta'] : $numeInstanta = null;
            $loginLocation = 'cartetel';
            $loginAudit = 'mj.login';
            if (!$loggedBefore) {
                $prenume = $userInfoLDAP['givenname'][0];
                $nume = $userInfoLDAP['sn'][0];
                $functie = $userInfoLDAP['title'][0];
                $checkFunctie = findBestColumnMatchDB($functie, 'sp_it.z_functii', 'functie');
                $idFunctie = $checkFunctie['idz_functii'];
                $indexOU = count($userInfoLDAP['organizational_units']) - 1;
                $instanta = $userInfoLDAP['organizational_units'][$indexOU];
                $checkInstanta = findBestColumnMatchDB($instanta, 'sp_it.z_instante', 'den');
                $idInstanta = $checkInstanta['id'];
                $numeInstanta = $checkInstanta['den'];
                if (!in_array($numeInstanta, ['Ministerul Justiției', 'Ministerul Justitiei'])) {
                    $loginLocation = 'activedir';
                    $loginAudit = 'sp_it.login';
                }
                $idUser = insertUserAtFirstLogin($nume, $prenume, $email, 'active', $idInstanta, $idFunctie, $loginLocation);
            }

            $sesiune->set("session_uid", $idUser);//1
            $sesiune->set("session_nume", $nume);//2
            $sesiune->set("session_prenume", $prenume);//3
            $sesiune->set("session_id_structura", $idInstanta);//4
            $sesiune->set("session_structura", $numeInstanta);//5
            $sesiune->set("session_email", $email);//6
            $sesiune->set("session_functie", $functie);//7
            $sesiune->set("session_id_instanta_superioara", getInstantaSuperiora($idInstanta));//8
            $sesiune->set("session_id_instanta_subordonata", getInstanteSubordonate($idInstanta));//9
            loginUpdateTS($idUser);

            $loginAudit = "insert into $loginAudit (ip, port, device, id_user, timestamp) values ('$client_ip', '$client_port', '$client_device', '$idUser', now());";
            $pdo->exec($loginAudit);

            ($loginLocation == 'cartetel') ? $redirectLocation = 'index.php' : $redirectLocation = 'sp_it_just.php';
            header("Location: $redirectLocation");
            exit;
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <base href="./">
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no">
    <meta name="description" content="Aplicatie interna Ministerul Justitiei">
    <meta name="author" content="Radu Hazsda">
    <meta name="keyword" content="MJ,Ministerul Justitiei, just.ro">
    <title>MJ Apps</title>
    <link rel="apple-touch-icon" href="assets/img/logo-mj.png">
    <link rel="icon" type="image/png" href="assets/img/logo-mj.png">
    <meta name="theme-color" content="#ffffff">
    <link href="css/style.css" rel="stylesheet">
</head>
<body>

<div class="bg-light min-vh-100 d-flex flex-row align-items-center">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-4 col-10">
                <div class="card-group d-block d-md-flex row">
                    <div class="card col-md-7 p-4 mb-0">
                        <div class="card-body">
                            <h1>Login</h1>
                            <p class="text-medium-emphasis">Autentificați-vă în cont</p>
                            <form method="POST" action="login.php">
                                <div class="input-group mb-3">
                                <span class="input-group-text">
                                  <svg class="icon">
                                    <use xlink:href="vendors/@coreui/icons/svg/free.svg#cil-user"></use>
                                  </svg>
                                </span>
                                    <input type="text" class="form-control p_input" name="username" placeholder="User">
                                </div>
                                <div class="input-group mb-4">
                                <span class="input-group-text">
                                  <svg class="icon">
                                    <use xlink:href="vendors/@coreui/icons/svg/free.svg#cil-lock-locked"></use>
                                  </svg>
                                </span>
                                    <input class="form-control" type="password" name="password" placeholder="Parolă">
                                </div>

                                <?php if ($login_error): ?>
                                    <div class="row">
                                        <div class="alert alert-danger" role="alert">
                                            <?php echo $login_error; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <div class="row">
                                    <a href="index.php" class="btn-behance mb-4" style="text-decoration: none">
                                        <i class="fa fa-backward"></i> Înapoi
                                    </a>
                                </div>

                                <div class="row">
                                    <div class="col-6">
                                        <button type="submit" name="login" class="btn btn-primary px-4">Login</button>
                                    </div>
                            </form>
                            <div class="col-6 text-end">
                                <!--                      <button class="btn btn-link px-0" type="button">Forgot password?</button>-->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
<script src="assets/js/jquery.js"></script>

<script src="assets/js/cookie.js"></script>
<script src="assets/js/datatable-fonts.js"></script>
<script src="assets/js/datatable-pdfmake.js"></script>
<script src="assets/js/datatable.js"></script>
<script src="assets/js/misc.js"></script>
<script src="assets/js/swal.js"></script>
</body>
</html>
<script>
    $(document).ready(function () {
        $(document).find('.p_input').focus();
    });
</script>

</body>
</html>