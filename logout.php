<?php
$cale_sesiune = 'assets/vendors/session/autoload.php';
require_once $cale_sesiune;
$session_factory = new \Aura\Session\SessionFactory;
$session = $session_factory->newInstance($_COOKIE);
$sesiune = $session->getSegment('Vendor\Package\ClassName');
$session_uid = $sesiune->get("session_uid");

$sesiune->set("session_uid", NULL);//1
$sesiune->set("session_nume", NULL);//2
$sesiune->set('session_prenume', NULL);//3
$sesiune->set('session_id_structura', NULL);//4
$sesiune->set('session_structura', NULL);//5
$sesiune->set('session_email', NULL);//6
$sesiune->set('session_functie', NULL);//7
$sesiune->set('session_id_instanta_superioara', NULL);//8
$sesiune->set('session_id_instanta_subordonata', NULL);//9

$session->clear();
$session->destroy();
$session_uid = $pdo = null;

header("Location: login.php");
exit;
?>
