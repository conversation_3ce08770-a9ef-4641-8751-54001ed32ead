<?php
require_once('inc/cfg_pdo_pdf2doc.php');
require_once('inc/cfg_session.php');

global $date;

class StatsPdf2Doc
{
    protected static $pdo_pdf2doc;
    protected static $pdo;

    public function __construct()
    {
        self::init();
    }

    public static function init(): void
    {
        global $pdo_pdf2doc, $pdo;
        if ($pdo_pdf2doc instanceof PDO) {
            self::$pdo_pdf2doc = $pdo_pdf2doc;
            self::$pdo = $pdo;
        } else {
            throw new PDOException("Failed to initialize PDO connection for PDF 2 Doc");
        }
    }

    public function getThisMonthPDFPagesCounter()
    {
        global $date;
        try {
            if (self::$pdo_pdf2doc instanceof PDO) {
                $check_server = self::$pdo_pdf2doc->prepare("SELECT * FROM servers where ip_address in ('***********', '**********');");
                $check_server->execute();
                $select = $check_server->fetchAll(PDO::FETCH_ASSOC);

                $raspuns = [];
                foreach ($select as $s) {
                    $reset_day = $s['counter_reset_day'];
                    $reset_date = strtotime(date("Y") . '-' . date("m") . '-' . $reset_day);
                    $final_reset_date = date("Y-m-d", strtotime("-1 month", $reset_date));
                    $current_date = $date;
                    if ($reset_date < strtotime($current_date)) {
                        $final_reset_date = date("Y-m-d", $reset_date);
                    }
                    $counter = "select sum(pdf_page_counter) counter from conversions where server_ip_address = '$s[ip_address]' and created_at >= '$final_reset_date'; ";
                    $counter = self::$pdo_pdf2doc->prepare($counter);
                    $counter->execute();
                    $counter = $counter->fetch();
                    $raspuns[] = [
                        'nr_pagini' => $counter['counter']
                        , 'server' => $s['ip_address']
                    ];
                }

                return $raspuns;
            } else {
                throw new PDOException("PDO connection is not initialized");
            }
        } catch (PDOException $e) {
            return "Error selecting data! " . $e->getMessage();
        }
    }


    public function getThisMonthConversions()
    {
        global $date;
        try {
            if (self::$pdo_pdf2doc instanceof PDO) {
                $check_server = self::$pdo_pdf2doc->prepare("SELECT * FROM servers where ip_address in ('***********', '**********');");
                $check_server->execute();
                $select = $check_server->fetchAll(PDO::FETCH_ASSOC);

                $raspuns = [];
                foreach ($select as $s) {
                    $reset_day = $s['counter_reset_day'];
                    $reset_date = strtotime(date("Y") . '-' . date("m") . '-' . $reset_day);
                    $final_reset_date = date("Y-m-d", strtotime("-1 month", $reset_date));
                    $current_date = $date;
                    if ($reset_date < strtotime($current_date)) {
                        $final_reset_date = date("Y-m-d", $reset_date);
                    }
                    $counter = "select count(*) counter from conversions where server_ip_address = '$s[ip_address]' and created_at >= '$final_reset_date'; ";
                    $counter = self::$pdo_pdf2doc->prepare($counter);
                    $counter->execute();
                    $counter = $counter->fetch();
                    $raspuns[] = [
                        'nr_conversii' => $counter['counter']
                        , 'server' => $s['ip_address']
                    ];
                }

                return $raspuns;
            } else {
                throw new PDOException("PDO connection is not initialized");
            }
        } catch (PDOException $e) {
            return "Error selecting data! " . $e->getMessage();
        }
    }


    public function getPerioada()
    {
        $select = "select year(c.created_at) an, month(c.created_at) luna from conversions c
        where created_at >= '2024-07-01' and server_ip_address = '**********' group by an, luna;";
        $select = self::$pdo_pdf2doc->prepare($select);
        $select->execute();
        $select = $select->fetchAll();

        $maxIndex = count($select) - 1;
        $lunaStart = $select[0]['luna'];
        $lunaSfarsit = $select[$maxIndex]['luna'];
        $anStart = $select[0]['an'];
        $anSfarsit = $select[$maxIndex]['an'];

        return [
            'lunaInceput' => $lunaStart
            , 'anInceput' => $anStart
            , 'lunaSfarsit' => $lunaSfarsit
            , 'anSfarsit' => $anSfarsit
        ];
    }

    public function getNrConvertiri()
    {
        $select = "select year(c.created_at) an, month(c.created_at) luna, sum(c.pdf_page_counter) nrPagini, count(*) nrDoc
        from conversions c
        where created_at >= '2024-07-01' and server_ip_address in ('***********', '**********')
        group by an, luna
        order by an desc, luna desc";
        $select = self::$pdo_pdf2doc->prepare($select);
        $select->execute();
        $select = $select->fetchAll();

        $return = [];
        foreach ($select as $s) {
            $an = $s['an'];
            $luna = $s['luna'];
            $nrPagini = $s['nrPagini'];
            $nrDoc = $s['nrDoc'];

            $return[$an][$luna]['nrPagini'] = $nrPagini;
            $return[$an][$luna]['nrDoc'] = $nrDoc;
        }

        return $return;
    }

}

?>
