<?php
include '../inc/cfg_functions.php';
global $pdo, $session_uid, $date;
header('Content-type: application/json');
$status = $message = null;

if (isset($_POST['changePass']) && $_POST['changePass']) {
    $userName = injCaseSensitive($_POST['userName']) ?? null;
    $changePwForce = ((int) $_POST['changePwForce']) ?? 0;
    $lucrare = injCaseSensitive($_POST['lucrare']) ?? null;
    $dataLucrare = injCaseSensitive($_POST['dataLucrare']) ?? null;
    $metodaComunicare = injCaseSensitive($_POST['metodaComunicare']) ?? null;
    $ordonator = injCaseSensitive($_POST['ordonator']) ?? null;
    if ($userName == null) {
        $status = 'error';
        $message .= 'Completați username-ul.<br>';
    }
    if ($lucrare == null) {
        $status = 'error';
        $message .= 'Completați numărul lucrării.<br>';
    }
    if ($dataLucrare == null) {
        $status = 'error';
        $message .= 'Alegeți data lucrării.<br>';
    }
    if (
        !isset($metodaComunicare) || $metodaComunicare == NULL
        && (!str_contains($metodaComunicare, '@"') && !sanitize_email($metodaComunicare)
            && !filter_var($metodaComunicare, FILTER_VALIDATE_EMAIL) && !formatNumarTelefon($metodaComunicare))
    ) {
        $status = 'error';
        $message .= 'Completați numărul de telefon sau e-mailul alternativ.<br>';
    }
    if ($ordonator == null) {
        $status = 'error';
        $message .= 'Alegeți ordonatorul de unde provine userul.<br>';
    }

    if ($message == null) {
        $lucrare = "$lucrare/$dataLucrare";
        $ldapReturn = resetPWLDAPS($userName, $metodaComunicare, $lucrare, $ordonator, $changePwForce);

        $status = $ldapReturn['statusLDAP'];
        $message = $ldapReturn['messageLDAP'];
    }
}

if (isset($_POST['getSmsStat'])) {
    $select = "select * from mj.ad_reset_password where metoda_comunicare = 'sms' order by moment desc limit 5";
    $select = $pdo->query($select)->fetchAll();
    $status = 'ok';
    foreach ($select as $s) {
        $message .= "
        <tr>
            <td>$s[user_ad]</td>
            <td>$s[lucrare]</td>
            <td>$s[comunicat_catre]</td>
        </tr>";
    }
}

if (isset($_POST['getEmailStat'])) {
    $select = "select * from mj.ad_reset_password where metoda_comunicare = 'email' order by moment desc limit 5";
    $select = $pdo->query($select)->fetchAll();
    $status = 'ok';
    foreach ($select as $s) {
        $message .= "
        <tr>
            <td>$s[user_ad]</td>
            <td>$s[lucrare]</td>
            <td>$s[comunicat_catre]</td>
        </tr>";
    }
}

if (isset($_POST['getExportYears'])) {
    $select = "select year(moment) an from mj.ad_reset_password group by year(moment) order by an desc;";
    $select = $pdo->query($select)->fetchAll();
    $status = 'ok';
    foreach ($select as $s) {
        $message[] = $s['an'];
    }
}


if (isset($_POST['getExportData'])) {
    $an = (int)$_POST['anulDorit'] ?? 0;
    if ($an == 0) {
        $status = 'error';
        $message .= 'Alegeți anul pentru export.<br>';
    }
    if ($message == null) {
        $select = "select arp.*, di.email sp_it_dti from mj.ad_reset_password arp
        join mj.dti_ip di on di.ip = arp.client_ip
        where year(moment) = '$an'order by moment desc;";
        $select = $message = $pdo->query($select)->fetchAll();
        $status = 'ok';
    }
}

echo json_encode
([
    'status' => $status,
    'message' => $message
]);
exit;
