<?php
include '../inc/cfg_functions.php';
global $pdo, $session_uid, $session_id_structura, $session_id_instanta_subordonata, $session_functie, $date;
header('Content-type: application/json');
$status = $message = null;

$selectStructuri = "SELECT * FROM sp_it.z_instante order by id_nivel, id_instanta_sup, den;";
//echo $selectStructuri;
$selectStructuri = $pdo->query($selectStructuri)->fetchAll();

if (isset($_POST['changeVal'])) {
    $tabel = injSql($_POST['tabel']) ?? null;
    $id_rowBaza = injSql($_POST['id_rowBaza']);
    $coloana = injSql($_POST['coloana']);
    $pk = injSql($_POST['pk']);
    $valoare = [];
    foreach ($_POST['input'] as $in) {
        $valoare[] = injSql($in);
    }

    if ($tabel == null) {
        $status = 'error';
        $message .= 'Eroare tabel!';
    }
    if ($coloana == null) {
        $status = 'error';
        $message .= 'Eroare coloana!';
    }
    if ($id_rowBaza == 0) {
        $status = 'error';
        $message .= 'Eroare rand baza de date!';
    }

    if ($message == null) {
        $stmt = "select id as id from sp_it.$tabel where id = '$id_rowBaza'";
        if ($tabel == 'carte_telefon') {
            $stmt = "select id_instanta as id from sp_it.$tabel where id_carte_telefon = '$id_rowBaza'";
        }
        $stmt = $pdo->query($stmt);
        $row = $stmt->fetch();
        if (($row['id'] != $session_id_structura || !in_array($row['id'], $session_id_instanta_subordonata)) && $session_functie != 'Specialist IT Șef') {
            echo json_encode(['status' => 'error', 'message' => "Nu aveți permisiunea de a edita această înregistrare!"]);
            exit;
        }

        $update = "UPDATE sp_it.$tabel SET $coloana = :valoare WHERE $pk = :id";
        $update = $pdo->prepare($update);
        $update->execute(['valoare' => $valoare[0], 'id' => $id_rowBaza]);

        $audit = "INSERT INTO sp_it.audit (id_row, tabela, camp, val_finala, uid_operare, data_operare, obs) VALUES 
      ('$id_rowBaza', 'carte_telefon', '$coloana', '$valoare[0]', '$session_uid', '$date', 'Schimbata valoarea campului');";
        $audit = $pdo->exec($audit);

        $status = 'ok';
        $message = "Succes!";
    }

    echo json_encode(['status' => $status, 'message' => $message]);
    exit;
}


if (isset($_POST['modifStructura']) && $_POST['modifStructura']) {
    $tabel = injCaseSensitive($_POST['tabel']) ?? null;
    $pk = injCaseSensitive($_POST['pk']) ?? null;
    $id_rowBaza = injCaseSensitive($_POST['id_rowBaza']) ?? 0;

    if ($tabel == null) {
        $status = 'error';
        $message .= 'Eroare tabel!';
    }
    if ($id_rowBaza == 0) {
        $status = 'error';
        $message .= 'Eroare rand baza de date!';
    }

    if ($message == null) {
        $tr = null;
        $select = "
        select * from sp_it.carte_telefon ct 
            left join sp_it.z_instante zi on ct.id_instanta = zi.id
            left join sp_it.z_functii zf on zf.idz_functii = ct.id_functie
        where ct.id_instanta = '$id_rowBaza' and ct.delete_uid = 0 and ct.delete_date is null
        order by ct.nume;";
//        echo $select;exit;
        $select = $pdo->query($select);
        foreach ($select as $s) {
            $carteTelIdPersoana = $s['id_carte_telefon'];
            $carteTelNume = $s['nume'];
            $carteTelIdStructura = $s['id_instanta'];
            $carteteltel_interior = $s['tel_interior'];
            $carteteltel_mobil = $s['tel_mobil'];
            $carteteltel_fax = $s['tel_fax'];
            $cartetelemail = $s['email'];

            $tr .= "
            <tr data-structure-id='$carteTelIdStructura'>
                <td colspan='7'>$carteTelNume</td>
                <td><button class='btn btn-sm btn-outline-danger persoanaDel' idPersoana='$carteTelIdPersoana' numePersoana='$carteTelNume' data-toggle='tooltip' title='Sterge persoana din cartea de telefon'><img src='assets/icons/trash.svg' alt='Delete'> sterge persoana</button></td>
            </tr>";

        }

        $tr = "
        <table class='table table-bordered table-contextual' style='text-align: center;'>
            <thead>
                <tr>
                    <th colspan='7'>Nume Prenume</th>
                    <th><button class='btn btn-sm btn-outline-primary persoanaAddForm' idStructura='$id_rowBaza' data-toggle='tooltip' title='Adauga persoana'><img src='assets/icons/plus.svg' alt='Plus'> persoana noua</button></th>
                </tr>
            </thead>
            <tbody>$tr</tbody>
        </table>";

        $status = 'ok';
        $message = $tr;
    }
}
if (isset($_POST['persoanaAddForm']) && $_POST['persoanaAddForm']) {
    $idInstanta = injCaseSensitive($_POST['idStructura']) ?? 0;
    $structura = null;
    foreach ($selectStructuri as $sS) {
        if ($sS['id'] == $idInstanta) {
            $structura = $sS['den'];
        }
    }

    global $pdo;
    $selectFunctii = "select * from sp_it.z_functii order by functie";
    //echo $selectFunctii;
    $selectFunctii = $pdo->query($selectFunctii)->fetchAll();

    $message = "
     <tr>
        <td><input type='text' class='form-control nume' placeholder='Nume si prenume'></td>
        <td>
            <select class='form-control functie' coloana='id_functie'>";
    foreach ($selectFunctii as $sF) {
        $message .= "<option value='$sF[idz_functii]'>$sF[functie]</option>";
    }
    $message .= "
            </select>
        </td>
        <td><input type='text' class='form-control interior' placeholder='Interior'></td>
        <td><input type='text' class='form-control mobil' placeholder='Mobil'></td>
        <td><input type='text' class='form-control fax' placeholder='Fax'></td>
        <td><input type='text' class='form-control email' placeholder='E-mail'></td>
        <td><input type='text' class='form-control obs' placeholder='Observatii'></td>
        <td><button class='btn btn-outline-primary persoanaAddBtn' idInstanta='$idInstanta'><img src='assets/icons/check.svg' alt='Adauga' title='Adauga persoana'></button></td>
    </tr>";
}
if (isset($_POST['delPersoana']) && $_POST['delPersoana']) {
    if ($session_functie != 'Specialist IT Șef') {
        echo json_encode(['status' => 'error', 'message' => "Nu aveți permisiunea de a edita această înregistrare!"]);
        exit;
    }

    $idPersoana = injCaseSensitive($_POST['idPersoana']) ?? 0;
    $update = "update sp_it.carte_telefon set delete_uid = '$session_uid', delete_date = '$date' where id_carte_telefon = '$idPersoana';";
//    echo $update;
    $update = $pdo->exec($update);

    $audit = "INSERT INTO sp_it.audit (id_row, tabela, camp, uid_operare, data_operare, obs) VALUES 
          ('$idPersoana', 'carte_telefon', 'delete_uid', '$session_uid', '$date', 'Stearsa persoana $idPersoana');";
    $audit = $pdo->exec($audit);

    $message = "Persoană ștearsă din cartea de telefon!";
    $status = 'ok';
}
if (isset($_POST['persoanaAddBtn']) && $_POST['persoanaAddBtn']) {
    if ($session_functie != 'Specialist IT Șef') {
        echo json_encode(['status' => 'error', 'message' => "Nu aveți permisiunea de a edita această înregistrare!"]);
        exit;
    }

    $idInstanta = injCaseSensitive($_POST['idInstanta']) ?? 0;
    $nume = $_POST['nume'] ?? null;
    $functie = $_POST['functie'] ?? null;
    $interior = $_POST['interior'] ?? null;
    $mobil = $_POST['mobil'] ?? null;
    $fax = $_POST['fax'] ?? null;
    $email = $_POST['email'] ?? null;
    $obs = $_POST['obs'] ?? null;

    if ($nume == null) {
        $message = "Completati numele!";
        $status = 'error';
    }


    if ($message == null) {
        $insert = "insert into sp_it.carte_telefon (id_instanta, nume, id_functie, tel_interior, tel_mobil, tel_fax, email, observatii) 
values ($idInstanta, '$nume', '$functie', '$interior', '$mobil', '$fax', '$email', '$obs');";
//        echo $insert;
        $insert = $pdo->exec($insert);
        $idPersoana = $pdo->lastInsertId();

        $audit = "INSERT INTO sp_it.audit (id_row, tabela, camp, val_finala, uid_operare, data_operare, obs) VALUES 
          ('$idPersoana', 'carte_telefon', 'id_carte_telefon', '$nume', '$session_uid', '$date', 'Persoana $idPersoana adaugata in cartea de telefon, in instanta $idInstanta');";
        $audit = $pdo->exec($audit);

        $message = "Adaugat";
        $status = 'ok';
    }
}
if (isset($_POST['selectFiltruFunctie']) && $_POST['selectFiltruFunctie']) {
    $stmt = "SELECT zf.idz_functii id, zf.functie type FROM sp_it.z_functii zf group by zf.idz_functii order by zf.functie";
    $stmt = $pdo->query($stmt)->fetchAll();
    foreach ($stmt as $s) {
        $message .= "<option value='$s[id]'>$s[type]</option>";
    }
    $status = 'ok';
}

echo json_encode
([
    'status' => $status,
    'message' => $message
]);
exit;
