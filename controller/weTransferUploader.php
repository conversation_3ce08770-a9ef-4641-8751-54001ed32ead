<?php
//ini_set('display_errors', 1);
//ini_set('display_startup_errors', 1);
//error_reporting(E_ALL);
//ob_flush();flush();

set_time_limit(600);
require_once '../inc/cfg_session.php';
require_once '../inc/cfg_functions.php';
require '../vendor/autoload.php';
global $project, $semnaturaDTI, $date;

(isset($_POST['email'])) ? $email = $_POST['email'] : $email = '<EMAIL>';
if ($email == null && !isset($_POST)) {
    exit("Lipsa email");
}
if (str_contains($email, '@') && sanitize_email($email) && filter_var($email, FILTER_VALIDATE_EMAIL)) {
    $email = substr($email, 0, strpos($email, "@")) . "@just.ro";
} else {
    $email = "$<EMAIL>";
}
if (!empty($_FILES)) {
    $containsInputFile = false;
    $nrFiles = count($_FILES);
    if ($nrFiles > 500) {
        echo "<h2 style='color:red'>Ați încărcat mai mult de 500 de fișiere!</h2><h1 style='color:red'>Arhivați fișierele și încărcați arhiva, apoi reîncercați.</h1>";
        sendEmail('<EMAIL>', 'Upload peste 500 de fisiere', "Persoana $email a incercat sa incarce mai mult de 500 de fisiere", 0);
        exit;
    }
    foreach ($_FILES as $key => $value) {
        if (strpos($key, 'inputFile') === 0) {
            $containsInputFile = true;
            break;
        } else {
            var_dump($_FILES);
            echo "<h2 style='color:red'>Lipsă fișiere transmise/încărcate - cheie inputFile</h2>";
            exit;
        }
    }
} else {
    var_dump($_FILES);
    echo "<h2 style='color:red'>Lipsă fișiere transmise/încărcate</h2>";
    exit;
}

if (isset($_POST['uploadFiles'])) {
	$totalSize = 0;
	foreach ($_FILES as $f) {
		$totalSize += $f['size'];
	}
    $error = 0;
    $message = null;
    $vecFiles = $zipFiles = [];
    $fisiereString = '';
    $i = 0;
    foreach ($_FILES as $f) {
//        echo("<br>fisierul<br>");exit(var_dump($f)."<br>totalSize $totalSize");
        $tempFile = $f['tmp_name'];
        $numeFisier = strtolower(str_replace([' ', '(', ')'], '_', $f['name']));
        $size = $f['size'];

        if ($totalSize >= 2 * 1024 * 1024 * 1024) {
            $error++;
            $message .= "Fisier(e) cu dimensiunea peste 2 GB.<br>";
			writeToCronFile($message);
            echo $text;
			return;
        }
		if ($totalSize < (20 * 1024 * 1024)) {
            //Fisier cu dimensiunea sub 20MB
            $text = "Fisier(e) cu dimensiunea sub 20MB.<br>Folositi e-mailul pentru trimitere.";
            writeToCronFile($text);
            echo $text;
            return;
        }

        if (!is_dir($email)) {
            mkdir($email);
        }

        $targetPath = $_SERVER['DOCUMENT_ROOT'] . "/controller/$email/";
        $targetFile = $targetPath . time() . '-' . $numeFisier;

        if (move_uploaded_file($tempFile, $targetFile)) {
            $fisiereString .= $targetPath . "$numeFisier ";
            $zipFiles[] = $targetFile;
        } else {
            var_dump($_FILES);
            $error++;
            $message .= "Fisierul nu poate fi mutat.<br>";
        }

        $vecFiles[$i]['file_name'] = $numeFisier;
        $vecFiles[$i]['file_size'] = $size;
        $vecFiles[$i]['target_file'] = $targetFile;
        $i++;
    }

    if ($error == 0) {
        $zipName = strtolower(str_replace([' ', '(', ')', ':'], '-', "Documente-arhiva-$date.zip"));
//        $zip = createZipArchive($zipFiles, $zipName, "controller/$email");
        createZipArchive($zipFiles, $zipName, "controller/$email", function ($zipName) use ($email) {
            rename($zipName, "$email/$zipName");
        });
//        rename($zipName, "$email/$zipName");
//        var_dump($vecFiles); exit("<br>fisierele<br>");


        $output = [];
        $returnCode = null;
        $scriptFile = "uploadScript.py";
        exec("icacls $scriptFile /grant Everyone:(RX)", $output, $returnCode);
        $command = "python $scriptFile upload $email/$zipName";
        $out = shell_exec("$command 2>&1");
//        echo $command;

        if (empty($out)) {
            $text = "-- Eroare incarcare WeTransfer<br>";
            writeToCronFile($text);
            echo $text;
        } else {
            $continut = "<p>Incarcarea fisierelor este finalizata.</p>
            <p>Link-ul pe care dumneavoastra il comunicati destinatarului este: 
            <b class='copyToClipboard' style='color: blue; cursor: pointer;'>$out</b> - <b>click pe link pentru copiere in clipboard</b></p>";
            $suffix = "<p style='color: blue;'>Deasemenea veti primi link-ul si pe adresa de e-mail $email</p>";
            writeToCronFile($continut . $suffix);
            echo $continut . $suffix;
            echo "<small style='font-size: 10px;'>";
//            removeDir($email);
            queueEmail($email, 'Link-ul de descarcare WeTransfer', $continut);
            processEmailQueue();
            echo "</small>";

            insertSIDinDBUploader($email, $out, $vecFiles);
        }
    } else {
        echo $message;
    }

}


function insertSIDinDBUploader($email, $link_generat, $vecFiles)
{
    global $pdo, $date, $client_ip, $client_port, $client_device, $client_device_tip, $PHPSESSID;
    $insert = $pdo->prepare("INSERT INTO `scan`.`upload_user`
    (`email`, `link_generat`, `phpsessid`, `client_ip`, `client_port`, `client_device`, `client_device_tip`, `timestamp`)
    VALUES (:email, :link_generat, :phpsessid, :client_ip, :client_port, :client_device, :client_device_tip, :timestamp)");
    $insert->execute([
        ':email' => $email
        , ':link_generat' => $link_generat
        , ':phpsessid' => $PHPSESSID
        , ':client_ip' => $client_ip
        , ':client_port' => $client_port
        , ':client_device' => $client_device
        , ':client_device_tip' => $client_device_tip
        , ':timestamp' => $date
    ]);
    $lastID = $pdo->lastInsertId();

    foreach ($vecFiles as $v) {
        $file_name = $v['file_name'];
        $file_size = $v['file_size'];
        $target_file = $v['target_file'];
        $insertFiles = $pdo->prepare("INSERT INTO `scan`.`upload_files`
        (`file_name`, `file_size`, `target_file`, `idupload_user`) VALUES (:file_name, :file_size, :target_file, :idupload_user)");
        $insertFiles->execute([
            ':file_name' => $file_name
            , ':file_size' => $file_size
            , ':target_file' => $target_file
            , ':idupload_user' => $lastID
        ]);
    }
}


?>
