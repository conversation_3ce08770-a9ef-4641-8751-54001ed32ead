<?php
require_once '../inc/cfg_functions.php';

$data = [];
$draw = $_POST['draw'];
$row = $_POST['start'];
$rowperpage = $_POST['length'];
($rowperpage == 10) ? $rowperpage = 30 : $rowperpage;
($rowperpage > 0) ? $limit = " limit $row,$rowperpage" : $limit = null;
$columnIndex = 0;
$columnName = $columnSortOrder = null;
if (isset($_POST['order'])) {
    $columnIndex = $_POST['order'][0]['column'];
    $columnName = $_POST['columns'][$columnIndex]['data'];
    $columnSortOrder = $_POST['order'][0]['dir']; // asc or desc
}
if (intval($draw) == 1) {
    $columnIndex = 0;
    $columnName = $columnSortOrder = null;
}

## Search
$searchQuery = '';
$searchValue = str_replace(['\\', '&amp;', '&amp'], '', inj($_POST['search']['value']));
if ($searchValue != '') {
    // Define fields to search in
    $mainFields = ['nume', 'email'];
    $secondaryFields = ['tel_interior', 'tel_mobil', 'tel_fax'];

    // Use the enhanced search function with strict mode and precise mode for main search
    list($searchConditions, $mainSearchQuery) = enhancedSearch($searchValue, 'ct.', $mainFields, $secondaryFields, true, true);

    // Also search in related tables
    $institutionFields = ['den'];
    list($institutionConditions, $institutionSearchQuery) = enhancedSearch($searchValue, 'zi.', $institutionFields, [], false);

    $countyFields = ['judet'];
    list($countyConditions, $countySearchQuery) = enhancedSearch($searchValue, 'zj.', $countyFields, [], false);

    // Combine all search conditions
    $allConditions = array_merge($searchConditions, $institutionConditions, $countyConditions);

    if (!empty($allConditions)) {
        $searchQuery .= " AND (" . implode(' OR ', $allConditions) . ")";
    }
}

## Search filters
$numeFilter = str_replace(['\\', '&amp;', '&amp'], '', inj($_POST['columns'][0]['search']['value']));
if ($numeFilter != '') {
    // Use the enhanced search function for name column search
    // We use strict mode and precise mode to make the search more accurate
    $mainFields = ['nume', 'email'];
    list($searchConditions, $columnSearchQuery) = enhancedSearch($numeFilter, 'ct.', $mainFields, [], true, true);

    if (!empty($columnSearchQuery)) {
        $searchQuery .= $columnSearchQuery;
    }
}
$functieFilter = str_replace(['\\', '&amp;', '&amp'], '%', inj($_POST['columns'][1]['search']['value']));
if ($functieFilter != '') {
    $searchQuery .= " and zf.idz_functii = '$functieFilter' ";
}
$interiorFilter = str_replace(['\\', '&amp;', '&amp'], '%', inj($_POST['columns'][2]['search']['value']));
if ($interiorFilter != '') {
    $searchQuery .= " and ct.tel_interior like '%$interiorFilter%' ";
}
$mobilFilter = str_replace(['\\', '&amp;', '&amp'], '%', inj($_POST['columns'][3]['search']['value']));
if ($mobilFilter != '') {
    $searchQuery .= " and ct.tel_mobil like '%$mobilFilter%' ";
}
$faxFilter = str_replace(['\\', '&amp;', '&amp'], '%', inj($_POST['columns'][4]['search']['value']));
if ($faxFilter != '') {
    $searchQuery .= " and ct.tel_fax like '%$faxFilter%' ";
}

## Custom click sort
if ($columnName == 'numePrenume') {
    $columnName = 'zi.id_nivel, zi.id_instanta_sup, ct.id_instanta, ct.nume';
} elseif ($columnName == 'functie') {
//    $columnName = 'zo.idz_ordonatori, zs.prioritate, zf.functie';
    $columnName = 'ct.id_instanta, zi.id_nivel, zi.id_instanta_sup, ct.id_instanta, zf.prioritate';
} elseif ($columnName == 'telFix') {
    $columnName = 'zi.id_nivel, zi.id_instanta_sup, ct.id_instanta, ct.tel_interior';
} elseif ($columnName == 'telMobil') {
    $columnName = 'zi.id_nivel, zi.id_instanta_sup, ct.id_instanta, ct.tel_mobil';
} elseif ($columnName == 'telFax') {
    $columnName = 'zi.id_nivel, zi.id_instanta_sup, ct.id_instanta, ct.tel_fax';
} elseif ($columnName == 'email') {
    $columnName = 'zi.id_nivel, zi.id_instanta_sup, ct.id_instanta, ct.email';
} elseif ($columnName == 'observatii') {
    $columnName = 'zi.id_nivel, zi.id_instanta_sup, ct.id_instanta, ct.observatii';
} else {
    $columnName = 'zi.id_judet, zi.den';
}

global $pdo;
$selectFunctii = "select * from sp_it.z_functii zf order by functie";
$selectFunctii = $pdo->query($selectFunctii)->fetchAll();

## Query
$query_string = "
SELECT ct.*
, zf.functie spITfunctie
, zi.id instantaID, zi.den instantaDen, zi.email emailInstanta, zi.observatii obsInstanta
, zj.*";

$from = "
from sp_it.carte_telefon ct
    left join sp_it.z_functii zf on zf.idz_functii = ct.id_functie
    left join sp_it.z_instante zi on zi.id = ct.id_instanta
    left join sp_it.z_judete zj on zj.cod_judet = zi.id_judet
where ct.delete_uid = 0 and ct.delete_date is null
    $searchQuery
    order by $columnName $columnSortOrder";

$fromClean = "
from sp_it.carte_telefon ct
    left join sp_it.z_functii zf on zf.idz_functii = ct.id_functie
    left join sp_it.z_instante zi on zi.id = ct.id_instanta
    left join sp_it.z_judete zj on zj.cod_judet = zi.id_judet
where ct.delete_uid = 0 and ct.delete_date is null";

//echo nl2br($query_string . $from);exit;
$query = $pdo->query($query_string . $from . $limit)->fetchAll();

## Total number of records
$totalRecords = "select count(*) nr $fromClean ";
//exit($totalRecords);
$totalRecords = $pdo->query($totalRecords)->fetch();
$totalRecords = $totalRecords['nr'];

## Total number of records with filtering
$totalRecordwithFilter = "select count(*) nr $from";
//exit($totalRecordwithFilter);
$totalRecordwithFilter = $pdo->query($totalRecordwithFilter)->fetch();
$totalRecordwithFilter = $totalRecordwithFilter['nr'];


$numePrenumeTD = $functieTD = $telFixTD = $telMobilTD = $telFaxTD = $emailTD = $observatiiTD = null;
$ultimulOrdID = $ultimaStructID = 0;
foreach ($query as $q) {
    $carteTelId = $q['id_carte_telefon'];
    $ordID = $q['instantaID'];
    $ordDen = ucwords($q['instantaDen']);
    $ordEmail = $q['emailInstanta'];
    $ordObservatii = $q['obsInstanta'];

    if ($ultimulOrdID != $ordID) {
        $ordDenTD = "<input type='hidden' tabel='z_instante' pk='id' id_row='$ordID'>
        <b>$ordDen</b>";
        $ordEmailTD = "<b>$ordEmail</b>
        <div><input type='text' class='ascuns form-control' tabel='z_instante' pk='id' coloana='email' id_row='$ordID' value='$ordEmail'></div>";
        $ordObservatiiTD = "$ordObservatii
        <div><input type='text' class='ascuns form-control' tabel='z_instante' pk='id' coloana='observatii' id_row='$ordID' value='$ordObservatii'></div>";

        $data[] = [
            'numePrenume' => $ordDenTD,
            'functie' => null,
            'telFix' => null,
            'telMobil' => null,
            'telFax' => null,
            'email' => $ordEmailTD,
            'observatii' => $ordObservatiiTD,
            'id_structura' => $ordID
        ];
        $ultimulOrdID = $ordID;
    }

    $carteTelNume = isset($q['nume']) ? htmlspecialchars($q['nume']) : '-';
    $carteTelTel_interior = $q['tel_interior'];
    $carteTelTel_mobil = $q['tel_mobil'];
    $carteTelTel_fax = $q['tel_fax'];
    $carteTelEmail = $q['email'];
    $carteTelObservatii = $q['observatii'];
    $carteTelIdStructura = $q['instantaID'];
    $carteTelIdFunctie = 'id_functie';
    $carteTelDenFunctie = $q['spITfunctie'];

    $sexPersoana = sexPersoana($carteTelNume);
    $iconClass = ($sexPersoana == 'F') ? 'fa-female fs-4' : 'fa-user-alt fs-6';
    $colorType = ($sexPersoana == 'F') ? 'lightcoral' : 'royalblue';
    $carteTelNumePrenumeTD = "
    <div class='d-flex align-items-center'>
        <i class='fas $iconClass' style='margin-right: 0.3em; color: $colorType;'></i>
        <span class='fw-medium'>
            $carteTelNume
        </span>
    </div>
    <div><input type='text' class='ascuns' tabel='carte_telefon' pk='id_carte_telefon' coloana='nume' id_row='$carteTelId' value='$carteTelNume'></div>";

    $carteTelFunctieTD = "$carteTelDenFunctie
    <div>
        <select class='ascuns' tabel='carte_telefon' pk='id_carte_telefon' coloana='id_functie' id_row='$carteTelId'>";
            foreach ($selectFunctii as $sF) {
                ($sF['idz_functii'] == $carteTelIdFunctie) ? $selected = "selected='selected'" : $selected = null;
                $carteTelFunctieTD .= "<option value='$sF[idz_functii]' $selected>$sF[functie]</option>";
            }
            $carteTelFunctieTD .= "
        </select>
    </div>";

    $carteTelTelFixTD = "$carteTelTel_interior
    <div><input type='text' class='ascuns' tabel='carte_telefon' pk='id_carte_telefon' coloana='tel_interior' id_row='$carteTelId' value='$carteTelTel_interior'></div>";
    $carteTelTelMobilTD = "$carteTelTel_mobil
    <div><input type='text' class='ascuns' tabel='carte_telefon' pk='id_carte_telefon' coloana='tel_mobil' id_row='$carteTelId' value='$carteTelTel_mobil'></div>";
    $carteTelTelFaxTD = "$carteTelTel_fax
    <div><input type='text' class='ascuns' tabel='carte_telefon' pk='id_carte_telefon' coloana='tel_fax' id_row='$carteTelId' value='$carteTelTel_fax'></div>";
    $carteTelEmailTD = "$carteTelEmail
    <div><input type='text' class='ascuns' tabel='carte_telefon' pk='id_carte_telefon' coloana='email' id_row='$carteTelId' value='$carteTelEmail'></div>";
    $carteTelObservatiiTD = "$carteTelObservatii
    <div><input type='text' class='ascuns' tabel='carte_telefon' pk='id_carte_telefon' coloana='observatii' id_row='$carteTelId' value='$carteTelObservatii'></div>";

    $data[] = [
        'numePrenume' => $carteTelNumePrenumeTD,
        'functie' => $carteTelFunctieTD,
        'telFix' => $carteTelTelFixTD,
        'telMobil' => $carteTelTelMobilTD,
        'telFax' => $carteTelTelFaxTD,
        'email' => $carteTelEmailTD,
        'observatii' => $carteTelObservatiiTD,
        'id_structura' => $carteTelIdStructura
    ];


}


## Response
$response = [
    "draw" => intval($draw),
    "iTotalRecords" => $totalRecords,
    "iTotalDisplayRecords" => $totalRecordwithFilter,
    "aaData" => $data
];

echo json_encode($response);

?>