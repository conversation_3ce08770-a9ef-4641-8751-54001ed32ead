<?php
include '../inc/cfg_functions.php';

global $pdo, $session_uid, $date, $client_ip, $client_port, $client_device, $client_device_tip;
header('Content-type: application/json');
$status = $message = null;

if (isset($_POST['sendSMS']) && $_POST['sendSMS']) {
    $parola = injCaseSensitive($_POST['parola']) ?? null;
    $smsDest = injCaseSensitive($_POST['smsDest']) ?? null;
    $nume_persoana = injCaseSensitive($_POST['nume_persoana']) ?? null;
    $message = $status = null;

    if ($parola == null) {
        $status = 'error';
        $message .= 'Completați parola.<br>';
    }
    if ($nume_persoana == null) {
        $status = 'error';
        $message .= 'Completați numele persoanei.<br>';
    }
    if (
        (!isset($smsDest) || $smsDest == NULL || $nume_persoana == NULL)
        && (!filter_var($smsDest, FILTER_VALIDATE_EMAIL) && !formatNumarTelefon($smsDest))
    ) {
        $status = 'error';
        $message .= 'Completați numărul de telefon.<br>';
    }

    if ($message == null) {
        //sms
        $numarTelefon = formatNumarTelefon($smsDest);
        $sms_message = "Parola pentru conectarea clientului FortiClient la serverul FortiEMS este următoarea: $parola Având în vedere caracterul sensibil al acestei parole, vă rugăm să rețineți că aceasta are un caracter confidențial și responsabilitatea păstrării acestuia cade în sarcina persoanei căreia i-a fost încredințată";
        $scriptFile = "C:\\wamp64\\www\\sendsms_1.exe";

        $output = [];
        $returnCode = null;
        exec("icacls $scriptFile /grant Everyone:(RX)", $output, $returnCode);
        $command = "powershell.exe C:\\wamp64\\www\\sendsms_1.exe -m '$sms_message' -nr '$numarTelefon' 2>&1";
        $out = shell_exec($command);
//        exit($out);


        $audit = $pdo->prepare("INSERT INTO `mj`.`dti_sms`
(`sms_dest`, `text`, `nume`, `client_ip`, `client_port`, `client_device`, `client_device_tip`, `moment`)
VALUES (:sms_dest, :text, :nume, :client_ip, :client_port, :client_device, :client_device_tip, :moment)");
        $audit->execute([
            ':sms_dest' => $numarTelefon
            , ':text' => $sms_message
            , ':nume' => $nume_persoana
            , ':client_ip' => $client_ip
            , ':client_port' => $client_port
            , ':client_device' => $client_device
            , ':client_device_tip' => $client_device_tip
            , ':moment' => $date
        ]);
        $message = "Parola a fost transmisă cu succes!";
        $status = 'ok';

    }
}

if (isset($_POST['getSmsStat'])) {
    $select = "SELECT * FROM `mj`.`dti_sms` s join `mj`.`dti_ip` i on s.client_ip = i.ip order by s.moment desc limit 15;";
    $select = $pdo->query($select)->fetchAll();
    $status = 'ok';
    foreach ($select as $s) {
        $message .= "
        <tr>
            <td>$s[persoana]</td>
            <td>$s[sms_dest]</td>
            <td>$s[nume]</td>
            <td>".data_afisare($s['moment'])."</td>
        </tr>";
    }
}

if (isset($_POST['getExportYears'])) {
    $select = "select year(moment) an from mj.dti_sms group by year(moment) order by an desc;";
    $select = $pdo->query($select)->fetchAll();
    $status = 'ok';
    foreach ($select as $s) {
        $message[] = $s['an'];
    }
}

if (isset($_POST['getExportData'])) {
    $an = (int)$_POST['anulDorit'] ?? 0;
    if ($an == 0) {
        $status = 'error';
        $message .= 'Alegeți anul pentru export.<br>';
    }
    if ($message == null) {
        $select = "SELECT s.*, i.ip, i.persoana FROM `mj`.`dti_sms` s 
        join `mj`.`dti_ip` i on s.client_ip = i.ip 
        where year(moment) = '$an' order by s.moment desc;";
        $select = $message = $pdo->query($select)->fetchAll();
        $status = 'ok';
    }
}


echo json_encode
([
    'status' => $status,
    'message' => $message
]);
exit;
