<?php
require_once 'cfg_pdo.php';
global $pdo;
$mentenanta = 0;

$DOCUMENT_ROOT = $_SERVER['DOCUMENT_ROOT'];
$domain = $_SERVER['SERVER_NAME'];
$self = $_SERVER['PHP_SELF'];
$base_dir = __DIR__;
$folder_curent = getcwd();
$client_ip = $_SERVER['REMOTE_ADDR'];
$client_port = $_SERVER['REMOTE_PORT'];
$client_device = $_SERVER['HTTP_USER_AGENT'];
$client_device_tip = "D";
$mjappsjustro = "mjapps.just.ro";

$envPaths = ['.env', '../.env', dirname(__DIR__) . '/.env', '/.env'];
foreach ($envPaths as $path) {
    if (file_exists($path)) {
        $env = parse_ini_file($path);
        break;
    }
}
if (!$env) {
    die('Error: .env file not found in the specified paths.');
}


function accesMJApps()
{
    global $client_ip;
    $accessGroups = [
        'MJ+ANABI' => [
            '/^10\.1\.[0-7]\.\d{1,3}$/',  // MJ Aparat Central
            '/^10\.1\.245\.\d{1,3}$/',    // MJ rețea 245
            '/^10\.1\.38\.\d{1,3}$/'      // ANABI
        ]
    ];

    $exceptieIP = ['::1'];
//    $exceptieIP[] = '************'; // pentru manager economic Csilla Izabella Traxler Specialist IT Tribunalul Maramureș - pus si in cfg_menu.php
    $userGroup = in_array($client_ip, $exceptieIP) ? 'MJ+ANABI' : 'OutsideMJ';
    foreach ($accessGroups as $group => $patterns) {
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $client_ip)) {
                $userGroup = $group;
                break 2;
            }
        }
    }

    return $userGroup;
}

$accesMJApps = accesMJApps();


if (!function_exists('isMobileDevice')) {
    function isMobileDevice()
    {
        global $client_device;
        return preg_match("/(android|avantgo|blackberry|bolt|boost|cricket|docomo|fone|hiptop|mini|mobi|palm|phone|pie|tablet|up\.browser|up\.link|webos|wos)/i", $client_device);
    }
}
if (isMobileDevice()) {
    $client_device_tip = 'M';
}

date_default_timezone_set('Europe/Bucharest');
$zi_curenta = date("d");
$luna_curenta = date("m");
$an_curent = date("Y");
$ora_curenta = date("H");
$min_curent = date("i");
$sec_curenta = date("s");
$date = date("Y-m-d H:i:s");

$project = null;
if (in_array($domain, ['**********', 'cartetelefon.just.ro', $mjappsjustro])) {
    $project = null;
}
$cale_sesiune = $DOCUMENT_ROOT . "/$project/assets/vendors/session/autoload.php";
require_once $cale_sesiune;
$session_factory = new \Aura\Session\SessionFactory;
$session = $session_factory->newInstance($_COOKIE);
$sesiune = $session->getSegment('Vendor\Package\ClassName');

if ($sesiune->get('session_uid')) {
    $auth = true;
    $session_uid = $sesiune->get('session_uid');
    $session_nume = isset($session_uid) ? $sesiune->get('session_nume') : null; //2
    $session_prenume = isset($session_uid) ? $sesiune->get('session_prenume') : null; //3
    $session_id_structura = isset($session_uid) ? $sesiune->get('session_id_structura') : null; //4
    $session_structura = isset($session_uid) ? $sesiune->get('session_structura') : null; //5
    $session_email = isset($session_uid) ? $sesiune->get('session_email') : null; //6
    $session_functie = isset($session_uid) ? $sesiune->get('session_functie') : null; //7
    $session_id_instanta_superioara = isset($session_uid) ? $sesiune->get('session_id_instanta_superioara') : null; //8
    $session_id_instanta_subordonata = isset($session_uid) ? $sesiune->get('session_id_instanta_subordonata') : null; //9
    //de modif si login.php logout.php
} else {
    $auth = false;
    $session_uid = $session_nume = $session_prenume = $session_id_structura = $session_structura = $session_email =
    $session_functie = $session_id_instanta_superioara = $session_id_instanta_subordonata = null;

    $sesiune->set('session_uid', NULL);//1
    $sesiune->set('session_nume', NULL);//2
    $sesiune->set('session_prenume', NULL);//3
    $sesiune->set('session_id_structura', NULL);//4
    $sesiune->set('session_structura', NULL);//5
    $sesiune->set('session_email', NULL);//6
    $sesiune->set('session_functie', NULL);//7
    $sesiune->set('session_id_instanta_superioara', NULL);//8
    $sesiune->set('session_id_instanta_subordonata', NULL);//9
}

$semnaturaDTI = "
<br>
<p>Cu stima,</p>
<p>Directia Tehnologia Informatiei</p>
<p style='margin-top:0px;'>Ministerul Justitiei</p>";


function accesDTI()
{
    global $pdo, $client_ip;
    $selectDTI = "SELECT group_concat(ip) ip FROM mj.dti_ip;";
    $selectDTI = $pdo->query($selectDTI)->fetch();
    $selectDTI = explode(',', $selectDTI['ip']);

    if (in_array($client_ip, $selectDTI)) {
        return true;
    }
    return false;
}

function accesSIC()
{
    global $pdo, $client_ip;
    $accessSIC = "SELECT group_concat(ip) ip FROM sic.ip_access;";
    $accessSIC = $pdo->query($accessSIC)->fetch();
    $accessSIC = explode(',', $accessSIC['ip']);

    if (in_array($client_ip, $accessSIC)) {
        return true;
    }
    return false;
}

function getBrowser()
{
    $u_agent = $_SERVER['HTTP_USER_AGENT'];
    $bname = 'Unknown';
    $platform = 'Unknown';
    $version = "";

    // First get the platform
    if (preg_match('/linux/i', $u_agent)) {
        $platform = 'Linux';
    } elseif (preg_match('/macintosh|mac os x/i', $u_agent)) {
        $platform = 'Mac';
    } elseif (preg_match('/windows|win32/i', $u_agent)) {
        $platform = 'Windows';
    }

    $ub = '';
    // Check for the browser name
    if (preg_match('/Edge/i', $u_agent) || preg_match('/Edg/i', $u_agent)) {
        $bname = 'Microsoft Edge';
        $ub = "Edge";
    } elseif (preg_match('/MSIE/i', $u_agent) && !preg_match('/Opera/i', $u_agent)) {
        $bname = 'Internet Explorer';
        $ub = "MSIE";
    } elseif (preg_match('/Firefox/i', $u_agent)) {
        $bname = 'Mozilla Firefox';
        $ub = "Firefox";
    } elseif (preg_match('/Chrome/i', $u_agent) && !preg_match('/Edge/i', $u_agent) && !preg_match('/Edg/i', $u_agent)) {
        $bname = 'Google Chrome';
        $ub = "Chrome";
    } elseif (preg_match('/Safari/i', $u_agent) && !preg_match('/Edge/i', $u_agent) && !preg_match('/Edg/i', $u_agent)) {
        $bname = 'Apple Safari';
        $ub = "Safari";
    } elseif (preg_match('/Opera/i', $u_agent)) {
        $bname = 'Opera';
        $ub = "Opera";
    } elseif (preg_match('/Netscape/i', $u_agent)) {
        $bname = 'Netscape';
        $ub = "Netscape";
    }

    // Get the version number
    $known = array('Version', $ub, 'other');
    $pattern = '#(?<browser>' . join('|', $known) . ')[/ ]+(?<version>[0-9.|a-zA-Z.]*)#';
    if (preg_match_all($pattern, $u_agent, $matches)) {
        // See how many we have
        $i = count($matches['browser']);
        if ($i != 1) {
            // We will have two since we are not using 'other' argument yet
            // See if version is before or after the name
            $version_pos = stripos($u_agent, "Version");
            $ub_pos = $ub !== null ? stripos($u_agent, $ub) : false;

            if ($ub_pos !== false && $version_pos < $ub_pos) {
                $version = isset($matches['version'][0]) ? $matches['version'][0] : "?";
            } else {
                $version = isset($matches['version'][1]) ? $matches['version'][1] : "?";
            }
        } else {
            $version = isset($matches['version'][0]) ? $matches['version'][0] : "?";
        }
    }

    // Check if we have a number
    if ($version == null || $version == "") {
        $version = "?";
    }

    return array(
        'browser' => $bname,
        'version' => $version,
        'platform' => $platform,
        'userAgent' => $u_agent,
    );
}
