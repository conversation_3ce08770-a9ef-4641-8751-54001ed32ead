<?php
require_once 'cfg_session.php';

global $DOCUMENT_ROOT, $project;
require_once $DOCUMENT_ROOT . "/$project/vendor/autoload.php";

use PHPMailer\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\Exception;
use GuzzleHttp\Client;

/**
 * Enhanced search function that handles multiple search terms, different word orders, and hyphenated variations
 *
 * @param string $searchTerm The original search term
 * @param string $fieldPrefix The database field prefix (e.g., 'ct.')
 * @param array $mainFields Primary fields to search in (e.g., ['nume', 'email'])
 * @param array $secondaryFields Optional secondary fields for basic search
 * @param bool $strictMode If true, requires all terms to be present for a match
 * @param bool $preciseMode If true, prioritizes exact matches and reduces partial matches
 * @return array Array with two elements: search conditions array and SQL query fragment
 */
function enhancedSearch($searchTerm, $fieldPrefix = 'ct.', $mainFields = ['nume', 'email'], $secondaryFields = [], $strictMode = false, $preciseMode = true)
{
    $searchTerm = trim($searchTerm);
    if (empty($searchTerm)) {
        return [[], ''];
    }

    // Clean up the search term - normalize spaces and hyphens
    $cleanFilter = preg_replace('/\s*-\s*/', '-', $searchTerm); // Convert "word - word" to "word-word"
    $cleanFilter = preg_replace('/\s+/', ' ', $cleanFilter); // Normalize multiple spaces

    // Split by spaces and hyphens
    $searchTerms = preg_split('/[\s-]+/', $cleanFilter);
    $searchTerms = array_filter($searchTerms); // Remove empty elements
    $searchTerms = array_values($searchTerms); // Reset array keys

    // Filter out very short terms (less than 2 characters) to avoid too many matches
    // But keep all terms if there are only 2-3 terms in total (for name searches)
    $filteredTerms = [];
    $totalTerms = count($searchTerms);

    foreach ($searchTerms as $term) {
        // Keep all terms if we only have 2-3 terms total, otherwise filter very short ones
        if ($totalTerms <= 3 || strlen($term) >= 2) {
            $filteredTerms[] = strtolower($term); // Convert to lowercase
        }
    }
    $searchTerms = $filteredTerms;

    // If we have no valid search terms after filtering, return empty
    if (empty($searchTerms)) {
        return [[], ''];
    }

    $searchConditions = [];

    // For precise mode with multiple terms, we'll use a very targeted approach
    if ($preciseMode && count($searchTerms) > 1) {
        // 1. Highest priority: Exact match of the full search term
        $exactMatchConditions = [];
        foreach ($mainFields as $field) {
            // Use = for exact match if possible
            $exactMatchConditions[] = "LOWER({$fieldPrefix}{$field}) = LOWER('{$cleanFilter}')";
            // Exact match with word boundaries
            $exactMatchConditions[] = "LOWER({$fieldPrefix}{$field}) LIKE LOWER('{$cleanFilter}')";
            $exactMatchConditions[] = "LOWER({$fieldPrefix}{$field}) LIKE LOWER('% {$cleanFilter} %')";
            $exactMatchConditions[] = "LOWER({$fieldPrefix}{$field}) LIKE LOWER('% {$cleanFilter}')";
            $exactMatchConditions[] = "LOWER({$fieldPrefix}{$field}) LIKE LOWER('{$cleanFilter} %')";
        }
        $searchConditions[] = "(" . implode(' OR ', $exactMatchConditions) . ")";

        // 2. High priority: All terms must be present as whole words
        $allTermsConditions = [];
        foreach ($searchTerms as $term) {
            $termFieldConditions = [];
            foreach ($mainFields as $field) {
                // Only use word boundary matches for precision
                $termFieldConditions[] = "LOWER({$fieldPrefix}{$field}) LIKE LOWER('% {$term} %')";
                $termFieldConditions[] = "LOWER({$fieldPrefix}{$field}) LIKE LOWER('% {$term}')";
                $termFieldConditions[] = "LOWER({$fieldPrefix}{$field}) LIKE LOWER('{$term} %')";
                $termFieldConditions[] = "LOWER({$fieldPrefix}{$field}) = LOWER('{$term}')";
            }
            $allTermsConditions[] = "(" . implode(' OR ', $termFieldConditions) . ")";
        }
        if (!empty($allTermsConditions)) {
            $searchConditions[] = "(" . implode(' AND ', $allTermsConditions) . ")";
        }

        // 3. Medium priority: Adjacent pairs of terms (for 2+ word searches)
        if (count($searchTerms) >= 2) {
            // Only generate pairs of adjacent terms from the original search
            $adjacentPairs = [];
            for ($i = 0; $i < count($searchTerms) - 1; $i++) {
                // Space-separated adjacent pair
                $pair = $searchTerms[$i] . ' ' . $searchTerms[$i + 1];
                $adjacentPairs[] = $pair;

                // Hyphenated adjacent pair
                $pairHyphen = $searchTerms[$i] . '-' . $searchTerms[$i + 1];
                $adjacentPairs[] = $pairHyphen;
            }

            // Add reversed pairs for the first and last terms
            if (count($searchTerms) >= 2) {
                $first = $searchTerms[0];
                $last = $searchTerms[count($searchTerms) - 1];

                // Reversed pair with space
                $reversedPair = $last . ' ' . $first;
                $adjacentPairs[] = $reversedPair;

                // Reversed pair with hyphen
                $reversedPairHyphen = $last . '-' . $first;
                $adjacentPairs[] = $reversedPairHyphen;
            }

            // Add each adjacent pair as a condition
            foreach ($adjacentPairs as $pair) {
                $pairConditions = [];
                foreach ($mainFields as $field) {
                    // Use word boundaries for precise matching
                    $pairConditions[] = "LOWER({$fieldPrefix}{$field}) LIKE LOWER('% {$pair} %')";
                    $pairConditions[] = "LOWER({$fieldPrefix}{$field}) LIKE LOWER('% {$pair}')";
                    $pairConditions[] = "LOWER({$fieldPrefix}{$field}) LIKE LOWER('{$pair} %')";
                    $pairConditions[] = "LOWER({$fieldPrefix}{$field}) = LOWER('{$pair}')";
                }
                $searchConditions[] = "(" . implode(' OR ', $pairConditions) . ")";
            }
        }

        // 4. Handle partial name matching (for cases like "nume prenu" matching "nume prenume")
        if (count($searchTerms) >= 2) {
            // Get all possible pairs of terms for partial matching
            for ($i = 0; $i < count($searchTerms); $i++) {
                for ($j = 0; $j < count($searchTerms); $j++) {
                    if ($i != $j) { // Don't pair a term with itself
                        $term1 = $searchTerms[$i];
                        $term2 = $searchTerms[$j];

                        // Only proceed if at least one term is 3+ characters (for precision)
                        if (strlen($term1) >= 3 || strlen($term2) >= 3) {
                            $partialMatchConditions = [];

                            foreach ($mainFields as $field) {
                                // 1. Basic partial matching - both terms anywhere in the field
                                $partialMatchConditions[] = "(LOWER({$fieldPrefix}{$field}) LIKE LOWER('%{$term1}%') AND LOWER({$fieldPrefix}{$field}) LIKE LOWER('%{$term2}%'))";

                                // 2. Partial matching with one term as prefix of another word
                                if (strlen($term2) >= 3) {
                                    // Look for term1 and any word starting with term2
                                    $partialMatchConditions[] = "(LOWER({$fieldPrefix}{$field}) LIKE LOWER('%{$term1}%') AND LOWER({$fieldPrefix}{$field}) LIKE LOWER('% {$term2}%'))";
                                }

                                if (strlen($term1) >= 3) {
                                    // Look for term2 and any word starting with term1
                                    $partialMatchConditions[] = "(LOWER({$fieldPrefix}{$field}) LIKE LOWER('%{$term2}%') AND LOWER({$fieldPrefix}{$field}) LIKE LOWER('% {$term1}%'))";
                                }

                                // 3. Adjacent terms pattern (with or without space/hyphen between)
                                $partialMatchConditions[] = "LOWER({$fieldPrefix}{$field}) LIKE LOWER('%{$term1}%{$term2}%')";
                                $partialMatchConditions[] = "LOWER({$fieldPrefix}{$field}) LIKE LOWER('%{$term1} {$term2}%')";
                                $partialMatchConditions[] = "LOWER({$fieldPrefix}{$field}) LIKE LOWER('%{$term1}-{$term2}%')";
                            }

                            $searchConditions[] = "(" . implode(' OR ', $partialMatchConditions) . ")";
                        }
                    }
                }
            }

            // Also try matching all terms together in sequence
            if (count($searchTerms) >= 2) {
                $allTermsPattern = implode('%', $searchTerms);
                foreach ($mainFields as $field) {
                    $searchConditions[] = "LOWER({$fieldPrefix}{$field}) LIKE LOWER('%{$allTermsPattern}%')";
                }
            }
        }
    } // For non-precise mode or single term searches, use the original approach but with stricter matching
    else {
        // If we have multiple terms and strict mode is enabled, prioritize exact matches
        if (count($searchTerms) > 1 && $strictMode) {
            // Exact match has highest priority - all terms must be present in the exact order
            $exactMatchConditions = [];
            foreach ($mainFields as $field) {
                $exactMatchConditions[] = "LOWER({$fieldPrefix}{$field}) LIKE LOWER('%{$cleanFilter}%')";
            }
            $searchConditions[] = "(" . implode(' OR ', $exactMatchConditions) . ")";
        }

        // Basic search for each individual term
        $termConditions = [];
        foreach ($searchTerms as $term) {
            if (strlen($term) > 0) {
                $fieldConditions = [];
                foreach ($mainFields as $field) {
                    $fieldConditions[] = "LOWER({$fieldPrefix}{$field}) LIKE LOWER('%{$term}%')";
                }
                $termConditions[] = "(" . implode(' OR ', $fieldConditions) . ")";
            }
        }

        // In strict mode, require ALL terms to be present
        if ($strictMode && !empty($termConditions)) {
            $searchConditions[] = "(" . implode(' AND ', $termConditions) . ")";
        } // In non-strict mode, if we have just one term, add it as a condition
        elseif (!$strictMode && count($searchTerms) == 1 && !empty($termConditions)) {
            $searchConditions[] = $termConditions[0];
        } // For multiple terms in non-strict mode, still require all terms but with lower priority
        elseif (!$strictMode && count($searchTerms) > 1 && !empty($termConditions)) {
            $searchConditions[] = "(" . implode(' AND ', $termConditions) . ")";
        }

        // Handle 2-3 word searches with all possible combinations
        if (count($searchTerms) >= 2 && count($searchTerms) <= 3) {
            // Generate all possible 2-word combinations (for partial name matching)
            for ($i = 0; $i < count($searchTerms); $i++) {
                for ($j = 0; $j < count($searchTerms); $j++) {
                    if ($i != $j) { // Don't pair a word with itself
                        // Add space-separated pair
                        $pair = $searchTerms[$i] . ' ' . $searchTerms[$j];
                        $pairConditions = [];
                        foreach ($mainFields as $field) {
                            $pairConditions[] = "LOWER({$fieldPrefix}{$field}) LIKE LOWER('%{$pair}%')";
                        }
                        $searchConditions[] = "(" . implode(' OR ', $pairConditions) . ")";

                        // Add hyphenated pair
                        $pairHyphen = $searchTerms[$i] . '-' . $searchTerms[$j];
                        $pairHyphenConditions = [];
                        foreach ($mainFields as $field) {
                            $pairHyphenConditions[] = "LOWER({$fieldPrefix}{$field}) LIKE LOWER('%{$pairHyphen}%')";
                        }
                        $searchConditions[] = "(" . implode(' OR ', $pairHyphenConditions) . ")";
                    }
                }
            }
        }
    }

    // Add search across secondary fields (only for the complete search term)
    if (!empty($secondaryFields) && !empty($cleanFilter)) {
        $secondaryConditions = [];
        foreach ($secondaryFields as $field) {
            // Use more precise matching for secondary fields too
            $secondaryConditions[] = "LOWER({$fieldPrefix}{$field}) = LOWER('{$cleanFilter}')";
            $secondaryConditions[] = "LOWER({$fieldPrefix}{$field}) LIKE LOWER('% {$cleanFilter} %')";
            $secondaryConditions[] = "LOWER({$fieldPrefix}{$field}) LIKE LOWER('% {$cleanFilter}')";
            $secondaryConditions[] = "LOWER({$fieldPrefix}{$field}) LIKE LOWER('{$cleanFilter} %')";
        }
        if (!empty($secondaryConditions)) {
            $searchConditions[] = "(" . implode(' OR ', $secondaryConditions) . ")";
        }
    }

    // Combine all conditions with OR
    $sqlFragment = !empty($searchConditions) ? " AND (" . implode(' OR ', $searchConditions) . ")" : '';

    return [$searchConditions, $sqlFragment];
}

function stringTooLong($string, $lungime)
{
    return wordwrap($string ?? '', $lungime, "<br>");
}

function contains($str, array $arr)
{
    foreach ($arr as $a) {
        if (str_contains($str, $a) !== false) return true;
    }
    return false;
}


function writeToCronFile($text)
{
    file_put_contents('cronWrite.txt', "\n$text", FILE_APPEND | LOCK_EX);
}


function verificareHostSendEmail($port)
{
    $hosts = ['***********', 'mail.just.ro', '***********', '***********'];
    $timeout = 2; // seconds

    foreach ($hosts as $host) {
        $connection = @fsockopen($host, $port, $errno, $errstr, $timeout);
        if (is_resource($connection)) {
            fclose($connection);
            return $host;
        }
    }

    return null; // if none of the hosts are available
}


function sendEmail($to, $subiect, $continut, $id)
{
    global $date, $semnaturaDTI;
    $continut = "
    <!DOCTYPE html>
        <html>
        <head>
            <meta http-equiv='Content-Type' content='text/html; charset=UTF-8'>
            <meta http-equiv='Content-Language' content='ro'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <meta charset='UTF-8'>
            <title>Direcția Tehnologia Informației</title>
        </head>
        <body>
          $continut
          $semnaturaDTI
        </body>
    </html>";

    $mail = new PHPMailer(true);
    try {
        $port = 25;
        $mail->isSMTP();
        $mail->CharSet = 'UTF-8';
        $mail->Timeout = 30;
        //$hostEmail = '***********';//nlb
        $hostEmail = verificareHostSendEmail($port);
        if ($hostEmail === null) {
            throw new Exception('No available email server - eroare CAS e-mail');
        }

        $mail->Host = $hostEmail;
        //$mail->Host = '***********';//da
        //$mail->Host = '***********';
        //$mail->Host = '***********';
        //$mail->Host = 'mail.just.ro';

        $mail->SMTPAuth = false;
        $mail->Port = $port;

        // $mail->SMTPAuth = true;
        //$mail->SMTPSecure = 'tls';
        //$mail->Port = 587;

        // $mail->Port = 465;
        // $mail->SMTPSecure = 'ssl';

        // $mail->Username = '<EMAIL>';
        // $mail->Password = 'P@ssw0rd20131';
        $mail->SMTPOptions = [
            'ssl' => [
                'verify_peer' => true,
                'verify_peer_name' => false,
                'allow_self_signed' => false,
            ]
        ];

//        $mail->SMTPDebug = 3;
        $mail->setFrom('<EMAIL>', 'DTI');
        $mail->addAddress($to);
        if (str_contains($subiect, 'Credentiale noi cont')) {
            //fara BCC
        } else {
            $mail->addBCC('<EMAIL>');
        }

        $mail->isHTML(true);
        $mail->Subject = $subiect;
        $mail->Body = $continut;
        $mail->ContentType = 'text/html';

        if ($mail->send()) {
            updateEmailStatus($id, 'sent');
            $text = "-- E-mail trimis catre $to -- $date<br>";
        } else {
            $text = "Failed to send email. Error: {$mail->ErrorInfo}<br>";
            updateEmailStatus($id, 'failed');
        }
    } catch (Exception $e) {
        $text = "Message could not be sent. Mailer Error: {$mail->ErrorInfo}<br>";
        updateEmailStatus($id, 'failed');
    }
    writeToCronFile($text);
    return $text;
}


function updateEmailStatus($id, $status)
{
    global $pdo;
    $updateStmt = "UPDATE scan.email_queue SET status = :status WHERE id = :id";
    if ($status == 'failed') {
        $updateStmt = "UPDATE scan.email_queue SET status = :status, retries = retries + 1 WHERE id = :id";
    }
    $updateStmt = $pdo->prepare($updateStmt);
    $updateStmt->execute([
        ':id' => $id
        , ':status' => $status
    ]);
}


function queueEmail($to, $subject, $content)
{
    global $pdo;
    $text = null;
    $stmt_check = $pdo->prepare("SELECT id, retries FROM scan.email_queue WHERE to_email = :to_email AND subject = :subject AND body = :body and status != 'sent'");
    $stmt_check->execute([
        ':to_email' => $to
        , ':subject' => $subject
        , ':body' => $content
    ]);
    $nrStmt_check = $stmt_check->rowCount();
    $stmt_check = $stmt_check->fetchAll(PDO::FETCH_ASSOC);

    if ($nrStmt_check > 0) {
        foreach ($stmt_check as $s) {
            $id = $s['id'];
            $retries = $s['retries'] + 1;

            $updateStmt = $pdo->prepare("UPDATE scan.email_queue SET retries = :retries WHERE id = :id");
            $updateStmt->execute([
                ':id' => $id
                , ':retries' => $retries
            ]);

            $text = "Se încearcă retrimiterea e-mail-ului...\n<br>";
            writeToCronFile($text);
            echo $text;
        }
    } else {
        $stmt = $pdo->prepare("INSERT INTO scan.email_queue (to_email, subject, body) VALUES (:to_email, :subject, :body)");
        if (
            $stmt->execute([
                ':to_email' => $to
                , ':subject' => $subject
                , ':body' => $content
            ])
        ) {
            $text = "<span style='color: blue;'>Urmează să primiți confirmarea și pe e-mail în câteva minute.</span><br>";
            writeToCronFile($text);
            echo $text;
            return true;
        } else {
            $text = "Nu s-a reușit trimiterea e-mail-ului.";
            writeToCronFile($text);
            echo $text;
            return false;
        }
    }
}

function queueEmailADResetPW($to, $subject, $content)
{
    global $pdo;
    $stmt_check = $pdo->prepare("SELECT id, retries FROM scan.email_queue WHERE to_email = :to_email AND subject = :subject AND body = :body and status != 'sent'");
    $stmt_check->execute([
        ':to_email' => $to
        , ':subject' => $subject
        , ':body' => $content
    ]);
    $nrStmt_check = $stmt_check->rowCount();
    $stmt_check = $stmt_check->fetchAll(PDO::FETCH_ASSOC);

    if ($nrStmt_check > 0) {
        foreach ($stmt_check as $s) {
            $id = $s['id'];
            $retries = $s['retries'] + 1;

            $updateStmt = $pdo->prepare("UPDATE scan.email_queue SET retries = :retries WHERE id = :id");
            $updateStmt->execute([
                ':id' => $id
                , ':retries' => $retries
            ]);

            $text = "Se încearcă retrimiterea e-mail-ului...\n";
            writeToCronFile($text);
            return $text;
        }
    } else {
        $stmt = $pdo->prepare("INSERT INTO scan.email_queue (to_email, subject, body) VALUES (:to_email, :subject, :body)");
        if (
            $stmt->execute([
                ':to_email' => $to
                , ':subject' => $subject
                , ':body' => $content
            ])
        ) {
            $text = "A fost trimis e-mail-ul de resetare a parolei.\n";
            writeToCronFile($text);
            return $text;
        } else {
            $text = "Nu s-a reușit trimiterea e-mail-ului.\n";
            writeToCronFile($text);
            return $text;
        }
    }
}

function processEmailQueue()
{
    global $pdo;
    $sql = "SELECT id, to_email, subject, body, retries FROM scan.email_queue WHERE status != 'sent'";
    $sql = $pdo->query($sql);
    $nrSql = $sql->rowCount();
    $sql = $sql->fetchAll();

    if ($nrSql > 0) {
        $mailRadu = null;
        foreach ($sql as $row) {
            $id = $row['id'];
            $to = $row['to_email'];
            $subject = $row['subject'];
            $content = $row['body'];
            sendEmail($to, $subject, $content, $id);
        }

        $retries = 0;
        foreach ($sql as $row) {
            $id = $row['id'];
            $to = $row['to_email'];
            $retries = $row['retries'];
            if ($to == '<EMAIL>') {
                updateEmailStatus($id, 'sent');
            }
            if ($retries > 5) {
                $mailRadu .= "select * from scan.email_queue where id = $id;";
            }
        }
        if ($retries > 5) {
            //Daca nu merge emailul nu poate trimite notice-ul, asa ca trimite SMS
            $numarTelefon = formatNumarTelefon('0749607867');
            $sms_message = "Peste 5 incercari de trimitere esuate. Am modificat statusul din failed in sent pentru a nu mai incerca trimiterile la urmatoarele id-uri din MJ-Apps $mailRadu";
            $scriptFile = "C:\\wamp64\\www\\sendsms_1.exe";

            $output = [];
            $returnCode = null;
            exec("icacls $scriptFile /grant Everyone:(RX)", $output, $returnCode);
            $command = "powershell.exe C:\\wamp64\\www\\sendsms_1.exe -m '$sms_message' -nr '$numarTelefon' 2>&1";
            $out = shell_exec($command);
        }
    }
}


function is_local_file($file_path)
{
    $prefixes = ['http://', 'https://', 'ftp://'];
    foreach ($prefixes as $prefix) {
        if (str_contains($file_path, $prefix)) {
            return false;
        }
    }
    return true;
}


function sanitize_email($email)
{
    $sanitized_email = filter_var($email, FILTER_SANITIZE_EMAIL);
    if (filter_var($sanitized_email, FILTER_VALIDATE_EMAIL)) {
        return $sanitized_email;
    } else {
        return false;
    }
}


function formatFileSize($bytes, $decimals = 2)
{
    if ($bytes === null) {
        return '0';
    }
    $size = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
    if (!is_string($bytes)) {
        $bytes = (string)$bytes;
    }
    $factor = floor((strlen($bytes) - 1) / 3);
    return sprintf("%.{$decimals}f", $bytes / pow(1024, $factor)) . ' ' . @$size[$factor];
}


function removeDir(string $dir): void
{
    $it = new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS);
    $files = new RecursiveIteratorIterator($it,
        RecursiveIteratorIterator::CHILD_FIRST);
    foreach ($files as $file) {
        if ($file->isDir()) {
            rmdir($file->getPathname());
        } else {
            if (unlink($file->getPathname())) {
//                echo $file->getPathname().' -- file deleted successfully.';
            } else {
                echo $file->getPathname() . ' -- failed to delete file.';
            }
        }
    }
    rmdir($dir);
}


function createZipArchive($files, $zipFileName, $destinatie, $callback)
{
    $zip = new ZipArchive();

    if ($zip->open($zipFileName, ZipArchive::CREATE | ZipArchive::OVERWRITE) === TRUE) {
        foreach ($files as $file) {
            if (file_exists($file)) {
                $zip->addFile($file, basename($file));
            }
        }
        $zip->close();

        if (filesize($zipFileName) > (2 * 1024 * 1024 * 1024)) {
            //Fisier cu dimensiunea peste 2 GB
            return false;
        }

        if (is_callable($callback)) {
            call_user_func($callback, $zipFileName);
        }

        return $zipFileName;
    } else {
        return false;
    }
}


function ziua_saptamanii($date)
{
    $ziua_saptamanii = date('N', strtotime($date));
    return $ziua_saptamanii;
}


function initiale_ziua_saptamana($ziua)
{
    $ziua_saptamana = null;
    $ziua = (int)$ziua;
    if ($ziua == 1) {
        $ziua_saptamana = 'L';
    } elseif ($ziua == 2) {
        $ziua_saptamana = 'Ma';
    } elseif ($ziua == 3) {
        $ziua_saptamana = 'Mi';
    } elseif ($ziua == 4) {
        $ziua_saptamana = 'J';
    } elseif ($ziua == 5) {
        $ziua_saptamana = 'V';
    } elseif ($ziua == 6) {
        $ziua_saptamana = 'S';
    } elseif ($ziua == 7) {
        $ziua_saptamana = 'D';
    }

    return $ziua_saptamana;

}// ziua_saptamana($ziua)


function luna_calendar($luna)
{
    $luna_calendar = null;
    $luna = (int)$luna;
    if ($luna == 1) {
        $luna_calendar = 'Ianuarie';
    } elseif ($luna == 2) {
        $luna_calendar = 'Februarie';
    } elseif ($luna == 3) {
        $luna_calendar = 'Martie';
    } elseif ($luna == 4) {
        $luna_calendar = 'Aprilie';
    } elseif ($luna == 5) {
        $luna_calendar = 'Mai';
    } elseif ($luna == 6) {
        $luna_calendar = 'Iunie';
    } elseif ($luna == 7) {
        $luna_calendar = 'Iulie';
    } elseif ($luna == 8) {
        $luna_calendar = 'August';
    } elseif ($luna == 9) {
        $luna_calendar = 'Septembrie';
    } elseif ($luna == 10) {
        $luna_calendar = 'Octombrie';
    } elseif ($luna == 11) {
        $luna_calendar = 'Noiembrie';
    } elseif ($luna == 12) {
        $luna_calendar = 'Decembrie';
    }

    return $luna_calendar;

}// luna_calendar($luna)


function ziua_saptamanii_nume($data)
{
    $ziSaptamana = date('l', strtotime($data));
    if ($ziSaptamana == 'Monday') {
        $ziSaptamana = "Luni";
    } elseif ($ziSaptamana == 'Tuesday') {
        $ziSaptamana = "Marți";
    } elseif ($ziSaptamana == 'Wednesday') {
        $ziSaptamana = "Miercuri";
    } elseif ($ziSaptamana == 'Thursday') {
        $ziSaptamana = "Joi";
    } elseif ($ziSaptamana == 'Friday') {
        $ziSaptamana = "Vineri";
    } elseif ($ziSaptamana == 'Saturday') {
        $ziSaptamana = "Sâmbătă";
    } elseif ($ziSaptamana == 'Sunday') {
        $ziSaptamana = "Duminică";
    }

    return $ziSaptamana;
}

function data_afisare($data)
{
    if ($data == '0000-00-00' || $data == null || strlen($data) < 10) {
        return null;
    } elseif (strlen($data) == 10) {
        return date_format(date_create_from_format('Y-m-d', $data), 'd.m.Y');
    } elseif (strlen($data) > 10) {
        return date_format(date_create_from_format('Y-m-d H:i:s', $data), 'd.m.Y H:i:s');
    }
    return null;
}

function scurtare_data($data)
{
    if (strlen(data_null($data)) > 10) {
        return date_format(date_create_from_format('Y-m-d H:i:s', $data), 'Y-m-d');
    }
    if (data_null($data) == 10) {
        return $data;
    }
    return null;
}

function data_intrare_baza($data)
{
    //echo $data."<br>";
    if (containsWord($data, '_')) {
        return null;
    }
    if (strlen($data) == 10) {
        return date_format(date_create_from_format('d.m.Y', $data), 'Y-m-d');
    }
    return null;
}

function data_null($data)
{
    if ((strlen($data) == 10 || strlen($data) == 19) && $data[4] == '-' && $data[7] == '-' && $data[0] > 0) {
        return $data;
    }
    return null;
}

function imprimare($id_div)
{
    return "
    <div>
        <a id='metaData' onclick=\"javascript:printContent('$id_div');\" href='#' style='text-decoration: none; color: #333;'>
            <svg style='height: 3em; width: 2em;'>
                <use xlink:href='vendors/@coreui/icons/svg/free.svg#cil-print'></use>
            </svg>
            Print
        </a>
    </div>";
}


function generateRandomString($lungime)
{
//    $alphabet = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890%$#@!';
    $alphabet = 'ABCDEFGHIJKLMNPQRSTUVWXYZ123456789%$#@!';
    $pass = array();
    $alphaLength = strlen($alphabet) - 1;
    for ($i = 0; $i < $lungime; $i++) {
        $n = rand(0, $alphaLength);
        $pass[] = $alphabet[$n];
    }
    return implode($pass);
}

function randomPassword($length, $excludeChars = '')
{
    $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%&';

    // Exclude specific characters if requested
    if ($excludeChars) {
        $characters = str_replace(str_split($excludeChars), '', $characters);
    }

    // Ensure the character set is not empty
    if (strlen($characters) == 0) {
        throw new Exception("Character set is empty after exclusions.");
    }

    $rawRandom = bin2hex(random_bytes($length));
    $result = '';
    $charLen = strlen($characters);

    // Re-map the random bytes to the allowed character set
    for ($i = 0; $i < strlen($rawRandom); $i++) {
        // Get a random index within the character set
        $index = ord($rawRandom[$i]) % $charLen;
        $result .= $characters[$index];
    }

    $result = substr($result, 0, $length);
    if (strlen($result) == 0) {
        return randomPassword($length, $excludeChars);
    }

    return strtoupper($result);
}


function resetPWLDAPS($userName, $metodaComunicare, $lucrare, $ordonator, $changePwForce)
{
    global $env, $date, $client_ip, $client_port, $client_device, $client_device_tip, $email, $link, $PHPSESSID, $pdo;
    $messageLDAP = $statusLDAP = null;

    $canalComunicare = 'sms';
    $numarTelefon = $comunicat_catre = formatNumarTelefon($metodaComunicare);
    if (str_contains($metodaComunicare, '@')) {
        $canalComunicare = 'email';
        $comunicat_catre = $metodaComunicare;
    }

    $ldap_dn = "DC=just,DC=ro";
    $ldap_conn = ldapsConnectToServers();

    $adminUser = $env["ADMIN_AD_USER"];
    $adminPass = $env["ADMIN_AD_PASS"];
    if (ldap_bind($ldap_conn, $adminUser, $adminPass)) {
//        $search_filter = "(cn=$userName)";
//        $search_filter = "(mail=$<EMAIL>)";
        $search_filter = "(sAMAccountName=$userName)";
        $attributes = array("cn", "sn", "userprincipalname", "mail", "sAMAccountName", "useraccountcontrol");
        //$search_result = ldap_search($ldap_conn, $ldap_dn, $search_filter);
        $search_result = ldap_search($ldap_conn, $ldap_dn, $search_filter, $attributes);
        // $search_result = ldap_search($ldap_conn, $ldap_dn, "(objectClass=*)", ["dn"]);

        if (!$search_result) {
            $messageLDAP = "LDAP search failed.";
            $statusLDAP = 'error';
        }
        $entries = ldap_get_entries($ldap_conn, $search_result);
//        $messageLDAP = var_dump($entries);

        if ($entries["count"] > 0) {
            for ($i = 0; $i < $entries["count"]; $i++) {
                $user_dn = $entries[$i]["dn"];
                $user_cn = $entries[$i]["cn"][0];
                $email = $entries[$i]["userprincipalname"][0];
                $userPass = randomPassword(8, '0oO1lI');
                $entry = [];

                //enable account
                $userAccountControl = $entries[0]["useraccountcontrol"][0];

                // Remove the disable flag (2)
                // Remove the 'Password Never Expires' flag (65536)

                if ($changePwForce == 0) {
                    //cont standard utilizator
                    $updatedAccountControl = $userAccountControl & ~2 & ~65536;
                    $entry["userAccountControl"] = $updatedAccountControl;
                } else {
                    //cont generic outlook
                    // $updatedAccountControl = ($userAccountControl | 0x10000) & ~2;
                    // $entry["userAccountControl"] = $userAccountControl | 0x10000;
                    $updatedAccountControl = ($userAccountControl | 65536) & ~2;
                    $entry["userAccountControl"] = $updatedAccountControl;
                }

                //reset pw
                $new_password_quoted = '"' . $userPass . '"';
                $new_password_utf16 = mb_convert_encoding($new_password_quoted, 'UTF-16LE');
                $entry["unicodePwd"] = $new_password_utf16;

                if ($changePwForce == 1) {
                    //change pw at next logon
                    $entry["pwdLastSet"] = 0;
                }


                if (ldap_modify($ldap_conn, $user_dn, $entry)) {
                    if ($canalComunicare == 'email') {
                        //email
                        $to = $metodaComunicare;
                        $subiect = "Credentiale noi cont - $userName";
                        $continut = "Conform cererii nr. $lucrare noua parola pentru contul $userName este: <br><br><b><font face='Trebuchet MS' color='red'>$userPass</font></b><br><br><span style='font-family: Arial; color: black; font-size: 7pt; background-color: #99CCFF'>$date</span><br><br><span style='font-family: Arial; color: red; font-size: 7pt;'><b>Parola a fost generata prin mijloace automate, folosind un string securizat, si a fost comunicata in clar doar destinatarului prezentului email.<br>DTI nu isi asuma responsabilitatea privind activitatile derulate pe un cont de domeniu, i.e. casuta de posta electronica si activitate PC (local sau remote), de catre titularul acestuia sau a altor utilizatori care au intrat in posesia prezentelor credentiale de acces<br></span>";
//                        sendEmail($to, $subiect, $continut, 0);
                        queueEmailADResetPW($to, $subiect, $continut);
                        processEmailQueue();
                    }
                    if ($canalComunicare == 'sms') {
                        //sms
                        $sms_message = "Parola contului de domeniu $<EMAIL> este: $userPass";
                        $scriptFile = "C:\\wamp64\\www\\sendsms_1.exe";

                        $output = [];
                        $returnCode = null;
                        exec("icacls $scriptFile /grant Everyone:(RX)", $output, $returnCode);
                        $command = "powershell.exe C:\\wamp64\\www\\sendsms_1.exe -m '$sms_message' -nr '$numarTelefon' 2>&1";
                        $out = shell_exec($command);
//                        exit($out);

                        $smsInsert = $pdo->prepare("INSERT INTO scan.email_queue (to_email, subject, body, status) VALUES (:to_email, :subject, :body, :status)");
                        $smsInsert->execute([
                            ':to_email' => $numarTelefon
                            , ':subject' => 'sms'
                            , ':body' => $sms_message
                            , ':status' => 'sent'
                        ]);
                    }

                    $audit = $pdo->prepare("INSERT INTO `mj`.`ad_reset_password`
    (`user_ad`, `lucrare`, `comunicat_catre`, `metoda_comunicare`, `ordonator`, `client_ip`, `client_port`, `client_device`, `client_device_tip`, `moment`)
    VALUES (:user_ad, :lucrare, :comunicat_catre, :metoda_comunicare, :ordonator, :client_ip, :client_port, :client_device, :client_device_tip, :moment)");
                    $audit->execute([
                        ':user_ad' => "$<EMAIL>"
                        , ':lucrare' => $lucrare
                        , ':comunicat_catre' => $comunicat_catre
                        , ':metoda_comunicare' => $canalComunicare
                        , ':ordonator' => $ordonator
                        , ':client_ip' => $client_ip
                        , ':client_port' => $client_port
                        , ':client_device' => $client_device
                        , ':client_device_tip' => $client_device_tip
                        , ':moment' => $date
                    ]);

                    $messageLDAP = "Parola schimbată cu succes!";
                    $statusLDAP = 'ok';
                } else {
                    $messageLDAP = "Eroare la schimbarea parolei.";
                    $statusLDAP = 'error';
                }

            }
        } else {
            $messageLDAP = "E-mail negasit ($<EMAIL>).";
            $statusLDAP = 'error';
        }
    } else {
        $messageLDAP = "Failed to bind to LDAP server.";
        $statusLDAP = 'error';
    }
    ldap_close($ldap_conn);

    return [
        'messageLDAP' => $messageLDAP
        , 'statusLDAP' => $statusLDAP
    ];

}


function formatNumarTelefon($nrTelefon)
{
    // sterge spatii, bara, paranteze
    $nrTelefon = preg_replace('/[\s\-()]/', '', $nrTelefon);

    // If the number starts with +407 or 00407, replace with 07
    if (preg_match('/^(\+|00)407/', $nrTelefon)) {
        $nrTelefon = preg_replace('/^(\+|00)407/', '07', $nrTelefon);
    }

    // Check if the number is now in the correct format 07xxxxxxxx
    if (preg_match('/^07\d{8}$/', $nrTelefon)) {
        return "+4$nrTelefon";
    } else {
        return false;
    }
}

function ldapsConnectToServers()
{
//    $servers = ['ldap://MJ-S-DC-02.JUST.RO:389'];
    $servers = ['ldaps://10.1.245.79:636', 'ldaps://10.1.245.80:636', 'ldaps://10.1.244.174:636'];

    foreach ($servers as $server) {
        $ldap_conn = ldap_connect($server);
        if ($ldap_conn) {
            ldap_set_option($ldap_conn, LDAP_OPT_PROTOCOL_VERSION, 3);
            ldap_set_option($ldap_conn, LDAP_OPT_REFERRALS, 0);
            ldap_set_option($ldap_conn, LDAP_OPT_X_TLS_REQUIRE_CERT, LDAP_OPT_X_TLS_NEVER);

            return $ldap_conn;
        }
    }
    return false;
}

//amalia
function loginLDAPS($username, $password)
{
    if (extension_loaded('ldap')) {
        $DomainName = 'just.ro'; // name = domain
        $auth_user = $username . "@" . $DomainName;

        $connect = ldapsConnectToServers();
        if ($bind = @ldap_bind($connect, $auth_user, $password)) {
            //echo "true <BR>";
            @ldap_close($connect);
            return true;
        } else {
            echo "error message - password incorrect";
            @ldap_close($connect);
            return false;
        }
    }
    echo "message - ldap module not loaded";
    return false;
}

function loginMJAppsLDAP($username, $password)
{
    global $client_ip;
    //exceptie <NAME_EMAIL>
    if ($username == '<EMAIL>' && preg_match('/^10\.1\.53\./', $client_ip)) {
        $userInfo = [];
        $userInfo['givenname'][0] = 'Dan';
        $userInfo['sn'][0] = 'Chiranescu';
        $userInfo['title'][0] = 'Specialist IT Sef';
        $userInfo['organizational_units'][0] = 'Curtea Militara de Apel BUCUREȘTI';

        return $userInfo;
    }

    if (extension_loaded('ldap')) {
        $DomainName = 'just.ro';
        $username = explode('@', $username)[0];
        $auth_user = "$username@$DomainName";
        $baseDN = "DC=just,DC=ro";

        $connect = ldapsConnectToServers();
        if ($bind = @ldap_bind($connect, $auth_user, $password)) {
            $filter = "(sAMAccountName=" . ldap_escape($username, "", LDAP_ESCAPE_FILTER) . ")";
            $attributes = array("*"); // Retrieve all attributes
            $search = ldap_search($connect, $baseDN, $filter, $attributes);
            if ($search) {
                $entries = ldap_get_entries($connect, $search);
                if ($entries['count'] > 0) {
                    $userInfo = $entries[0]; // Get the first entry
                    // Extract OU information from the DN
                    $dnParts = ldap_explode_dn($userInfo['dn'], 0);
                    $ouInfo = array();

                    if ($dnParts['count'] > 0) {
                        for ($i = 0; $i < $dnParts['count']; $i++) {
                            if (strpos($dnParts[$i], 'OU=') === 0) {
                                $ouInfo[] = substr($dnParts[$i], 3); // Remove "OU=" prefix
                            }
                        }
                    }

                    // Add OU information to user info array
                    $userInfo['organizational_units'] = $ouInfo;
                    @ldap_close($connect);
                    return $userInfo;
                }
            }
            @ldap_close($connect);
            return true; // Fallback to original behavior if user info can't be retrieved
        } else {
//            echo "error message - user or password incorrect";
            @ldap_close($connect);
            return false;
        }
    }
//    echo "message - ldap module not loaded";
    return false;
}


function loginCheckUserExists($email)
{
    global $pdo;
    $stmt = $pdo->prepare("SELECT u.*, zi.den numeInstanta, zf.functie FROM mj.users u left join sp_it.z_instante zi on zi.id = u.id_structura left join sp_it.z_functii zf on zf.idz_functii = u.id_functie WHERE u.email = :email");
    $stmt->execute(['email' => $email]);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

function insertUserAtFirstLogin($nume, $prenume, $email, $status = 'active', $id_structura = NULL, $id_functie = 0, $login_type = 'activedir')
{
    global $pdo;
    $stmt = $pdo->prepare("INSERT INTO users (nume, prenume, email, status, id_structura, id_functie, login_type, added_on, timestamp)
                           VALUES (:nume, :prenume, :email, :status, :id_structura, :id_functie, :login_type, NOW(), NOW())");
    $stmt->execute([
        'nume' => $nume,
        'prenume' => $prenume,
        'email' => $email,
        'status' => $status,
        'id_structura' => $id_structura,
        'id_functie' => $id_functie,
        'login_type' => $login_type
    ]);
    return $pdo->lastInsertId();
}

function loginUpdateTS($uid)
{
    global $pdo;
    $stmt = $pdo->prepare("UPDATE users SET timestamp = NOW() WHERE uid = :uid");
    $stmt->execute(['uid' => $uid]);
}


function dd(...$vars)
{
    echo '<pre>';
    foreach ($vars as $var) {
        var_dump($var);
    }
    echo '</pre>';
    die();
}


/**
 * Find the best matching instance from the database based on similarity
 *
 * @param PDO $pdo PDO database connection object
 * @param string $textCautat The name to search for
 * @param int $threshold Minimum similarity percentage to consider a match (0-100)
 * @param string $table Optional table name
 * @param string $column Optional column name
 * @return array|null Returns matching record or null if no match found
 */
function findBestColumnMatchDB(string $textCautat, string $table, string $column, int $threshold = 70): ?array
{
    global $pdo;
    $textCautat = trim($textCautat);
    $allRecords = $pdo->query("SELECT * FROM $table")->fetchAll(PDO::FETCH_ASSOC);

    $bestMatch = null;
    $highestSimilarity = 0;
    foreach ($allRecords as $record) {
        similar_text($textCautat, $record[$column], $percent);
        if ($percent > $highestSimilarity) {
            $highestSimilarity = $percent;
            $bestMatch = $record;
        }
    }

    return ($highestSimilarity >= $threshold) ? $bestMatch : null;
}


function sexPersoana($nume)
{
    $masculin = ['Raul', 'Mircea', 'Mihnea', 'Horia', 'Luca', 'Nicolae', 'Niculae', 'Nicolaie', 'Niculaie', 'Mihaita', 'Ionică'];
    $feminin = ['Ingrid', 'Ionica', 'Rosemary', 'Elena'];

    foreach ($masculin as $numeB) {
        if (stripos($nume, $numeB) !== false) {
            return 'B';
        }
    }
    foreach ($feminin as $numeF) {
        if (stripos($nume, $numeF) !== false) {
            return 'F';
        }
    }
    return (substr($nume ?? '', -1) == 'a') ? 'F' : 'B';
}


function generateBreadcrumbs()
{
    $currentFile = basename($_SERVER['PHP_SELF']);
    $menuStructure = [
        'index.php' => [
            'title' => 'Carte telefon MJ',
            'path' => [['title' => 'Carte telefon MJ', 'active' => true]]
        ],

        'weTransferDownload.php' => [
            'title' => 'WeTransfer Download',
            'path' => [
                ['title' => 'Transfer', 'active' => false, 'link' => 'weTransferDownload.php'],
                ['title' => 'Fișiere WeTransfer', 'active' => true]
            ]
        ],
        'weTransferUpload.php' => [
            'title' => 'WeTransfer Upload',
            'path' => [
                ['title' => 'Transfer', 'active' => false, 'link' => 'weTransferUpload.php'],
                ['title' => 'Fișiere WeTransfer', 'active' => true]
            ]
        ],
        'statisticaWeTransfer.php' => [
            'title' => 'Statistică WeTransfer',
            'path' => [
                ['title' => 'Transfer', 'active' => false, 'link' => 'statisticaWeTransfer.php'],
                ['title' => 'Statistică', 'active' => true]
            ]
        ],


        'statisticaPdf2Doc.php' => [
            'title' => 'PDF to DOC',
            'path' => [
                ['title' => 'PDF to DOCX', 'active' => false, 'link' => 'statisticaPdf2Doc.php'],
                ['title' => 'Statistică', 'active' => true]
            ]
        ],


        'dtiResetPw.php' => [
            'title' => 'Resetare parole conturi Active Directory',
            'path' => [
                ['title' => 'DTI', 'active' => false, 'link' => 'dtiResetPw.php'],
                ['title' => 'Resetare parolă AD', 'active' => true]
            ]
        ],


        'dtiSendSMS.php' => [
            'title' => 'Send SMS',
            'path' => [
                ['title' => 'DTI', 'active' => false, 'link' => 'dtiSendSMS.php'],
                ['title' => 'Send SMS', 'active' => true]
            ]
        ],


        'sp_it_just.php' => [
            'title' => 'Carte telefon Specialisti IT just',
            'path' => [
                ['title' => 'DTI', 'active' => false, 'link' => 'sp_it_just.php'],
                ['title' => 'Specialiști IT Just', 'active' => true]
            ]
        ],
    ];

    $breadcrumbHtml = '
    <div class="header-divider"></div>
    <div class="container-fluid">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb my-0 ms-2">
                <li class="breadcrumb-item">
                    <a href="index.php">Home</a>
                </li>';

    if (isset($menuStructure[$currentFile])) {
        foreach ($menuStructure[$currentFile]['path'] as $pathItem) {
            $activeClass = $pathItem['active'] ? ' active' : '';
            if ($pathItem['active']) {
                $breadcrumbHtml .= '
                <li class="breadcrumb-item' . $activeClass . '">
                    <span>' . htmlspecialchars($pathItem['title']) . '</span>
                </li>';
            } else {
                $link = isset($pathItem['link']) ? $pathItem['link'] : '#';
                $breadcrumbHtml .= '
                <li class="breadcrumb-item">
                    <a href="' . htmlspecialchars($link) . '">' . htmlspecialchars($pathItem['title']) . '</a>
                </li>';
            }
        }
    } else {
        $title = ucfirst(str_replace(['_', '-', '.php'], [' ', ' ', ''], $currentFile));
        $breadcrumbHtml .= '
            <li class="breadcrumb-item active">
                <span>' . htmlspecialchars($title) . '</span>
            </li>';
    }
    $breadcrumbHtml .= '
            </ol>
        </nav>
    </div>';

    return $breadcrumbHtml;
}

function getInstantaSuperiora($session_id_structura)
{
    global $pdo;
    $sql = "WITH RECURSIVE Instante_CTE AS (
            SELECT id, den, id_instanta_sup
            FROM sp_it.z_instante
            WHERE id = :id_instanta

            UNION ALL

            SELECT zi.id, zi.den, zi.id_instanta_sup
            FROM sp_it.z_instante zi
            INNER JOIN Instante_CTE ic ON zi.id = ic.id_instanta_sup
        )
        SELECT * FROM Instante_CTE;";
    $stmt = $pdo->prepare($sql);
    $stmt->bindValue(':id_instanta', $session_id_structura, PDO::PARAM_INT);
    $stmt->execute();
    $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
    return $result;
}

function getInstanteSubordonate($session_id_structura)
{
    global $pdo;
    $sql = "WITH RECURSIVE Instante_CTE AS (
                SELECT id, den, id_instanta_sup
                FROM sp_it.z_instante
                WHERE id_instanta_sup = :id_instanta

                UNION ALL

                SELECT zi.id, zi.den, zi.id_instanta_sup
                FROM sp_it.z_instante zi
                INNER JOIN Instante_CTE ic ON zi.id_instanta_sup = ic.id
            )
            SELECT * FROM Instante_CTE;";

    $stmt = $pdo->prepare($sql);
    $stmt->bindValue(':id_instanta', $session_id_structura, PDO::PARAM_INT);
    $stmt->execute();
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}


function approximateNameMatch($dbName, $searchName) {
    // Convert both strings to lowercase for case-insensitive comparison
    $dbNameLower = mb_strtolower($dbName);
    $searchNameLower = mb_strtolower($searchName);

    // Direct match (fastest check first)
    if (strpos($dbNameLower, $searchNameLower) !== false) {
        return true;
    }

    // Split search name into words
    $searchWords = preg_split('/\s+/', trim($searchNameLower));
    $dbWords = preg_split('/\s+/', trim($dbNameLower));

    // Check if each search word approximately matches any db word
    foreach ($searchWords as $searchWord) {
        if (empty(trim($searchWord))) continue;

        $wordFound = false;

        // Check if the word is directly in the db name
        if (strpos($dbNameLower, $searchWord) !== false) {
            $wordFound = true;
            continue;
        }

        // Check each db word for approximate match
        foreach ($dbWords as $dbWord) {
            if (empty(trim($dbWord))) continue;

            // Check for prefix match (first 3 characters)
            if (strlen($searchWord) >= 3 && strlen($dbWord) >= 3 &&
                substr($searchWord, 0, 3) === substr($dbWord, 0, 3)) {
                $wordFound = true;
                break;
            }

            // Check for similar sounding words using metaphone
            if (metaphone($searchWord) === metaphone($dbWord)) {
                $wordFound = true;
                break;
            }

            // Check for Levenshtein distance (for typos)
            if (levenshtein($searchWord, $dbWord) <= 2) {
                $wordFound = true;
                break;
            }
        }

        if (!$wordFound) {
            return false;
        }
    }

    return true;
}

