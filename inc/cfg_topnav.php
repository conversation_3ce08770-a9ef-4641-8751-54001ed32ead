<?php
require_once 'inc/cfg_session.php';
global $auth, $session_uid, $session_nume, $session_prenume, $session_structura, $session_email, $session_functie;
$loginBtn = null;
if (!$auth) {
    $loginBtn = "
    <li class='nav-item my-2'>
    <a class='nav-link py-1 px-2 d-flex align-items-center' href='login.php'>
        <i class='fa fa-user-circle' style='margin-right: 10px;'></i><span>Login</span>
    </a>
</li>";
}
?>
<header class="header header-sticky mb-4">
    <div class="container-fluid">
        <button class="header-toggler px-md-0 me-md-3" type="button"
                onclick="coreui.Sidebar.getInstance(document.querySelector('#sidebar')).toggle()">
            <svg class="icon icon-lg">
                <use xlink:href="vendors/@coreui/icons/svg/free.svg#cil-menu"></use>
            </svg>
        </button>
        <a class="header-brand d-md-none" href="#">
            <img src="assets/img/logo-mj.png" alt="MJ"> Ministerul Justiției
        </a>

        <!--        top menu-->
        <!--        <ul class="header-nav d-none d-md-flex">-->
        <!--            <li class="nav-item"><a class="nav-link" href="#">Dashboard</a></li>-->
        <!--            <li class="nav-item"><a class="nav-link" href="#">Users</a></li>-->
        <!--            <li class="nav-item"><a class="nav-link" href="#">Settings</a></li>-->
        <!--        </ul>-->
        <!--        top menu-->


        <!--        notificari / list / emails-->
        <!--        <ul class="header-nav ms-auto">-->
        <!--            <li class="nav-item"><a class="nav-link" href="#">-->
        <!--                    <svg class="icon icon-lg">-->
        <!--                        <use xlink:href="vendors/@coreui/icons/svg/free.svg#cil-bell"></use>-->
        <!--                    </svg>-->
        <!--                </a></li>-->
        <!--            <li class="nav-item"><a class="nav-link" href="#">-->
        <!--                    <svg class="icon icon-lg">-->
        <!--                        <use xlink:href="vendors/@coreui/icons/svg/free.svg#cil-list-rich"></use>-->
        <!--                    </svg>-->
        <!--                </a></li>-->
        <!--            <li class="nav-item"><a class="nav-link" href="#">-->
        <!--                    <svg class="icon icon-lg">-->
        <!--                        <use xlink:href="vendors/@coreui/icons/svg/free.svg#cil-envelope-open"></use>-->
        <!--                    </svg>-->
        <!--                </a></li>-->
        <!--        </ul>-->
        <!--        notificari / list / emails-->


        <ul class="header-nav ms-3">
            <?php
            (substr($session_prenume ?? '', -1) == 'a') ? $profileImage = "user-female.svg" : $profileImage = "user.svg";
            //            if($session_prenume != null && contains($session_prenume, ['Mircea', 'Mihnea', 'Horia'])){$profileImage = "dada";}

            echo $loginBtn;
            if ($auth) {
                ?>
                <li class="nav-item dropdown">
                    <a class="nav-link py-2 px-3 position-relative" data-coreui-toggle="dropdown" href="#" role="button"
                       aria-haspopup="true" aria-expanded="false">
                        <div class="d-flex align-items-center">
                            <?php if (isset($profileImage) && !empty($profileImage)): ?>
                                <div class="avatar avatar-md rounded-circle overflow-hidden">
                                    <div class="avatar avatar-md bg-primary text-white border border-2 border-light rounded-circle d-flex align-items-center justify-content-center">
                                        <?= substr($session_prenume, 0, 1) . substr($session_nume, 0, 1) ?>
                                    </div>
                                </div>
                            <?php else: ?>
                                <img src="assets/icons/<?= $profileImage ?>"
                                     alt="<?= $session_prenume ?> <?= $session_nume ?>">
                            <?php endif; ?>

                            <span class="ms-2 d-none d-md-inline-block text-truncate" style="max-width: 150px;">
                                <?= $session_prenume ?>
                            </span>
                            <svg class="icon ms-1 text-body-secondary">
                                <use xlink:href="vendors/@coreui/icons/svg/free.svg#cil-chevron-bottom"></use>
                            </svg>

                        </div>
                    </a>
                    <div class="dropdown-menu dropdown-menu-end pt-0 shadow-sm border-0">
                        <div class="dropdown-header bg-light py-3 border-bottom">
                            <div class="d-flex align-items-center px-3">
                                <img style="margin-right: 0.9em;" src="assets/icons/<?= $profileImage ?>"
                                     alt="<?= $session_prenume ?> <?= $session_nume ?>">
                                <div>
                                    <div class="fw-bold"><?= $session_prenume . ' ' . $session_nume ?></div>
                                    <div class="small text-muted"><?= $session_structura ?></div>
                                    <div class="small text-muted"><?= $session_functie ?></div>
                                    <div class="small text-muted"><?= $session_email ?></div>
                                </div>
                            </div>
                        </div>

                        <div class="p-2">
                            <!--                            <a class="dropdown-item d-flex align-items-center rounded-2 py-2" href="profile.php">-->
                            <!--                                <svg class="icon me-2 text-primary">-->
                            <!--                                    <use xlink:href="vendors/@coreui/icons/svg/free.svg#cil-user"></use>-->
                            <!--                                </svg>-->
                            <!--                                <span>My Profile (poate - in progress)</span>-->
                            <!--                            </a>-->
                            <!--                            <a class="dropdown-item d-flex align-items-center rounded-2 py-2" href="settings.php">-->
                            <!--                                <svg class="icon me-2 text-primary">-->
                            <!--                                    <use xlink:href="vendors/@coreui/icons/svg/free.svg#cil-settings"></use>-->
                            <!--                                </svg>-->
                            <!--                                <span>Settings (poate - in progress)</span>-->
                            <!--                            </a>-->
                            <!--                            <div class="dropdown-divider"></div>-->
                            <a class="dropdown-item d-flex align-items-center rounded-2 py-2" href="logout.php">
                                <svg class="icon me-2 text-danger">
                                    <use xlink:href="vendors/@coreui/icons/svg/free.svg#cil-account-logout"></use>
                                </svg>
                                <span>Logout</span>
                            </a>
                        </div>
                    </div>
                </li>
                <?php
            }
            ?>


        </ul>
    </div>

    <?= generateBreadcrumbs() ?>
</header>
