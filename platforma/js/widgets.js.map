{"version": 3, "file": "widgets.js", "names": ["Chart", "defaults", "pointHitDetectionRadius", "plugins", "tooltip", "enabled", "mode", "position", "external", "<PERSON><PERSON>", "ChartJS", "customTooltips", "defaultFontColor", "cardChart1", "document", "getElementById", "type", "data", "labels", "datasets", "label", "backgroundColor", "borderColor", "pointBackgroundColor", "Utils", "getStyle", "options", "legend", "display", "maintainAspectRatio", "scales", "x", "grid", "drawBorder", "ticks", "y", "min", "max", "elements", "line", "borderWidth", "tension", "point", "radius", "hitRadius", "hoverRadius", "cardChart2", "cardChart3", "fill", "cardChart4", "barPercentage", "drawTicks", "random", "Math", "floor", "sparklineChart1", "sparklineChart2", "sparklineChart3", "sparklineChart4", "sparklineChart5", "sparklineChart6", "brandBoxChartLabels", "brandBoxChartOptions", "hoverBorderWidth", "brandBoxChart1", "pointHoverBackgroundColor", "brandBoxChart2", "brandBoxChart3"], "sources": ["../../src/js/widgets.js"], "sourcesContent": ["/* global Chart, coreui */\n\n/**\n * --------------------------------------------------------------------------\n * Core<PERSON> Boostrap Admin Template (v4.2.2): main.js\n * Licensed under MIT (https://coreui.io/license)\n * --------------------------------------------------------------------------\n */\n\n// Disable the on-canvas tooltip\nChart.defaults.pointHitDetectionRadius = 1\nChart.defaults.plugins.tooltip.enabled = false\nChart.defaults.plugins.tooltip.mode = 'index'\nChart.defaults.plugins.tooltip.position = 'nearest'\nChart.defaults.plugins.tooltip.external = coreui.ChartJS.customTooltips\nChart.defaults.defaultFontColor = '#646470'\n\n// eslint-disable-next-line no-unused-vars\nconst cardChart1 = new Chart(document.getElementById('card-chart1'), {\n  type: 'line',\n  data: {\n    labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],\n    datasets: [\n      {\n        label: 'My First dataset',\n        backgroundColor: 'transparent',\n        borderColor: 'rgba(255,255,255,.55)',\n        pointBackgroundColor: coreui.Utils.getStyle('--cui-primary'),\n        data: [65, 59, 84, 84, 51, 55, 40]\n      }\n    ]\n  },\n  options: {\n    plugins: {\n      legend: {\n        display: false\n      }\n    },\n    maintainAspectRatio: false,\n    scales: {\n      x: {\n        grid: {\n          display: false,\n          drawBorder: false\n        },\n        ticks: {\n          display: false\n        }\n      },\n      y: {\n        min: 30,\n        max: 89,\n        display: false,\n        grid: {\n          display: false\n        },\n        ticks: {\n          display: false\n        }\n      }\n    },\n    elements: {\n      line: {\n        borderWidth: 1,\n        tension: 0.4\n      },\n      point: {\n        radius: 4,\n        hitRadius: 10,\n        hoverRadius: 4\n      }\n    }\n  }\n})\n\n// eslint-disable-next-line no-unused-vars\nconst cardChart2 = new Chart(document.getElementById('card-chart2'), {\n  type: 'line',\n  data: {\n    labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],\n    datasets: [\n      {\n        label: 'My First dataset',\n        backgroundColor: 'transparent',\n        borderColor: 'rgba(255,255,255,.55)',\n        pointBackgroundColor: coreui.Utils.getStyle('--cui-info'),\n        data: [1, 18, 9, 17, 34, 22, 11]\n      }\n    ]\n  },\n  options: {\n    plugins: {\n      legend: {\n        display: false\n      }\n    },\n    maintainAspectRatio: false,\n    scales: {\n      x: {\n        grid: {\n          display: false,\n          drawBorder: false\n        },\n        ticks: {\n          display: false\n        }\n      },\n      y: {\n        min: -9,\n        max: 39,\n        display: false,\n        grid: {\n          display: false\n        },\n        ticks: {\n          display: false\n        }\n      }\n    },\n    elements: {\n      line: {\n        borderWidth: 1\n      },\n      point: {\n        radius: 4,\n        hitRadius: 10,\n        hoverRadius: 4\n      }\n    }\n  }\n})\n\n// eslint-disable-next-line no-unused-vars\nconst cardChart3 = new Chart(document.getElementById('card-chart3'), {\n  type: 'line',\n  data: {\n    labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],\n    datasets: [\n      {\n        label: 'My First dataset',\n        backgroundColor: 'rgba(255,255,255,.2)',\n        borderColor: 'rgba(255,255,255,.55)',\n        data: [78, 81, 80, 45, 34, 12, 40],\n        fill: true\n      }\n    ]\n  },\n  options: {\n    plugins: {\n      legend: {\n        display: false\n      }\n    },\n    maintainAspectRatio: false,\n    scales: {\n      x: {\n        display: false\n      },\n      y: {\n        display: false\n      }\n    },\n    elements: {\n      line: {\n        borderWidth: 2,\n        tension: 0.4\n      },\n      point: {\n        radius: 0,\n        hitRadius: 10,\n        hoverRadius: 4\n      }\n    }\n  }\n})\n\n// eslint-disable-next-line no-unused-vars\nconst cardChart4 = new Chart(document.getElementById('card-chart4'), {\n  type: 'bar',\n  data: {\n    labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December', 'January', 'February', 'March', 'April'],\n    datasets: [\n      {\n        label: 'My First dataset',\n        backgroundColor: 'rgba(255,255,255,.2)',\n        borderColor: 'rgba(255,255,255,.55)',\n        data: [78, 81, 80, 45, 34, 12, 40, 85, 65, 23, 12, 98, 34, 84, 67, 82],\n        barPercentage: 0.6\n      }\n    ]\n  },\n  options: {\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        display: false\n      }\n    },\n    scales: {\n      x: {\n        grid: {\n          display: false,\n          drawTicks: false\n\n        },\n        ticks: {\n          display: false\n        }\n      },\n      y: {\n        grid: {\n          display: false,\n          drawBorder: false,\n          drawTicks: false\n        },\n        ticks: {\n          display: false\n        }\n      }\n    }\n  }\n})\n\n// Random Numbers\n// eslint-disable-next-line no-mixed-operators\nconst random = (min, max) => Math.floor(Math.random() * (max - min + 1) + min)\n\n// eslint-disable-next-line no-unused-vars\nconst sparklineChart1 = new Chart(document.getElementById('sparkline-chart-1'), {\n  type: 'bar',\n  data: {\n    labels: ['M', 'T', 'W', 'T', 'F', 'S', 'S', 'M', 'T', 'W', 'T', 'F', 'S', 'S', 'M'],\n    datasets: [\n      {\n        backgroundColor: coreui.Utils.getStyle('--cui-primary'),\n        borderColor: 'transparent',\n        borderWidth: 1,\n        data: [random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100)]\n      }\n    ]\n  },\n  options: {\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        display: false\n      }\n    },\n    scales: {\n      x: {\n        display: false\n      },\n      y: {\n        display: false\n      }\n    }\n  }\n})\n\n// eslint-disable-next-line no-unused-vars\nconst sparklineChart2 = new Chart(document.getElementById('sparkline-chart-2'), {\n  type: 'bar',\n  data: {\n    labels: ['M', 'T', 'W', 'T', 'F', 'S', 'S', 'M', 'T', 'W', 'T', 'F', 'S', 'S', 'M'],\n    datasets: [\n      {\n        backgroundColor: coreui.Utils.getStyle('--cui-warning'),\n        borderColor: 'transparent',\n        borderWidth: 1,\n        data: [random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100)]\n      }\n    ]\n  },\n  options: {\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        display: false\n      }\n    },\n    scales: {\n      x: {\n        display: false\n      },\n      y: {\n        display: false\n      }\n    }\n  }\n})\n\n// eslint-disable-next-line no-unused-vars\nconst sparklineChart3 = new Chart(document.getElementById('sparkline-chart-3'), {\n  type: 'bar',\n  data: {\n    labels: ['M', 'T', 'W', 'T', 'F', 'S', 'S', 'M', 'T', 'W', 'T', 'F', 'S', 'S', 'M'],\n    datasets: [\n      {\n        backgroundColor: coreui.Utils.getStyle('--cui-success'),\n        borderColor: 'transparent',\n        borderWidth: 1,\n        data: [random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100)]\n      }\n    ]\n  },\n  options: {\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        display: false\n      }\n    },\n    scales: {\n      x: {\n        display: false\n      },\n      y: {\n        display: false\n      }\n    }\n  }\n})\n\n// eslint-disable-next-line no-unused-vars\nconst sparklineChart4 = new Chart(document.getElementById('sparkline-chart-4'), {\n  type: 'line',\n  data: {\n    labels: ['M', 'T', 'W', 'T', 'F', 'S', 'S'],\n    datasets: [\n      {\n        backgroundColor: 'transparent',\n        borderColor: coreui.Utils.getStyle('--cui-info'),\n        borderWidth: 2,\n        data: [random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100)]\n      }\n    ]\n  },\n  options: {\n    maintainAspectRatio: false,\n    elements: {\n      line: {\n        tension: 0.4\n      },\n      point: {\n        radius: 0\n      }\n    },\n    plugins: {\n      legend: {\n        display: false\n      }\n    },\n    scales: {\n      x: {\n        display: false\n      },\n      y: {\n        display: false\n      }\n    }\n  }\n})\n\n// eslint-disable-next-line no-unused-vars\nconst sparklineChart5 = new Chart(document.getElementById('sparkline-chart-5'), {\n  type: 'line',\n  data: {\n    labels: ['M', 'T', 'W', 'T', 'F', 'S', 'S'],\n    datasets: [\n      {\n        backgroundColor: 'transparent',\n        borderColor: coreui.Utils.getStyle('--cui-success'),\n        borderWidth: 2,\n        data: [random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100)]\n      }\n    ]\n  },\n  options: {\n    maintainAspectRatio: false,\n    elements: {\n      line: {\n        tension: 0.4\n      },\n      point: {\n        radius: 0\n      }\n    },\n    plugins: {\n      legend: {\n        display: false\n      }\n    },\n    scales: {\n      x: {\n        display: false\n      },\n      y: {\n        display: false\n      }\n    }\n  }\n})\n\n// eslint-disable-next-line no-unused-vars\nconst sparklineChart6 = new Chart(document.getElementById('sparkline-chart-6'), {\n  type: 'line',\n  data: {\n    labels: ['M', 'T', 'W', 'T', 'F', 'S', 'S'],\n    datasets: [\n      {\n        backgroundColor: 'transparent',\n        borderColor: coreui.Utils.getStyle('--cui-danger'),\n        borderWidth: 2,\n        data: [random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100)]\n      }\n    ]\n  },\n  options: {\n    maintainAspectRatio: false,\n    elements: {\n      line: {\n        tension: 0.4\n      },\n      point: {\n        radius: 0\n      }\n    },\n    plugins: {\n      legend: {\n        display: false\n      }\n    },\n    scales: {\n      x: {\n        display: false\n      },\n      y: {\n        display: false\n      }\n    }\n  }\n})\n\nconst brandBoxChartLabels = ['January', 'February', 'March', 'April', 'May', 'June', 'July']\nconst brandBoxChartOptions = {\n  elements: {\n    line: {\n      tension: 0.4\n    },\n    point: {\n      radius: 0,\n      hitRadius: 10,\n      hoverRadius: 4,\n      hoverBorderWidth: 3\n    }\n  },\n  maintainAspectRatio: false,\n  plugins: {\n    legend: {\n      display: false\n    }\n  },\n  scales: {\n    x: {\n      display: false\n    },\n    y: {\n      display: false\n    }\n  }\n}\n\n// eslint-disable-next-line no-unused-vars\nconst brandBoxChart1 = new Chart(document.getElementById('social-box-chart-1'), {\n  type: 'line',\n  data: {\n    labels: brandBoxChartLabels,\n    datasets: [{\n      backgroundColor: 'rgba(255,255,255,.1)',\n      borderColor: 'rgba(255,255,255,.55)',\n      pointHoverBackgroundColor: '#fff',\n      borderWidth: 2,\n      data: [65, 59, 84, 84, 51, 55, 40],\n      fill: true\n    }]\n  },\n  options: brandBoxChartOptions\n})\n\n// eslint-disable-next-line no-unused-vars\nconst brandBoxChart2 = new Chart(document.getElementById('social-box-chart-2'), {\n  type: 'line',\n  data: {\n    labels: brandBoxChartLabels,\n    datasets: [{\n      backgroundColor: 'rgba(255,255,255,.1)',\n      borderColor: 'rgba(255,255,255,.55)',\n      pointHoverBackgroundColor: '#fff',\n      borderWidth: 2,\n      data: [1, 13, 9, 17, 34, 41, 38],\n      fill: true\n    }]\n  },\n  options: brandBoxChartOptions\n})\n\n// eslint-disable-next-line no-unused-vars\nconst brandBoxChart3 = new Chart(document.getElementById('social-box-chart-3'), {\n  type: 'line',\n  data: {\n    labels: brandBoxChartLabels,\n    datasets: [{\n      backgroundColor: 'rgba(255,255,255,.1)',\n      borderColor: 'rgba(255,255,255,.55)',\n      pointHoverBackgroundColor: '#fff',\n      borderWidth: 2,\n      data: [78, 81, 80, 45, 34, 12, 40],\n      fill: true\n    }]\n  },\n  options: brandBoxChartOptions\n})\n"], "mappings": "AAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACAA,KAAK,CAACC,QAAQ,CAACC,uBAAuB,GAAG,CAAC;AAC1CF,KAAK,CAACC,QAAQ,CAACE,OAAO,CAACC,OAAO,CAACC,OAAO,GAAG,KAAK;AAC9CL,KAAK,CAACC,QAAQ,CAACE,OAAO,CAACC,OAAO,CAACE,IAAI,GAAG,OAAO;AAC7CN,KAAK,CAACC,QAAQ,CAACE,OAAO,CAACC,OAAO,CAACG,QAAQ,GAAG,SAAS;AACnDP,KAAK,CAACC,QAAQ,CAACE,OAAO,CAACC,OAAO,CAACI,QAAQ,GAAGC,MAAM,CAACC,OAAO,CAACC,cAAc;AACvEX,KAAK,CAACC,QAAQ,CAACW,gBAAgB,GAAG,SAAS;;AAE3C;AACA,MAAMC,UAAU,GAAG,IAAIb,KAAK,CAACc,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAC,EAAE;EACnEC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE;IACJC,MAAM,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC;IACxEC,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,kBAAkB;MACzBC,eAAe,EAAE,aAAa;MAC9BC,WAAW,EAAE,uBAAuB;MACpCC,oBAAoB,EAAEd,MAAM,CAACe,KAAK,CAACC,QAAQ,CAAC,eAAe,CAAC;MAC5DR,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;IACnC,CAAC;EAEL,CAAC;EACDS,OAAO,EAAE;IACPvB,OAAO,EAAE;MACPwB,MAAM,EAAE;QACNC,OAAO,EAAE;MACX;IACF,CAAC;IACDC,mBAAmB,EAAE,KAAK;IAC1BC,MAAM,EAAE;MACNC,CAAC,EAAE;QACDC,IAAI,EAAE;UACJJ,OAAO,EAAE,KAAK;UACdK,UAAU,EAAE;QACd,CAAC;QACDC,KAAK,EAAE;UACLN,OAAO,EAAE;QACX;MACF,CAAC;MACDO,CAAC,EAAE;QACDC,GAAG,EAAE,EAAE;QACPC,GAAG,EAAE,EAAE;QACPT,OAAO,EAAE,KAAK;QACdI,IAAI,EAAE;UACJJ,OAAO,EAAE;QACX,CAAC;QACDM,KAAK,EAAE;UACLN,OAAO,EAAE;QACX;MACF;IACF,CAAC;IACDU,QAAQ,EAAE;MACRC,IAAI,EAAE;QACJC,WAAW,EAAE,CAAC;QACdC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE,CAAC;QACTC,SAAS,EAAE,EAAE;QACbC,WAAW,EAAE;MACf;IACF;EACF;AACF,CAAC,CAAC;;AAEF;AACA,MAAMC,UAAU,GAAG,IAAI9C,KAAK,CAACc,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAC,EAAE;EACnEC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE;IACJC,MAAM,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC;IACxEC,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,kBAAkB;MACzBC,eAAe,EAAE,aAAa;MAC9BC,WAAW,EAAE,uBAAuB;MACpCC,oBAAoB,EAAEd,MAAM,CAACe,KAAK,CAACC,QAAQ,CAAC,YAAY,CAAC;MACzDR,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;IACjC,CAAC;EAEL,CAAC;EACDS,OAAO,EAAE;IACPvB,OAAO,EAAE;MACPwB,MAAM,EAAE;QACNC,OAAO,EAAE;MACX;IACF,CAAC;IACDC,mBAAmB,EAAE,KAAK;IAC1BC,MAAM,EAAE;MACNC,CAAC,EAAE;QACDC,IAAI,EAAE;UACJJ,OAAO,EAAE,KAAK;UACdK,UAAU,EAAE;QACd,CAAC;QACDC,KAAK,EAAE;UACLN,OAAO,EAAE;QACX;MACF,CAAC;MACDO,CAAC,EAAE;QACDC,GAAG,EAAE,CAAC,CAAC;QACPC,GAAG,EAAE,EAAE;QACPT,OAAO,EAAE,KAAK;QACdI,IAAI,EAAE;UACJJ,OAAO,EAAE;QACX,CAAC;QACDM,KAAK,EAAE;UACLN,OAAO,EAAE;QACX;MACF;IACF,CAAC;IACDU,QAAQ,EAAE;MACRC,IAAI,EAAE;QACJC,WAAW,EAAE;MACf,CAAC;MACDE,KAAK,EAAE;QACLC,MAAM,EAAE,CAAC;QACTC,SAAS,EAAE,EAAE;QACbC,WAAW,EAAE;MACf;IACF;EACF;AACF,CAAC,CAAC;;AAEF;AACA,MAAME,UAAU,GAAG,IAAI/C,KAAK,CAACc,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAC,EAAE;EACnEC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE;IACJC,MAAM,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC;IACxEC,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,kBAAkB;MACzBC,eAAe,EAAE,sBAAsB;MACvCC,WAAW,EAAE,uBAAuB;MACpCL,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;MAClC+B,IAAI,EAAE;IACR,CAAC;EAEL,CAAC;EACDtB,OAAO,EAAE;IACPvB,OAAO,EAAE;MACPwB,MAAM,EAAE;QACNC,OAAO,EAAE;MACX;IACF,CAAC;IACDC,mBAAmB,EAAE,KAAK;IAC1BC,MAAM,EAAE;MACNC,CAAC,EAAE;QACDH,OAAO,EAAE;MACX,CAAC;MACDO,CAAC,EAAE;QACDP,OAAO,EAAE;MACX;IACF,CAAC;IACDU,QAAQ,EAAE;MACRC,IAAI,EAAE;QACJC,WAAW,EAAE,CAAC;QACdC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE,CAAC;QACTC,SAAS,EAAE,EAAE;QACbC,WAAW,EAAE;MACf;IACF;EACF;AACF,CAAC,CAAC;;AAEF;AACA,MAAMI,UAAU,GAAG,IAAIjD,KAAK,CAACc,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAC,EAAE;EACnEC,IAAI,EAAE,KAAK;EACXC,IAAI,EAAE;IACJC,MAAM,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,CAAC;IAC3KC,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,kBAAkB;MACzBC,eAAe,EAAE,sBAAsB;MACvCC,WAAW,EAAE,uBAAuB;MACpCL,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;MACtEiC,aAAa,EAAE;IACjB,CAAC;EAEL,CAAC;EACDxB,OAAO,EAAE;IACPG,mBAAmB,EAAE,KAAK;IAC1B1B,OAAO,EAAE;MACPwB,MAAM,EAAE;QACNC,OAAO,EAAE;MACX;IACF,CAAC;IACDE,MAAM,EAAE;MACNC,CAAC,EAAE;QACDC,IAAI,EAAE;UACJJ,OAAO,EAAE,KAAK;UACduB,SAAS,EAAE;QAEb,CAAC;QACDjB,KAAK,EAAE;UACLN,OAAO,EAAE;QACX;MACF,CAAC;MACDO,CAAC,EAAE;QACDH,IAAI,EAAE;UACJJ,OAAO,EAAE,KAAK;UACdK,UAAU,EAAE,KAAK;UACjBkB,SAAS,EAAE;QACb,CAAC;QACDjB,KAAK,EAAE;UACLN,OAAO,EAAE;QACX;MACF;IACF;EACF;AACF,CAAC,CAAC;;AAEF;AACA;AACA,MAAMwB,MAAM,GAAGA,CAAChB,GAAG,EAAEC,GAAG,KAAKgB,IAAI,CAACC,KAAK,CAACD,IAAI,CAACD,MAAM,CAAC,CAAC,IAAIf,GAAG,GAAGD,GAAG,GAAG,CAAC,CAAC,GAAGA,GAAG,CAAC;;AAE9E;AACA,MAAMmB,eAAe,GAAG,IAAIvD,KAAK,CAACc,QAAQ,CAACC,cAAc,CAAC,mBAAmB,CAAC,EAAE;EAC9EC,IAAI,EAAE,KAAK;EACXC,IAAI,EAAE;IACJC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IACnFC,QAAQ,EAAE,CACR;MACEE,eAAe,EAAEZ,MAAM,CAACe,KAAK,CAACC,QAAQ,CAAC,eAAe,CAAC;MACvDH,WAAW,EAAE,aAAa;MAC1BkB,WAAW,EAAE,CAAC;MACdvB,IAAI,EAAE,CAACmC,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC;IACtQ,CAAC;EAEL,CAAC;EACD1B,OAAO,EAAE;IACPG,mBAAmB,EAAE,KAAK;IAC1B1B,OAAO,EAAE;MACPwB,MAAM,EAAE;QACNC,OAAO,EAAE;MACX;IACF,CAAC;IACDE,MAAM,EAAE;MACNC,CAAC,EAAE;QACDH,OAAO,EAAE;MACX,CAAC;MACDO,CAAC,EAAE;QACDP,OAAO,EAAE;MACX;IACF;EACF;AACF,CAAC,CAAC;;AAEF;AACA,MAAM4B,eAAe,GAAG,IAAIxD,KAAK,CAACc,QAAQ,CAACC,cAAc,CAAC,mBAAmB,CAAC,EAAE;EAC9EC,IAAI,EAAE,KAAK;EACXC,IAAI,EAAE;IACJC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IACnFC,QAAQ,EAAE,CACR;MACEE,eAAe,EAAEZ,MAAM,CAACe,KAAK,CAACC,QAAQ,CAAC,eAAe,CAAC;MACvDH,WAAW,EAAE,aAAa;MAC1BkB,WAAW,EAAE,CAAC;MACdvB,IAAI,EAAE,CAACmC,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC;IACtQ,CAAC;EAEL,CAAC;EACD1B,OAAO,EAAE;IACPG,mBAAmB,EAAE,KAAK;IAC1B1B,OAAO,EAAE;MACPwB,MAAM,EAAE;QACNC,OAAO,EAAE;MACX;IACF,CAAC;IACDE,MAAM,EAAE;MACNC,CAAC,EAAE;QACDH,OAAO,EAAE;MACX,CAAC;MACDO,CAAC,EAAE;QACDP,OAAO,EAAE;MACX;IACF;EACF;AACF,CAAC,CAAC;;AAEF;AACA,MAAM6B,eAAe,GAAG,IAAIzD,KAAK,CAACc,QAAQ,CAACC,cAAc,CAAC,mBAAmB,CAAC,EAAE;EAC9EC,IAAI,EAAE,KAAK;EACXC,IAAI,EAAE;IACJC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IACnFC,QAAQ,EAAE,CACR;MACEE,eAAe,EAAEZ,MAAM,CAACe,KAAK,CAACC,QAAQ,CAAC,eAAe,CAAC;MACvDH,WAAW,EAAE,aAAa;MAC1BkB,WAAW,EAAE,CAAC;MACdvB,IAAI,EAAE,CAACmC,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC;IACtQ,CAAC;EAEL,CAAC;EACD1B,OAAO,EAAE;IACPG,mBAAmB,EAAE,KAAK;IAC1B1B,OAAO,EAAE;MACPwB,MAAM,EAAE;QACNC,OAAO,EAAE;MACX;IACF,CAAC;IACDE,MAAM,EAAE;MACNC,CAAC,EAAE;QACDH,OAAO,EAAE;MACX,CAAC;MACDO,CAAC,EAAE;QACDP,OAAO,EAAE;MACX;IACF;EACF;AACF,CAAC,CAAC;;AAEF;AACA,MAAM8B,eAAe,GAAG,IAAI1D,KAAK,CAACc,QAAQ,CAACC,cAAc,CAAC,mBAAmB,CAAC,EAAE;EAC9EC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE;IACJC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IAC3CC,QAAQ,EAAE,CACR;MACEE,eAAe,EAAE,aAAa;MAC9BC,WAAW,EAAEb,MAAM,CAACe,KAAK,CAACC,QAAQ,CAAC,YAAY,CAAC;MAChDe,WAAW,EAAE,CAAC;MACdvB,IAAI,EAAE,CAACmC,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC;IAC9H,CAAC;EAEL,CAAC;EACD1B,OAAO,EAAE;IACPG,mBAAmB,EAAE,KAAK;IAC1BS,QAAQ,EAAE;MACRC,IAAI,EAAE;QACJE,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE;MACV;IACF,CAAC;IACDxC,OAAO,EAAE;MACPwB,MAAM,EAAE;QACNC,OAAO,EAAE;MACX;IACF,CAAC;IACDE,MAAM,EAAE;MACNC,CAAC,EAAE;QACDH,OAAO,EAAE;MACX,CAAC;MACDO,CAAC,EAAE;QACDP,OAAO,EAAE;MACX;IACF;EACF;AACF,CAAC,CAAC;;AAEF;AACA,MAAM+B,eAAe,GAAG,IAAI3D,KAAK,CAACc,QAAQ,CAACC,cAAc,CAAC,mBAAmB,CAAC,EAAE;EAC9EC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE;IACJC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IAC3CC,QAAQ,EAAE,CACR;MACEE,eAAe,EAAE,aAAa;MAC9BC,WAAW,EAAEb,MAAM,CAACe,KAAK,CAACC,QAAQ,CAAC,eAAe,CAAC;MACnDe,WAAW,EAAE,CAAC;MACdvB,IAAI,EAAE,CAACmC,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC;IAC9H,CAAC;EAEL,CAAC;EACD1B,OAAO,EAAE;IACPG,mBAAmB,EAAE,KAAK;IAC1BS,QAAQ,EAAE;MACRC,IAAI,EAAE;QACJE,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE;MACV;IACF,CAAC;IACDxC,OAAO,EAAE;MACPwB,MAAM,EAAE;QACNC,OAAO,EAAE;MACX;IACF,CAAC;IACDE,MAAM,EAAE;MACNC,CAAC,EAAE;QACDH,OAAO,EAAE;MACX,CAAC;MACDO,CAAC,EAAE;QACDP,OAAO,EAAE;MACX;IACF;EACF;AACF,CAAC,CAAC;;AAEF;AACA,MAAMgC,eAAe,GAAG,IAAI5D,KAAK,CAACc,QAAQ,CAACC,cAAc,CAAC,mBAAmB,CAAC,EAAE;EAC9EC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE;IACJC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IAC3CC,QAAQ,EAAE,CACR;MACEE,eAAe,EAAE,aAAa;MAC9BC,WAAW,EAAEb,MAAM,CAACe,KAAK,CAACC,QAAQ,CAAC,cAAc,CAAC;MAClDe,WAAW,EAAE,CAAC;MACdvB,IAAI,EAAE,CAACmC,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAEA,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC;IAC9H,CAAC;EAEL,CAAC;EACD1B,OAAO,EAAE;IACPG,mBAAmB,EAAE,KAAK;IAC1BS,QAAQ,EAAE;MACRC,IAAI,EAAE;QACJE,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE;MACV;IACF,CAAC;IACDxC,OAAO,EAAE;MACPwB,MAAM,EAAE;QACNC,OAAO,EAAE;MACX;IACF,CAAC;IACDE,MAAM,EAAE;MACNC,CAAC,EAAE;QACDH,OAAO,EAAE;MACX,CAAC;MACDO,CAAC,EAAE;QACDP,OAAO,EAAE;MACX;IACF;EACF;AACF,CAAC,CAAC;AAEF,MAAMiC,mBAAmB,GAAG,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC;AAC5F,MAAMC,oBAAoB,GAAG;EAC3BxB,QAAQ,EAAE;IACRC,IAAI,EAAE;MACJE,OAAO,EAAE;IACX,CAAC;IACDC,KAAK,EAAE;MACLC,MAAM,EAAE,CAAC;MACTC,SAAS,EAAE,EAAE;MACbC,WAAW,EAAE,CAAC;MACdkB,gBAAgB,EAAE;IACpB;EACF,CAAC;EACDlC,mBAAmB,EAAE,KAAK;EAC1B1B,OAAO,EAAE;IACPwB,MAAM,EAAE;MACNC,OAAO,EAAE;IACX;EACF,CAAC;EACDE,MAAM,EAAE;IACNC,CAAC,EAAE;MACDH,OAAO,EAAE;IACX,CAAC;IACDO,CAAC,EAAE;MACDP,OAAO,EAAE;IACX;EACF;AACF,CAAC;;AAED;AACA,MAAMoC,cAAc,GAAG,IAAIhE,KAAK,CAACc,QAAQ,CAACC,cAAc,CAAC,oBAAoB,CAAC,EAAE;EAC9EC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE;IACJC,MAAM,EAAE2C,mBAAmB;IAC3B1C,QAAQ,EAAE,CAAC;MACTE,eAAe,EAAE,sBAAsB;MACvCC,WAAW,EAAE,uBAAuB;MACpC2C,yBAAyB,EAAE,MAAM;MACjCzB,WAAW,EAAE,CAAC;MACdvB,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;MAClC+B,IAAI,EAAE;IACR,CAAC;EACH,CAAC;EACDtB,OAAO,EAAEoC;AACX,CAAC,CAAC;;AAEF;AACA,MAAMI,cAAc,GAAG,IAAIlE,KAAK,CAACc,QAAQ,CAACC,cAAc,CAAC,oBAAoB,CAAC,EAAE;EAC9EC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE;IACJC,MAAM,EAAE2C,mBAAmB;IAC3B1C,QAAQ,EAAE,CAAC;MACTE,eAAe,EAAE,sBAAsB;MACvCC,WAAW,EAAE,uBAAuB;MACpC2C,yBAAyB,EAAE,MAAM;MACjCzB,WAAW,EAAE,CAAC;MACdvB,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;MAChC+B,IAAI,EAAE;IACR,CAAC;EACH,CAAC;EACDtB,OAAO,EAAEoC;AACX,CAAC,CAAC;;AAEF;AACA,MAAMK,cAAc,GAAG,IAAInE,KAAK,CAACc,QAAQ,CAACC,cAAc,CAAC,oBAAoB,CAAC,EAAE;EAC9EC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE;IACJC,MAAM,EAAE2C,mBAAmB;IAC3B1C,QAAQ,EAAE,CAAC;MACTE,eAAe,EAAE,sBAAsB;MACvCC,WAAW,EAAE,uBAAuB;MACpC2C,yBAAyB,EAAE,MAAM;MACjCzB,WAAW,EAAE,CAAC;MACdvB,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;MAClC+B,IAAI,EAAE;IACR,CAAC;EACH,CAAC;EACDtB,OAAO,EAAEoC;AACX,CAAC,CAAC"}