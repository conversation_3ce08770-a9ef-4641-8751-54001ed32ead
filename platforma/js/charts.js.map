{"version": 3, "file": "charts.js", "names": ["random", "Math", "round", "lineChart", "Chart", "document", "getElementById", "type", "data", "labels", "datasets", "label", "backgroundColor", "borderColor", "pointBackgroundColor", "pointBorderColor", "options", "responsive", "<PERSON><PERSON><PERSON>", "highlightFill", "highlightStroke", "doughnutChart", "hoverBackgroundColor", "radarChart", "pointHighlightFill", "pointHighlightStroke", "<PERSON><PERSON><PERSON>", "polarArea<PERSON>hart"], "sources": ["../../src/js/charts.js"], "sourcesContent": ["/* global Chart */\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON><PERSON> Boostrap Admin Template (v4.2.2): main.js\n * Licensed under MIT (https://coreui.io/license)\n * --------------------------------------------------------------------------\n */\n\n// random Numbers\nconst random = () => Math.round(Math.random() * 100)\n\n// eslint-disable-next-line no-unused-vars\nconst lineChart = new Chart(document.getElementById('canvas-1'), {\n  type: 'line',\n  data: {\n    labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],\n    datasets: [\n      {\n        label: 'My First dataset',\n        backgroundColor: 'rgba(220, 220, 220, 0.2)',\n        borderColor: 'rgba(220, 220, 220, 1)',\n        pointBackgroundColor: 'rgba(220, 220, 220, 1)',\n        pointBorderColor: '#fff',\n        data: [random(), random(), random(), random(), random(), random(), random()]\n      },\n      {\n        label: 'My Second dataset',\n        backgroundColor: 'rgba(151, 187, 205, 0.2)',\n        borderColor: 'rgba(151, 187, 205, 1)',\n        pointBackgroundColor: 'rgba(151, 187, 205, 1)',\n        pointBorderColor: '#fff',\n        data: [random(), random(), random(), random(), random(), random(), random()]\n      }\n    ]\n  },\n  options: {\n    responsive: true\n  }\n})\n\n// eslint-disable-next-line no-unused-vars\nconst barChart = new Chart(document.getElementById('canvas-2'), {\n  type: 'bar',\n  data: {\n    labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],\n    datasets: [\n      {\n        backgroundColor: 'rgba(220, 220, 220, 0.5)',\n        borderColor: 'rgba(220, 220, 220, 0.8)',\n        highlightFill: 'rgba(220, 220, 220, 0.75)',\n        highlightStroke: 'rgba(220, 220, 220, 1)',\n        data: [random(), random(), random(), random(), random(), random(), random()]\n      },\n      {\n        backgroundColor: 'rgba(151, 187, 205, 0.5)',\n        borderColor: 'rgba(151, 187, 205, 0.8)',\n        highlightFill: 'rgba(151, 187, 205, 0.75)',\n        highlightStroke: 'rgba(151, 187, 205, 1)',\n        data: [random(), random(), random(), random(), random(), random(), random()]\n      }\n    ]\n  },\n  options: {\n    responsive: true\n  }\n})\n\n// eslint-disable-next-line no-unused-vars\nconst doughnutChart = new Chart(document.getElementById('canvas-3'), {\n  type: 'doughnut',\n  data: {\n    labels: ['Red', 'Green', 'Yellow'],\n    datasets: [{\n      data: [300, 50, 100],\n      backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56'],\n      hoverBackgroundColor: ['#FF6384', '#36A2EB', '#FFCE56']\n    }]\n  },\n  options: {\n    responsive: true\n  }\n})\n\n// eslint-disable-next-line no-unused-vars\nconst radarChart = new Chart(document.getElementById('canvas-4'), {\n  type: 'radar',\n  data: {\n    labels: ['Eating', 'Drinking', 'Sleeping', 'Designing', 'Coding', 'Cycling', 'Running'],\n    datasets: [\n      {\n        label: 'My First dataset',\n        backgroundColor: 'rgba(220, 220, 220, 0.2)',\n        borderColor: 'rgba(220, 220, 220, 1)',\n        pointBackgroundColor: 'rgba(220, 220, 220, 1)',\n        pointBorderColor: '#fff',\n        pointHighlightFill: '#fff',\n        pointHighlightStroke: 'rgba(220, 220, 220, 1)',\n        data: [65, 59, 90, 81, 56, 55, 40]\n      },\n      {\n        label: 'My Second dataset',\n        backgroundColor: 'rgba(151, 187, 205, 0.2)',\n        borderColor: 'rgba(151, 187, 205, 1)',\n        pointBackgroundColor: 'rgba(151, 187, 205, 1)',\n        pointBorderColor: '#fff',\n        pointHighlightFill: '#fff',\n        pointHighlightStroke: 'rgba(151, 187, 205, 1)',\n        data: [28, 48, 40, 19, 96, 27, 100]\n      }\n    ]\n  },\n  options: {\n    responsive: true\n  }\n})\n\n// eslint-disable-next-line no-unused-vars\nconst pieChart = new Chart(document.getElementById('canvas-5'), {\n  type: 'pie',\n  data: {\n    labels: ['Red', 'Green', 'Yellow'],\n    datasets: [{\n      data: [300, 50, 100],\n      backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56'],\n      hoverBackgroundColor: ['#FF6384', '#36A2EB', '#FFCE56']\n    }]\n  },\n  options: {\n    responsive: true\n  }\n})\n\n// eslint-disable-next-line no-unused-vars\nconst polarAreaChart = new Chart(document.getElementById('canvas-6'), {\n  type: 'polarArea',\n  data: {\n    labels: ['Red', 'Green', 'Yellow', 'Grey', 'Blue'],\n    datasets: [{\n      data: [11, 16, 7, 3, 14],\n      backgroundColor: ['#FF6384', '#4BC0C0', '#FFCE56', '#E7E9ED', '#36A2EB']\n    }]\n  },\n  options: {\n    responsive: true\n  }\n})\n"], "mappings": "AAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,MAAMA,MAAM,GAAGA,CAAA,KAAMC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACD,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC;;AAEpD;AACA,MAAMG,SAAS,GAAG,IAAIC,KAAK,CAACC,QAAQ,CAACC,cAAc,CAAC,UAAU,CAAC,EAAE;EAC/DC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE;IACJC,MAAM,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC;IACxEC,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,kBAAkB;MACzBC,eAAe,EAAE,0BAA0B;MAC3CC,WAAW,EAAE,wBAAwB;MACrCC,oBAAoB,EAAE,wBAAwB;MAC9CC,gBAAgB,EAAE,MAAM;MACxBP,IAAI,EAAE,CAACR,MAAM,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC;IAC7E,CAAC,EACD;MACEW,KAAK,EAAE,mBAAmB;MAC1BC,eAAe,EAAE,0BAA0B;MAC3CC,WAAW,EAAE,wBAAwB;MACrCC,oBAAoB,EAAE,wBAAwB;MAC9CC,gBAAgB,EAAE,MAAM;MACxBP,IAAI,EAAE,CAACR,MAAM,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC;IAC7E,CAAC;EAEL,CAAC;EACDgB,OAAO,EAAE;IACPC,UAAU,EAAE;EACd;AACF,CAAC,CAAC;;AAEF;AACA,MAAMC,QAAQ,GAAG,IAAId,KAAK,CAACC,QAAQ,CAACC,cAAc,CAAC,UAAU,CAAC,EAAE;EAC9DC,IAAI,EAAE,KAAK;EACXC,IAAI,EAAE;IACJC,MAAM,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC;IACxEC,QAAQ,EAAE,CACR;MACEE,eAAe,EAAE,0BAA0B;MAC3CC,WAAW,EAAE,0BAA0B;MACvCM,aAAa,EAAE,2BAA2B;MAC1CC,eAAe,EAAE,wBAAwB;MACzCZ,IAAI,EAAE,CAACR,MAAM,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC;IAC7E,CAAC,EACD;MACEY,eAAe,EAAE,0BAA0B;MAC3CC,WAAW,EAAE,0BAA0B;MACvCM,aAAa,EAAE,2BAA2B;MAC1CC,eAAe,EAAE,wBAAwB;MACzCZ,IAAI,EAAE,CAACR,MAAM,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC;IAC7E,CAAC;EAEL,CAAC;EACDgB,OAAO,EAAE;IACPC,UAAU,EAAE;EACd;AACF,CAAC,CAAC;;AAEF;AACA,MAAMI,aAAa,GAAG,IAAIjB,KAAK,CAACC,QAAQ,CAACC,cAAc,CAAC,UAAU,CAAC,EAAE;EACnEC,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE;IACJC,MAAM,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,CAAC;IAClCC,QAAQ,EAAE,CAAC;MACTF,IAAI,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC;MACpBI,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;MAClDU,oBAAoB,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;IACxD,CAAC;EACH,CAAC;EACDN,OAAO,EAAE;IACPC,UAAU,EAAE;EACd;AACF,CAAC,CAAC;;AAEF;AACA,MAAMM,UAAU,GAAG,IAAInB,KAAK,CAACC,QAAQ,CAACC,cAAc,CAAC,UAAU,CAAC,EAAE;EAChEC,IAAI,EAAE,OAAO;EACbC,IAAI,EAAE;IACJC,MAAM,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,CAAC;IACvFC,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,kBAAkB;MACzBC,eAAe,EAAE,0BAA0B;MAC3CC,WAAW,EAAE,wBAAwB;MACrCC,oBAAoB,EAAE,wBAAwB;MAC9CC,gBAAgB,EAAE,MAAM;MACxBS,kBAAkB,EAAE,MAAM;MAC1BC,oBAAoB,EAAE,wBAAwB;MAC9CjB,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;IACnC,CAAC,EACD;MACEG,KAAK,EAAE,mBAAmB;MAC1BC,eAAe,EAAE,0BAA0B;MAC3CC,WAAW,EAAE,wBAAwB;MACrCC,oBAAoB,EAAE,wBAAwB;MAC9CC,gBAAgB,EAAE,MAAM;MACxBS,kBAAkB,EAAE,MAAM;MAC1BC,oBAAoB,EAAE,wBAAwB;MAC9CjB,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG;IACpC,CAAC;EAEL,CAAC;EACDQ,OAAO,EAAE;IACPC,UAAU,EAAE;EACd;AACF,CAAC,CAAC;;AAEF;AACA,MAAMS,QAAQ,GAAG,IAAItB,KAAK,CAACC,QAAQ,CAACC,cAAc,CAAC,UAAU,CAAC,EAAE;EAC9DC,IAAI,EAAE,KAAK;EACXC,IAAI,EAAE;IACJC,MAAM,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,CAAC;IAClCC,QAAQ,EAAE,CAAC;MACTF,IAAI,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC;MACpBI,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;MAClDU,oBAAoB,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;IACxD,CAAC;EACH,CAAC;EACDN,OAAO,EAAE;IACPC,UAAU,EAAE;EACd;AACF,CAAC,CAAC;;AAEF;AACA,MAAMU,cAAc,GAAG,IAAIvB,KAAK,CAACC,QAAQ,CAACC,cAAc,CAAC,UAAU,CAAC,EAAE;EACpEC,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE;IACJC,MAAM,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC;IAClDC,QAAQ,EAAE,CAAC;MACTF,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;MACxBI,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;IACzE,CAAC;EACH,CAAC;EACDI,OAAO,EAAE;IACPC,UAAU,EAAE;EACd;AACF,CAAC,CAAC"}