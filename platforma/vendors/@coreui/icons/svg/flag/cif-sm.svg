<svg xmlns="http://www.w3.org/2000/svg" width="301" height="225" viewBox="0 0 301 225">
  <g fill="none" fill-rule="evenodd">
    <path fill="#FFF" fill-rule="nonzero" d="M.5 225h300V0H.5z"/>
    <path fill="#5EB6E4" fill-rule="nonzero" d="M.5 112.5V225h300V112.5z"/>
    <path fill="#000" fill-rule="nonzero" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M113.916 134.163s-1.232-.497-1.777-.77c-.947-.474-3.081-3.099-5.465-4.483-3.66-2.123-7.202-1.576-10.437-2.966.725 3.389 3.008 6.449 6.788 8.07 3.105 1.332 8.115.187 8.661.232.545.046 2.276.714 2.276.714l-.046-.797h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M193.501 96.63a.243.243 0 01.014-.1s.112-.163.225-.43c.115-.272.23-.646.237-1.063.004-.254-.208-.459-.209-.459 0 0-.043-.219.097-.2l.976.134c.13.018.158.131.053.209l-.031.026c-.021.02-.051.048-.084.087a.881.881 0 00-.18.331c-.519 1.852-.54 2.509-.54 2.509-.005.156-.181.18-.227.031l-.331-1.075z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".291" d="M193.501 96.63a.243.243 0 01.014-.1s.112-.163.225-.43c.115-.272.23-.646.237-1.063.004-.254-.208-.459-.209-.459 0 0-.043-.219.097-.2l.976.134c.13.018.158.131.053.209l-.031.026c-.021.02-.051.048-.084.087a.881.881 0 00-.18.331c-.519 1.852-.54 2.509-.54 2.509-.005.156-.181.18-.227.031l-.331-1.075h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M195.171 103.242c.496-.325.663-1.166.694-1.71.055-.977-.758-1.841-.8-2.745-.027-.58.475-.64.57-1.092.084-.404-1.485-1.821-1.062-2.938.303-.798.939-.406 1.182-1.24.256-.884-.538-2.147.483-2.741.496-.289 1.224.132 1.44-.642.22-.787.045-2.351 1.179-2.462 1.504-.147 1.737 2.932 2.013 3.149.287.225.672-.188 1.082-.025 1.105.44-.078 2.478-.458 3.158 2.802.114.24 1.857-.191 2.632.119.283.836.388.954.818.077.283-.135.532-.332.706-.628.554-1.564.762-2.245 1.211l-.094.067c-.023.256.687 1.006-.27 1.324-1.787.595-2.925.985-3.614 2.79-.185.484-.25 1.971-.284 1.818l-.247-2.078z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M195.171 103.242c.496-.325.663-1.166.694-1.71.055-.977-.758-1.841-.8-2.745-.027-.58.475-.64.57-1.092.084-.404-1.485-1.821-1.062-2.938.303-.798.939-.406 1.182-1.24.256-.884-.538-2.147.483-2.741.496-.289 1.224.132 1.44-.642.22-.787.045-2.351 1.179-2.462 1.504-.147 1.737 2.932 2.013 3.149.287.225.672-.188 1.082-.025 1.105.44-.078 2.478-.458 3.158 2.802.114.24 1.857-.191 2.632.119.283.836.388.954.818.077.283-.135.532-.332.706-.628.554-1.564.762-2.245 1.211l-.094.067c-.023.256.687 1.006-.27 1.324-1.787.595-2.925.985-3.614 2.79-.185.484-.25 1.971-.284 1.818l-.247-2.078h0z"/>
    <path fill="#658D5C" fill-rule="nonzero" d="M195.443 104.653l-.31-1.364c.084-.045.155-.113.224-.179.364-.349.537-1.316.565-1.801.061-1.054-.704-1.658-.742-2.534-.019-.455.485-.617.567-1.048.104-.548-1.409-1.873-1.012-2.91.27-.702.923-.339 1.187-1.254.23-.797-.459-2.219.402-2.696.52-.287 1.269.174 1.521-.747.191-.697.044-1.887 1.029-1.985 1.339-.134 1.54 2.555 1.888 2.823.386.296.693-.088 1.064.057.56.218.422.991.296 1.436-.146.518-.398.945-.612 1.426-.042.096-.043.186.07.229.352.133.652.077.775.539.166.627-.797 1.312-1.01 1.809-.166.387.484.485.569.998.121.723-2.307 1.534-2.493 1.873-.022.474.608.965-.157 1.221-1.924.642-3.843 1.853-3.821 4.107z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M195.443 104.653l-.31-1.364c.084-.045.155-.113.224-.179.364-.349.537-1.316.565-1.801.061-1.054-.704-1.658-.742-2.534-.019-.455.485-.617.567-1.048.104-.548-1.409-1.873-1.012-2.91.27-.702.923-.339 1.187-1.254.23-.797-.459-2.219.402-2.696.52-.287 1.269.174 1.521-.747.191-.697.044-1.887 1.029-1.985 1.339-.134 1.54 2.555 1.888 2.823.386.296.693-.088 1.064.057.56.218.422.991.296 1.436-.146.518-.398.945-.612 1.426-.042.096-.043.186.07.229.352.133.652.077.775.539.166.627-.797 1.312-1.01 1.809-.166.387.484.485.569.998.121.723-2.307 1.534-2.493 1.873-.022.474.608.965-.157 1.221-1.924.642-3.843 1.853-3.821 4.107h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M195.275 103.447s2.448-4.358 3.156-7.453c.489-2.142.705-5.609.705-5.609s-.106 3.455-.53 5.602c-.646 3.265-3.176 7.87-3.176 7.87l-.155-.41z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".169" d="M195.275 103.447s2.448-4.358 3.156-7.453c.489-2.142.705-5.609.705-5.609s-.106 3.455-.53 5.602c-.646 3.265-3.176 7.87-3.176 7.87l-.155-.41h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M195.353 107.586a.237.237 0 01.03-.1s.478-.503.762-.824c.111-.125.18-.276.221-.399.04-.119.052-.206.052-.206.011-.077.078-.119.152-.094l.814.269c.111.037.126.138.03.206-.005.003-.625.442-.85.71-.495.59-.625.814-.825 1.423-.047.141-.201.13-.225-.017l-.161-.968z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".291" d="M195.353 107.586a.237.237 0 01.03-.1s.478-.503.762-.824c.111-.125.18-.276.221-.399.04-.119.052-.206.052-.206.011-.077.078-.119.152-.094l.814.269c.111.037.126.138.03.206-.005.003-.625.442-.85.71-.495.59-.625.814-.825 1.423-.047.141-.201.13-.225-.017l-.161-.968h0z"/>
    <path fill="#A48253" fill-rule="nonzero" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".291" d="M195.631 108.534c.205-.626.343-.862.846-1.461.237-.282.872-.731.872-.731l-.814-.269s-.053.384-.301.667c-.285.322-.765.828-.765.828l.162.966z"/>
    <path fill="#000" fill-rule="nonzero" d="M192.385 92.756s1.022 2.038 1.902 5.171c1.814 6.469 2.049 14.868 1.268 19.488-2.403 14.2-15.248 27.478-25.618 29.548-8.697 1.736-13.828 5.443-17.473 9.464-2.834 3.276-3.93 5.228-6.5 10.464-.791 1.612-3.381 1.928-2.267-.341 2.015-3.559 3.929-6.846 6.881-10.345 3.838-4.337 9.351-8.591 18.662-10.462 10.706-2.151 23.041-15.407 25.444-29.606.782-4.62.541-14.689-2.07-21.166l-.229-2.215z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M192.385 92.756s1.022 2.038 1.902 5.171c1.814 6.469 2.049 14.868 1.268 19.488-2.403 14.2-15.248 27.478-25.618 29.548-8.697 1.736-13.828 5.443-17.473 9.464-2.834 3.276-3.93 5.228-6.5 10.464-.791 1.612-3.381 1.928-2.267-.341 2.015-3.559 3.929-6.846 6.881-10.345 3.838-4.337 9.351-8.591 18.662-10.462 10.706-2.151 23.041-15.407 25.444-29.606.782-4.62.541-14.689-2.07-21.166l-.229-2.215"/>
    <path fill="#000" fill-rule="nonzero" d="M186.971 90.125s-3.018-.115-1.567-2.401c-1.381-.679-3.047-1.91-1.265-2.926-.08-.346-1.133-.679-1.054-1.496.046-.469.64-.684.691-.886-.196-.37-1.573-2.138-1.096-2.666.733-.81 2.316.166 3.036.6.179-.11.445-.333.623-.452 1.052-.697 1.122.989 1.41 1.528.132.247.963-.422 1.523-.125.969.512.541 1.764.785 2.608.243-.026.592-.224.835-.241 1.571-.105.72 2.661.653 3.53 2.498-.36.576 2.203.715 4.404.038.608-.15 1.951-.619 1.435-1.478-1.626-5.587-1.181-4.67-2.912z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".317" d="M186.971 90.125s-3.018-.115-1.567-2.401c-1.381-.679-3.047-1.91-1.265-2.926-.08-.346-1.133-.679-1.054-1.496.046-.469.64-.684.691-.886-.196-.37-1.573-2.138-1.096-2.666.733-.81 2.316.166 3.036.6.179-.11.445-.333.623-.452 1.052-.697 1.122.989 1.41 1.528.132.247.963-.422 1.523-.125.969.512.541 1.764.785 2.608.243-.026.592-.224.835-.241 1.571-.105.72 2.661.653 3.53 2.498-.36.576 2.203.715 4.404.038.608-.15 1.951-.619 1.435-1.478-1.626-5.587-1.181-4.67-2.912h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M186.622 99.198c-.541-.044-1.713-.209-1.944-.722-.365-.813.585-1.092.538-1.154-.333-.443-1.873-.726-1.882-1.346-.006-.435.721-.747.592-1.143-.176-.543-1.214-1.368-.811-1.971.488-.729 1.513.251 2.007.244.235-.003.568-.524 1.019-.532.663-.011 1.068 1.28 1.318 1.245.277-.038.447-.419.72-.5.831-.25 1.069.649 1.265 1.234.387 1.157.804-.216 1.43.056 1.581.69.93 3.785 1.388 4.156.276.223.794.195.997.511.433.673-.161 2.032.172 3.249.135.492 1.671 1.922 1.671 1.922s.239 1.432.199 1.357c-.692-1.332-.842-1.648-2.081-2.594-1.191-.909-3-.188-3.417-.665-.464-.531.073-1.016-.169-1.248-.092-.088-1.49.057-1.7.036-.995-.1-1.906-1.004-1.312-2.135z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".366" d="M186.622 99.198c-.541-.044-1.713-.209-1.944-.722-.365-.813.585-1.092.538-1.154-.333-.443-1.873-.726-1.882-1.346-.006-.435.721-.747.592-1.143-.176-.543-1.214-1.368-.811-1.971.488-.729 1.513.251 2.007.244.235-.003.568-.524 1.019-.532.663-.011 1.068 1.28 1.318 1.245.277-.038.447-.419.72-.5.831-.25 1.069.649 1.265 1.234.387 1.157.804-.216 1.43.056 1.581.69.93 3.785 1.388 4.156.276.223.794.195.997.511.433.673-.161 2.032.172 3.249.135.492 1.671 1.922 1.671 1.922s.239 1.432.199 1.357c-.692-1.332-.842-1.648-2.081-2.594-1.191-.909-3-.188-3.417-.665-.464-.531.073-1.016-.169-1.248-.092-.088-1.49.057-1.7.036-.995-.1-1.906-1.004-1.312-2.135h0z"/>
    <path fill="#658D5C" fill-rule="nonzero" d="M194.943 104.324l.16.891c-2.548-3.645-4.628-2.197-5.175-2.79-.416-.451.114-.776-.225-1.129-.359-.374-2.501.418-3.137-1.245-.132-.346.34-.917.101-.964-.542-.106-1.272-.091-1.644-.582-.155-.205-.288-.485-.086-.707.105-.114.229-.211.317-.341.331-.492-1.682-.872-1.689-1.605-.004-.42.548-.572.346-1.089-.177-.452-.788-1.201-.476-1.694.378-.596 1.227.154 1.7.209.232.027.626-.52 1.009-.531.61-.018.87 1.115 1.336 1.069.455-.045.971-.752 1.469-.138.347.428.339 1.679 1.151 1.317.194-.086.483-.323.7-.229 1.299.562.968 2.65 1.291 3.762.174.596.794.465 1.016.834.107.179.197.387.215.596.016.18-.012.358-.037.535-.101.72-.033 1.367.151 2.073.143.564.934 1.605 1.507 1.758z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".366" d="M194.943 104.324l.16.891c-2.548-3.645-4.628-2.197-5.175-2.79-.416-.451.114-.776-.225-1.129-.359-.374-2.501.418-3.137-1.245-.132-.346.34-.917.101-.964-.542-.106-1.272-.091-1.644-.582-.155-.205-.288-.485-.086-.707.105-.114.229-.211.317-.341.331-.492-1.682-.872-1.689-1.605-.004-.42.548-.572.346-1.089-.177-.452-.788-1.201-.476-1.694.378-.596 1.227.154 1.7.209.232.027.626-.52 1.009-.531.61-.018.87 1.115 1.336 1.069.455-.045.971-.752 1.469-.138.347.428.339 1.679 1.151 1.317.194-.086.483-.323.7-.229 1.299.562.968 2.65 1.291 3.762.174.596.794.465 1.016.834.107.179.197.387.215.596.016.18-.012.358-.037.535-.101.72-.033 1.367.151 2.073.143.564.934 1.605 1.507 1.758h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M195.533 115.44l.127-.105c.449-.008.76-.937.895-1.292.563-1.475-.448-3.923 1.255-3.916.087-.61.212-2.109.767-2.509.402-.29.797.075 1.182-.04.285-.574.421-1.665 1.239-1.818.259-.048.804.373.894.327.223-.112.488-1.282 1.143-1.458.404-.109.785.308.932.214.24-.153.472-.404.671-.617.854-.912 1.919-.928 1.891.289-.01.434-.287 1.046-.442 1.441-.053.134.537.415.627.587.302.575-.76 1.258-.997 1.711.016.076.562.185.644.3.707.989-1.339 2.073-1.857 2.681-.204.239.497.771.4 1.185-.392 1.661-2.949 1.05-3.516 1.307-.197.09-.257.98-.424 1.205-.72.973-3.572-.216-4.22.394-.59.556-.786 1.503-1.162 2.179-.081.147-.238.102-.224-.052l.175-2.013z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M195.533 115.44l.127-.105c.449-.008.76-.937.895-1.292.563-1.475-.448-3.923 1.255-3.916.087-.61.212-2.109.767-2.509.402-.29.797.075 1.182-.04.285-.574.421-1.665 1.239-1.818.259-.048.804.373.894.327.223-.112.488-1.282 1.143-1.458.404-.109.785.308.932.214.24-.153.472-.404.671-.617.854-.912 1.919-.928 1.891.289-.01.434-.287 1.046-.442 1.441-.053.134.537.415.627.587.302.575-.76 1.258-.997 1.711.016.076.562.185.644.3.707.989-1.339 2.073-1.857 2.681-.204.239.497.771.4 1.185-.392 1.661-2.949 1.05-3.516 1.307-.197.09-.257.98-.424 1.205-.72.973-3.572-.216-4.22.394-.59.556-.786 1.503-1.162 2.179-.081.147-.238.102-.224-.052l.175-2.013h0z"/>
    <path fill="#658D5C" fill-rule="nonzero" d="M195.702 115.511c.528-.026.857-.987 1.017-1.405a5.098 5.098 0 00.322-1.669c.032-.844-.571-2.327.739-2.126 0 0 .276-1.978.843-2.434.37-.297 1.029-.129 1.188-.246.378-.279.231-1.485 1.046-1.639.315-.06.882.252 1.122.132.301-.15.409-1.055.986-1.209.361-.096.839.293 1.074.146.497-.31.875-1.291 1.577-1.094.793.222.513 1.426.312 1.919-.144.353.257.48.399.74.266.487-.619 1.26-.729 1.689-.042.159.348.354.446.458.589.63-.929 1.743-1.337 2.091-.122.103-.385.311-.4.477-.031.353.349.611.251 1.042-.333 1.462-2.369 1.031-3.168 1.298-.322.107-.369.762-.568 1.035-.64.878-3.445.03-4.198.717-.45.411-.813 1.118-1.061 1.672l.139-1.594z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M195.702 115.511c.528-.026.857-.987 1.017-1.405a5.098 5.098 0 00.322-1.669c.032-.844-.571-2.327.739-2.126 0 0 .276-1.978.843-2.434.37-.297 1.029-.129 1.188-.246.378-.279.231-1.485 1.046-1.639.315-.06.882.252 1.122.132.301-.15.409-1.055.986-1.209.361-.096.839.293 1.074.146.497-.31.875-1.291 1.577-1.094.793.222.513 1.426.312 1.919-.144.353.257.48.399.74.266.487-.619 1.26-.729 1.689-.042.159.348.354.446.458.589.63-.929 1.743-1.337 2.091-.122.103-.385.311-.4.477-.031.353.349.611.251 1.042-.333 1.462-2.369 1.031-3.168 1.298-.322.107-.369.762-.568 1.035-.64.878-3.445.03-4.198.717-.45.411-.813 1.118-1.061 1.672l.139-1.594h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M196.122 115.135c.089-.025 3.4-2.661 5.26-4.681 1.523-1.654 3.547-4.545 3.547-4.545s-1.999 2.948-3.488 4.661c-1.883 2.167-5.229 5.063-5.318 5.089-.089.024-.09-.499-.001-.524z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".169" d="M196.122 115.135c.089-.025 3.4-2.661 5.26-4.681 1.523-1.654 3.547-4.545 3.547-4.545s-1.999 2.948-3.488 4.661c-1.883 2.167-5.229 5.063-5.318 5.089-.089.024-.09-.499-.001-.524h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M196.885 106.667c-.779-.453-.664-2.878.476-3.978.959-.926 3.64 1.8 2.808 2.927-.46.624-2.462 1.528-3.284 1.051z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M196.885 106.667c-.779-.453-.664-2.878.476-3.978.959-.926 3.64 1.8 2.808 2.927-.46.624-2.462 1.528-3.284 1.051h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M200.187 105.198c-.482.396-1.602-.121-2.17-.69-.521-.522-.837-1.32-.526-1.688.614-.729 2.325-1.775 3.148-1.085.819.686-.015 3.105-.452 3.463z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M200.187 105.198c-.482.396-1.602-.121-2.17-.69-.521-.522-.837-1.32-.526-1.688.614-.729 2.325-1.775 3.148-1.085.819.686-.015 3.105-.452 3.463z"/>
    <path fill="#A48253" fill-rule="nonzero" d="M200.03 105.512c-.438.593-2.348 1.356-3.056.945-.616-.358-.609-2.564.509-3.644.705-.68 3.335 1.632 2.547 2.699z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M200.03 105.512c-.438.593-2.348 1.356-3.056.945-.616-.358-.609-2.564.509-3.644.705-.68 3.335 1.632 2.547 2.699h0z"/>
    <path fill="#D99F31" fill-rule="nonzero" d="M200.468 101.927c.682.573.022 2.795-.393 3.136-.352.291-1.355-.097-1.935-.678-.528-.529-.779-1.268-.575-1.51.631-.749 2.221-1.52 2.903-.948z"/>
    <path fill="#F1BF31" fill-rule="nonzero" d="M199.63 102.915c0-.552.241-1.015.563-1.136a.806.806 0 01.274.147c.419.352.331 1.326.105 2.104a.478.478 0 01-.212.05c-.403.001-.73-.521-.73-1.165z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".409" d="M200.468 101.927c.682.573.022 2.795-.393 3.136-.352.291-1.355-.097-1.935-.678-.528-.529-.779-1.268-.575-1.51.631-.749 2.221-1.52 2.903-.948h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M185.657 107.548c-.218-.672-1.254-1.135-1.362-1.944-.049-.368.353-.993.32-1.102-.179-.584-1.153-1.337-.654-2.063.644-.938 1.561.03 2.324.252.125-.048.167-.467.466-.547 1.169-.311 2.186 1.588 2.493 1.703.906.343 1.617-1.26 2.11 2.244.388-.093.915-.188 1.149.21.964 1.651.401 5.21 2.327 6.166l.063.126c-.026.14-.089 1.192-.372.849-.487-.59-.87-1.43-1.718-1.578-.895-.157-4.801 1.257-4.906-.518-.012-.209.271-.497.177-.7-.223-.489-2.57-.318-2.816-1.629-.096-.515.265-.984.399-1.469z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".324" d="M185.657 107.548c-.218-.672-1.254-1.135-1.362-1.944-.049-.368.353-.993.32-1.102-.179-.584-1.153-1.337-.654-2.063.644-.938 1.561.03 2.324.252.125-.048.167-.467.466-.547 1.169-.311 2.186 1.588 2.493 1.703.906.343 1.617-1.26 2.11 2.244.388-.093.915-.188 1.149.21.964 1.651.401 5.21 2.327 6.166l.063.126c-.026.14-.089 1.192-.372.849-.487-.59-.87-1.43-1.718-1.578-.895-.157-4.801 1.257-4.906-.518-.012-.209.271-.497.177-.7-.223-.489-2.57-.318-2.816-1.629-.096-.515.265-.984.399-1.469h0z"/>
    <path fill="#658D5C" fill-rule="nonzero" d="M194.592 113.226c-1.436-2.229-2.079-1.388-4.26-1.224-.498.037-2.176.107-2.146-.733.009-.239.19-.302.041-.59-.317-.617-2.283-.504-2.579-1.646-.153-.591.171-1.388.089-1.61-.274-.742-1.495-1.436-1.132-2.407.048-.129.12-.248.143-.387.073-.429-.804-1.412-.413-2.045.453-.736 1.245-.155 1.848.118.431.196.322-.186.608-.28.498-.165 1.119.311 1.452.63.187.18.778.885.928.955.352.163.95-.023 1.358.121.367.129.615 1.668.67 2.061l.071.049c.334-.072.826-.069 1.026.256.911 1.486.463 5.032 2.417 6.109l-.121.623z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".324" d="M194.592 113.226c-1.436-2.229-2.079-1.388-4.26-1.224-.498.037-2.176.107-2.146-.733.009-.239.19-.302.041-.59-.317-.617-2.283-.504-2.579-1.646-.153-.591.171-1.388.089-1.61-.274-.742-1.495-1.436-1.132-2.407.048-.129.12-.248.143-.387.073-.429-.804-1.412-.413-2.045.453-.736 1.245-.155 1.848.118.431.196.322-.186.608-.28.498-.165 1.119.311 1.452.63.187.18.778.885.928.955.352.163.95-.023 1.358.121.367.129.615 1.668.67 2.061l.071.049c.334-.072.826-.069 1.026.256.911 1.486.463 5.032 2.417 6.109l-.121.623h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M194.48 113.123s-2.594-4.291-4.834-6.536c-1.429-1.431-3.99-3.311-3.99-3.311s2.586 1.961 3.99 3.473c2.043 2.198 4.311 6.374 4.311 6.374h.523z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".169" d="M194.48 113.123s-2.594-4.291-4.834-6.536c-1.429-1.431-3.99-3.311-3.99-3.311s2.586 1.961 3.99 3.473c2.043 2.198 4.311 6.374 4.311 6.374h.523z"/>
    <path fill="#000" fill-rule="nonzero" d="M191.706 119.73c.116.055.363.012.517.054 1.143.312-.493 3.627-.586 4.355-.024.194.1.821.088.863l-.249 1.411c-.044.146.027-1.404-.198-1.79-.398-.685-1.655-.315-2.301-.415-1.067-.164-.234-1.077-.698-1.262-.765-.306-2.362.131-2.552-1.008-.071-.425.209-.88.181-1.243-.549-.635-1.958-.777-1.795-1.893.056-.387.62-.798.605-1.048-.02-.304-1.181-1.156-.904-1.776.17-.379.497-.381.529-.602.033-.229-.942-1.692-.122-2.087.983-.474 1.796.635 2.504.687.359.026.676-.561 1.219-.44.572.128.636.895.897 1.321.253.009.501-.261.782-.29 1.199-.126 1.014 1.795 1.322 2.456.037.08.398-.138.546-.079.251.097.344.377.38.621.106.717-.278 1.486-.165 2.165z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M191.706 119.73c.116.055.363.012.517.054 1.143.312-.493 3.627-.586 4.355-.024.194.1.821.088.863l-.249 1.411c-.044.146.027-1.404-.198-1.79-.398-.685-1.655-.315-2.301-.415-1.067-.164-.234-1.077-.698-1.262-.765-.306-2.362.131-2.552-1.008-.071-.425.209-.88.181-1.243-.549-.635-1.958-.777-1.795-1.893.056-.387.62-.798.605-1.048-.02-.304-1.181-1.156-.904-1.776.17-.379.497-.381.529-.602.033-.229-.942-1.692-.122-2.087.983-.474 1.796.635 2.504.687.359.026.676-.561 1.219-.44.572.128.636.895.897 1.321.253.009.501-.261.782-.29 1.199-.126 1.014 1.795 1.322 2.456.037.08.398-.138.546-.079.251.097.344.377.38.621.106.717-.278 1.486-.165 2.165h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M199.304 122.608c.225 2.264-2.844.034-4.532.934-.504.268-1.682 1.17-1.596.953l.425-1.07c.14-.118.593-.12.752-.192.366-.167.866-.746 1.202-1.374.465-.871.148-1.856.43-2.496.433-.979 1.11-.079 1.402-.29.541-.392.632-2.371 1.952-2.391.348-.005.341.062.582.178.275-.4.363-.892.897-1.059.385-.121.649.222 1.005.219.255-.2 1.245-1.858 2.056-1.664.912.219.362 1.841.294 2.435.101.111.227.108.386.322.628.84-.356 1.737-.694 2.398.089.198.343.187.398.439.225 1.026-1.723 1.152-1.924 1.426 0 .095.15.205.133.419-.1 1.255-3.011.919-3.168.813z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M199.304 122.608c.225 2.264-2.844.034-4.532.934-.504.268-1.682 1.17-1.596.953l.425-1.07c.14-.118.593-.12.752-.192.366-.167.866-.746 1.202-1.374.465-.871.148-1.856.43-2.496.433-.979 1.11-.079 1.402-.29.541-.392.632-2.371 1.952-2.391.348-.005.341.062.582.178.275-.4.363-.892.897-1.059.385-.121.649.222 1.005.219.255-.2 1.245-1.858 2.056-1.664.912.219.362 1.841.294 2.435.101.111.227.108.386.322.628.84-.356 1.737-.694 2.398.089.198.343.187.398.439.225 1.026-1.723 1.152-1.924 1.426 0 .095.15.205.133.419-.1 1.255-3.011.919-3.168.813h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M179.596 130.076c.057-.429-.84-1.08-.463-1.67.306-.478 1.004-.231 1.285-.477.242-.76-.16-1.785.895-2.021.535-.119.83.424 1.003.836.065.153.303 1.101.395 1.134.198.071.722-.304.984-.266.715.103.151 1.44.368 1.818.153.266.533.056.776.14.721.248.161 1.447.161 1.96 0 .314.519.409.562.942.076.955-.731 1.058-1.19 1.675-.064.086-.079.129-.007.216.79.963.343 1.433-.532 2.069-1.03.748-2.616 1.225-2.034 2.824 0 0-.521 1.069-.577.713-.083-.523.171-1.057.026-1.627-.155-.607-2.011-1.405-2.085-2.06-.053-.462.483-.531.525-.947.049-.473-1.61-.909-1.416-1.846.105-.506.645-.655.881-1.064-.038-.398-1.888-1.811-.264-2.179.206-.045.531-.042.707-.17z"/>
    <path fill="#A48253" fill-rule="nonzero" d="M181.217 139.923c.028-.243.109-.751.261-.821.115-.054.226.084.297.205-.046.091-.149.29-.254.459l-.282.254a.154.154 0 01-.016-.05l-.006-.047z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M179.596 130.076c.057-.429-.84-1.08-.463-1.67.306-.478 1.004-.231 1.285-.477.242-.76-.16-1.785.895-2.021.535-.119.83.424 1.003.836.065.153.303 1.101.395 1.134.198.071.722-.304.984-.266.715.103.151 1.44.368 1.818.153.266.533.056.776.14.721.248.161 1.447.161 1.96 0 .314.519.409.562.942.076.955-.731 1.058-1.19 1.675-.064.086-.079.129-.007.216.79.963.343 1.433-.532 2.069-1.03.748-2.616 1.225-2.034 2.824 0 0-.521 1.069-.577.713-.083-.523.171-1.057.026-1.627-.155-.607-2.011-1.405-2.085-2.06-.053-.462.483-.531.525-.947.049-.473-1.61-.909-1.416-1.846.105-.506.645-.655.881-1.064-.038-.398-1.888-1.811-.264-2.179.206-.045.531-.042.707-.17h0z"/>
    <path fill="#658D5C" fill-rule="nonzero" d="M181.156 139.844c.063-.461.119-1.084.002-1.536-.17-.654-1.611-1.477-1.675-2.044-.041-.361.234-.456.292-.907.078-.608-1.421-.998-1.243-1.836.108-.513.749-.758.688-1.214-.034-.254-.793-.881-.631-1.532.132-.527.703-.317 1.035-.582.364-.29-.713-1.174-.37-1.706.272-.422 1.018-.237 1.229-.536.219-.309-.175-1.6.863-1.841.821-.19.988 1.572 1.336 1.828.329.243.479-.192.994-.122.508.069.116 1.31.317 1.683.217.405.562.083.913.209.567.203.008 1.405.008 1.823 0 .437.462.436.503.953.067.84-.732 1.053-1.155 1.571-.117.142-.127.275.002.414.761.82.224 1.312-.516 1.846-1.149.831-2.499 1.29-1.921 2.963l-.671.566z"/>
    <path fill="#A48253" fill-rule="nonzero" d="M181.237 139.776c.038-.259.116-.616.241-.675.119-.055.233.092.304.215l-.545.46z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M181.156 139.844c.063-.461.119-1.084.002-1.536-.17-.654-1.611-1.477-1.675-2.044-.041-.361.234-.456.292-.907.078-.608-1.421-.998-1.243-1.836.108-.513.749-.758.688-1.214-.034-.254-.793-.881-.631-1.532.132-.527.703-.317 1.035-.582.364-.29-.713-1.174-.37-1.706.272-.422 1.018-.237 1.229-.536.219-.309-.175-1.6.863-1.841.821-.19.988 1.572 1.336 1.828.329.243.479-.192.994-.122.508.069.116 1.31.317 1.683.217.405.562.083.913.209.567.203.008 1.405.008 1.823 0 .437.462.436.503.953.067.84-.732 1.053-1.155 1.571-.117.142-.127.275.002.414.761.82.224 1.312-.516 1.846-1.149.831-2.499 1.29-1.921 2.963l-.671.566h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M199.647 126.399c.572-.147 1.356-.705 1.909-.264 1.046.836-.776 2.3-1.113 3.083-.006.286.582.383.544.958-.067 1.011-1.414.796-2.048 1.157-.05.182.232.34.232.52 0 1.432-2.162.891-2.986 1.159-.263.085.18.843-.758 1.198-.405.152-.984-.008-1.395-.08-.497-.088-1.004-.191-1.513-.163-.209.012-.424.067-.635.049-.09-.008-.155-.052-.121-.153l2.362-6.97c.051-.149 1.077.239 1.306.239.255 0 .504-.534.682-.696.617-.563.914.237 1.429.24.376.002 1.432-2.095 2.105-.277z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M199.647 126.399c.572-.147 1.356-.705 1.909-.264 1.046.836-.776 2.3-1.113 3.083-.006.286.582.383.544.958-.067 1.011-1.414.796-2.048 1.157-.05.182.232.34.232.52 0 1.432-2.162.891-2.986 1.159-.263.085.18.843-.758 1.198-.405.152-.984-.008-1.395-.08-.497-.088-1.004-.191-1.513-.163-.209.012-.424.067-.635.049-.09-.008-.155-.052-.121-.153l2.362-6.97c.051-.149 1.077.239 1.306.239.255 0 .504-.534.682-.696.617-.563.914.237 1.429.24.376.002 1.432-2.095 2.105-.277h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M187.937 127.535c-.018-.295-.611.602-.611.602s.421.415.708.661c.353.305.946.733.946.733l.057-.441c-.001-.001-1.048-.733-1.1-1.555z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".291" d="M187.937 127.535c-.018-.295-.611.602-.611.602s.421.415.708.661c.353.305.946.733.946.733l.057-.441c-.001-.001-1.048-.733-1.1-1.555z"/>
    <path fill="#000" fill-rule="nonzero" d="M187.308 124.694c.161.227.06.534-.05.762-.12.252-.191.402-.356.621-.539.714-1.735 1.352-2.276.981-.492-.337-1.498-3.293-.711-3.802.749-.485 2.692.445 3.393 1.438z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M187.308 124.694c.161.227.06.534-.05.762-.12.252-.191.402-.356.621-.539.714-1.735 1.352-2.276.981-.492-.337-1.498-3.293-.711-3.802.749-.485 2.692.445 3.393 1.438z"/>
    <path fill="#000" fill-rule="nonzero" d="M184.464 126.011c.234-.354.575-.622.908-.876.474-.363 1.68-1.004 2.212-.49.699.675 1.035 2.715.224 3.423-1.069.935-2.799-.142-3.473-1.066-.231-.319-.063-.7.129-.991z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M184.464 126.011c.234-.354.575-.622.908-.876.474-.363 1.68-1.004 2.212-.49.699.675 1.035 2.715.224 3.423-1.069.935-2.799-.142-3.473-1.066-.231-.319-.063-.7.129-.991h0z"/>
    <path fill="#A48253" fill-rule="nonzero" d="M185.485 125.319c.73-.543 1.56-.844 1.874-.514.842.889.885 2.589.3 3.086-.589.5-1.959.46-3.141-1.033-.25-.316.229-.991.967-1.539z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M185.485 125.319c.73-.543 1.56-.844 1.874-.514.842.889.885 2.589.3 3.086-.589.5-1.959.46-3.141-1.033-.25-.316.229-.991.967-1.539z"/>
    <path fill="#D99F31" fill-rule="nonzero" d="M187.117 124.596c.395.726-1.758 2.75-2.476 2.271-.498-.332-1.177-2.879-.688-3.32.691-.625 2.822.418 3.164 1.049z"/>
    <path fill="#F1BF31" fill-rule="nonzero" d="M185.405 124.298c-.677-.302-1.342-.428-1.537-.632a.377.377 0 01.084-.12c.15-.135.367-.192.618-.192.659-.001 1.556.391 2.105.794.281.265.415.531.33.723-.157.355-.723-.183-1.6-.573z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".409" d="M187.117 124.596c.395.726-1.758 2.75-2.476 2.271-.498-.332-1.177-2.879-.688-3.32.691-.625 2.822.418 3.164 1.049h0z"/>
    <path fill="#466343" fill-rule="nonzero" d="M192.515 133.852c1.077-.052 2.106.426 2.757.191.969-.349.378-1.103.845-1.145.467-.043 2.825.13 2.822-1.043 0-.226-.19-.314-.105-.568.085-.255 1.859-.169 1.922-1.117.04-.604-.51-.611-.425-.977.085-.366 1.985-2.163 1.038-2.907-.652-.513-1.72.382-1.821.121-.132-.341-.161-.626-.593-.667-.625-.057-1.003.938-1.411.938-.409 0-.595-.409-1.014-.371-.548.048-.631.885-1.098.885-.467 0-1.196-.259-1.196-.259l-2.362 6.969c.001-.001.391-.038.641-.05z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M192.515 133.852c1.077-.052 2.106.426 2.757.191.969-.349.378-1.103.845-1.145.467-.043 2.825.13 2.822-1.043 0-.226-.19-.314-.105-.568.085-.255 1.859-.169 1.922-1.117.04-.604-.51-.611-.425-.977.085-.366 1.985-2.163 1.038-2.907-.652-.513-1.72.382-1.821.121-.132-.341-.161-.626-.593-.667-.625-.057-1.003.938-1.411.938-.409 0-.595-.409-1.014-.371-.548.048-.631.885-1.098.885-.467 0-1.196-.259-1.196-.259l-2.362 6.969c.001-.001.391-.038.641-.05z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".234" d="M193.671 131.105s2.218-.84 3.522-1.6c1.246-.726 2.999-2.144 2.999-2.144m-6.521 3.744s2.218-.723 3.522-1.483c1.246-.727 2.999-2.26 2.999-2.26"/>
    <path fill="#000" fill-rule="nonzero" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".291" d="M179.487 142.135s.878-.213 1.23-.213.56.17.56.17l.4-.734s-1.195.142-1.549.11l-.641.667h0z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M174.188 150.017c-.698-.851 1.616-3.058 2.478-2.433.617.448 1.392 2.368.778 2.941-.686.638-2.701.17-3.256-.508h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M173.122 147.213l-.257-.018c-.452-.563-.616-.586-1.278-.766-.211-.057-.24-.279-.013-.34l.801-.21c.127-.033.466.27.563.342.153.114.93.28.645.555l-.461.437z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".291" d="M173.122 147.213l-.257-.018c-.452-.563-.616-.586-1.278-.766-.211-.057-.24-.279-.013-.34l.801-.21c.127-.033.466.27.563.342.153.114.93.28.645.555l-.461.437h0z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M172.946 146.71c.327-.492 2.357-.609 3.54.434.728.643.142 1.549-.548 2.134-.684.579-1.719 1.057-2.144.686-1.206-1.055-1.241-2.662-.848-3.254z"/>
    <path fill="#000" fill-rule="nonzero" d="M100.709 138.391l.033-.099c2.002-.272 3.877-.726 5.824-1.22 4.314-1.093 6.335-.205 9.831 2.187.219.15.478.359.729.453.567.214.931.065 1.478.065l.05.01.82.641c.062.049.025.092-.034.104-.714.147-1.689.145-2.436.077-1.127-.103-3.448 2.569-6.751 2.569-4.185.001-6.936-1.851-9.544-4.787z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M100.709 138.391l.033-.099c2.002-.272 3.877-.726 5.824-1.22 4.314-1.093 6.335-.205 9.831 2.187.219.15.478.359.729.453.567.214.931.065 1.478.065l.05.01.82.641c.062.049.025.092-.034.104-.714.147-1.689.145-2.436.077-1.127-.103-3.448 2.569-6.751 2.569-4.185.001-6.936-1.851-9.544-4.787h0z"/>
    <path fill="#658D5C" fill-rule="nonzero" d="M119.114 140.368c-.653.023-1.349.023-1.994.131-1.925.324-3.83 2.421-6.757 2.505-3.894.112-6.354-1.722-9.015-4.34 3.873-.582 6.866-2.818 10.862-1.561 2.049.645 4.457 3.225 6.38 2.855l.524.41z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M119.114 140.368c-.653.023-1.349.023-1.994.131-1.925.324-3.83 2.421-6.757 2.505-3.894.112-6.354-1.722-9.015-4.34 3.873-.582 6.866-2.818 10.862-1.561 2.049.645 4.457 3.225 6.38 2.855l.524.41h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M116.705 139.855s-3.417.561-5.544.567c-3.437.008-8.697-1.425-8.697-1.425s5.245 1.587 8.697 1.657c2.123.043 5.544-.45 5.544-.45v-.349z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".169" d="M116.705 139.855s-3.417.561-5.544.567c-3.437.008-8.697-1.425-8.697-1.425s5.245 1.587 8.697 1.657c2.123.043 5.544-.45 5.544-.45v-.349h0z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".291" d="M107.42 98.58c-.065.146-.259.078-.219-.077.028-.108.007-.238-.06-.399-.068-.167-.174-.344-.286-.533-.117-.197-.241-.404-.339-.62a2.041 2.041 0 01-.191-.7c-.014-.241.033-.492.159-.58.113-.079.22-.009.272.074a.904.904 0 01.101.354c.038.298.217.68.42.98.101.15.203.273.289.35a.47.47 0 00.103.076.07.07 0 00.021.008l.137.158-.407.909z"/>
    <path fill="#000" fill-rule="nonzero" d="M127.275 145.855c-.017-.712.124-.447.891-.134.164.066.043.226-.097.314-.459.287-.864.894-1.058.758-.528-.37-.196-.383.264-.938z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".291" d="M127.275 145.855c-.017-.712.124-.447.891-.134.164.066.043.226-.097.314-.459.287-.864.894-1.058.758-.528-.37-.196-.383.264-.938h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M108.935 95.601c-2.26 5.745-3.022 15.917-2.241 20.535 2.403 14.2 14.098 27.513 24.805 29.664 9.312 1.872 14.988 6.044 18.695 10.493 2.423 2.908 3.716 4.229 7.195 10.02 1.271 2.117-.903 2.545-1.8.79-2.448-4.787-3.767-6.914-7.14-10.677-3.622-4.041-8.95-7.669-17.646-9.405-10.37-2.07-22.518-15.407-24.921-29.606-.782-4.62-.098-15.649 2.273-21.478l.78-.336z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M108.935 95.601c-2.26 5.745-3.022 15.917-2.241 20.535 2.403 14.2 14.098 27.513 24.805 29.664 9.312 1.872 14.988 6.044 18.695 10.493 2.423 2.908 3.716 4.229 7.195 10.02 1.271 2.117-.903 2.545-1.8.79-2.448-4.787-3.767-6.914-7.14-10.677-3.622-4.041-8.95-7.669-17.646-9.405-10.37-2.07-22.518-15.407-24.921-29.606-.782-4.62-.098-15.649 2.273-21.478l.78-.336"/>
    <path fill="#000" fill-rule="nonzero" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M124.359 145.25c-1.613.177-2.704 2.519-4.323 3.993-2.981 2.713-7.757 2.984-10.802 1.172-.128-.076-.192-.246-.048-.28 3.786-.859 4.28-6.026 12.173-5.517.925.06 1.316.249 2.245.249l.755.383z"/>
    <path fill="#000" fill-rule="nonzero" d="M118.365 121.226l.052.025c.04 2.308 1.79 5.456 2.219 7.994.37 2.186-.163 4.014-1.001 6.051-.21.508-.701 1.528-.72 2.028-.018.465.274.896.521 1.282.086.135.609.821.555.954-2.091-1.811-.861-.63-1.731-2.3-.449-.861-.983-1.643-1.428-2.51-2.348-4.58-2.391-9.774 1.533-13.524z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M118.365 121.226l.052.025c.04 2.308 1.79 5.456 2.219 7.994.37 2.186-.163 4.014-1.001 6.051-.21.508-.701 1.528-.72 2.028-.018.465.274.896.521 1.282.086.135.609.821.555.954-2.091-1.811-.861-.63-1.731-2.3-.449-.861-.983-1.643-1.428-2.51-2.348-4.58-2.391-9.774 1.533-13.524z"/>
    <path fill="#000" fill-rule="nonzero" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M133.459 146.239c-1.027-.332-2.073-.741-2.636-1.792-.947-1.765-.744-3.191-1.716-5.017-1.842-3.464-5.389-3.837-7.179-7.048-.363 2.955-.759 7.37 3.841 10.527 1.62 1.111 4.089 1.366 4.684 1.813.596.446.954 1.084.954 1.084l2.052.433h0z"/>
    <path fill="#658D5C" fill-rule="nonzero" d="M131.467 145.699a8.13 8.13 0 00-1.118-1.012c-.939-.679-2.972-.743-4.507-1.868-2.143-1.572-3.255-3.033-3.85-5.624-.361-1.571-.083-2.827-.01-4.134 1.931 2.805 5.285 3.069 7.08 6.424 1.091 2.039.946 5.139 3.181 6.378l-.776-.164z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M131.467 145.699a8.13 8.13 0 00-1.118-1.012c-.939-.679-2.972-.743-4.507-1.868-2.143-1.572-3.255-3.033-3.85-5.624-.361-1.571-.083-2.827-.01-4.134 1.931 2.805 5.285 3.069 7.08 6.424 1.091 2.039.946 5.139 3.181 6.378l-.776-.164h0z"/>
    <path fill="#000" fill-rule="nonzero" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".291" d="M113.134 130.274s-.128.154-.308.667c-.08.23.425.924.384.683-.103-.589.187-1.003.187-1.003l-.263-.347z"/>
    <path fill="#658D5C" fill-rule="nonzero" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M113.916 134.163s-1.232-.265-1.777-.537c-.947-.474-3.043-3.223-5.465-4.599-3.473-1.972-6.497-1.56-9.914-2.559.97 3.646 2.823 5.827 6.265 7.373 3.082 1.384 8.115.187 8.661.232.545.046 2.276.773 2.276.773l-.046-.683h0z"/>
    <path fill="#466343" fill-rule="nonzero" d="M118.804 138.34c-.152-1.085-1.385-2.646-1.901-3.656-.756-1.481-1.323-3.096-1.492-4.756-.364-3.576.456-5.506 2.81-8.156.017.777.122 1.049.356 1.772.985 3.034 2.364 5.542 1.917 8.841-.22 1.624-1.547 3.937-1.607 4.929-.031.514.259 1.117.515 1.55l-.598-.524z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M118.804 138.34c-.152-1.085-1.385-2.646-1.901-3.656-.756-1.481-1.323-3.096-1.492-4.756-.364-3.576.456-5.506 2.81-8.156.017.777.122 1.049.356 1.772.985 3.034 2.364 5.542 1.917 8.841-.22 1.624-1.547 3.937-1.607 4.929-.031.514.259 1.117.515 1.55l-.598-.524h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M109.971 128.668c-2.526-4.199-8.45-2.317-12.333-5.843-1.884-1.711-2.726-4.287-3.102-6.737-.112-.729-.424-3.621-.254-4.183.031-.104.136-.129.217-.05 1.287 1.258 2.577 2.071 4.068 3.08 3.54 2.396 4.993 4.644 6.817 8.38 2.389 4.894 4.554 2.106 4.587 5.353z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M109.971 128.668c-2.526-4.199-8.45-2.317-12.333-5.843-1.884-1.711-2.726-4.287-3.102-6.737-.112-.729-.424-3.621-.254-4.183.031-.104.136-.129.217-.05 1.287 1.258 2.577 2.071 4.068 3.08 3.54 2.396 4.993 4.644 6.817 8.38 2.389 4.894 4.554 2.106 4.587 5.353h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M117.849 123.038s-.802 4.201-.681 6.903c.125 2.76 1.349 6.956 1.349 6.956h-.233s-1.269-4.194-1.348-6.956c-.077-2.715.913-6.903.913-6.903z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".169" d="M117.849 123.038s-.802 4.201-.681 6.903c.125 2.76 1.349 6.956 1.349 6.956h-.233s-1.269-4.194-1.348-6.956c-.077-2.715.913-6.903.913-6.903h0z"/>
    <path fill="#658D5C" fill-rule="nonzero" d="M109.517 127.986c-1.74-2.036-4.611-2.292-7.856-3.074-1.552-.374-2.82-1.222-4.004-2.288-1.837-1.657-2.445-3.878-2.803-6.262a18.295 18.295 0 01-.166-3.983c.148.261.335.518.581.694 1.205.864 2.461 1.466 3.669 2.338 1.055.762 1.804 1.265 2.669 2.24a20.292 20.292 0 012.776 4.025c.606 1.15 1.126 2.498 1.911 3.541.406.542 2.046 1.246 2.643 1.322l.58 1.447z"/>
    <path fill="#A48253" fill-rule="nonzero" d="M106.481 125.409l-.152.405.021.226c1.186.388 2.217.913 3.025 1.784l.107.07-.545-1.356c-.527-.066-1.871-.626-2.456-1.129z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M109.517 127.986c-1.74-2.036-4.611-2.292-7.856-3.074-1.552-.374-2.82-1.222-4.004-2.288-1.837-1.657-2.445-3.878-2.803-6.262a18.295 18.295 0 01-.166-3.983c.148.261.335.518.581.694 1.205.864 2.461 1.466 3.669 2.338 1.055.762 1.804 1.265 2.669 2.24a20.292 20.292 0 012.776 4.025c.606 1.15 1.126 2.498 1.911 3.541.406.542 2.046 1.246 2.643 1.322l.58 1.447h0z"/>
    <path fill="#D99F31" fill-rule="nonzero" d="M102.485 112.64a1.655 1.655 0 110 3.313 1.656 1.656 0 010-3.313z"/>
    <path fill="#F1BF31" fill-rule="nonzero" d="M102.544 113.496c0-.316.081-.607.215-.834.785.13 1.383.812 1.383 1.635 0 .068-.004.136-.012.202-.168.2-.387.321-.627.321-.53-.001-.959-.594-.959-1.324z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M102.485 112.64a1.655 1.655 0 110 3.313 1.656 1.656 0 010-3.313z"/>
    <path fill="#000" fill-rule="nonzero" d="M107.38 113.561s.773-2.878 1.501-4.66c1.721-4.217 4.707-4.376 6.228-7.715.871 2.255 1.567 5.154.045 8.405-1.756 3.756-6.251 5.96-6.251 5.96l-1.523-1.99z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M107.38 113.561s.773-2.878 1.501-4.66c1.721-4.217 4.707-4.376 6.228-7.715.871 2.255 1.567 5.154.045 8.405-1.756 3.756-6.251 5.96-6.251 5.96l-1.523-1.99h0z"/>
    <path fill="#466343" fill-rule="nonzero" d="M115.095 101.943c2.243 6.124-.719 10.533-6.157 13.574l-1.433-1.98c.935-3.933 1.606-5.869 4.611-8.664 1.188-1.105 2.229-1.464 2.979-2.93z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M115.095 101.943c2.243 6.124-.719 10.533-6.157 13.574l-1.433-1.98c.935-3.933 1.606-5.869 4.611-8.664 1.188-1.105 2.229-1.464 2.979-2.93h0z"/>
    <path fill="#A48253" fill-rule="nonzero" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M108.767 95.874c-2.239 5.886-2.901 15.727-2.13 20.282 2.409 14.234 14.073 27.596 24.838 29.759 9.283 1.865 14.834 6.046 18.656 10.367a46.947 46.947 0 014.412 5.847c1.02 1.597 1.894 3.34 2.752 4.718.139.223-1.02.702-1.142.504-.907-1.466-1.741-3.315-2.786-5.009-1.189-1.926-3.231-3.897-5.007-5.994-3.779-4.462-8.812-7.816-17.535-9.557-10.314-2.059-22.317-15.232-24.714-29.396-.779-4.601-.205-15.281 1.846-20.919l.81-.602h0z"/>
    <path fill="#000" fill-rule="nonzero" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M107.645 123.724c-.463-.443-1.128-.866-1.573-1.328-4.135-4.292-3.049-11.789 1.486-15.451.154-.124.271-.066.293.117.182 1.511.417 2.932 1.023 4.596 1.384 3.8 1.609 5.98.352 9.216-.767 1.972.002 3.712.002 3.712l-1.583-.862h0z"/>
    <path fill="#000" fill-rule="nonzero" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".234" d="M122.227 142.204c-2.646-.905-5.379-.662-7.579-2.946-2.539-2.634-3.089-6.709-2.425-9.062.032-.114.121-.208.193-.12.564.69.863 1.187 1.604 1.734 2.65 1.955 5.904 2.935 7.028 5.921.224.594.298 1.217.508 1.816.716 2.052 1.89 2.672 3.591 3.792l-2.92-1.135h0z"/>
    <path fill="#658D5C" fill-rule="nonzero" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M124.294 143.293c-2.475-2.099-6.936-1.345-9.593-4.193-1.95-2.091-2.773-5.388-2.289-8.247 2.358 2.101 5.099 2.678 7.243 5.036 2.524 2.777.375 4.64 5.126 7.342l-.487.062h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M111.075 128.229l.007-.064c.615-.733.188-2.934-.013-3.816-.348-1.521-.391-2.828-.138-4.377.292-1.788 1.271-3.403 2.275-4.884.448-.66 1.901-2.721 2.052-3.468.012-.064.08-.059.108.002.554 1.209.844 2.68 1.068 3.987.312 1.83.41 3.493-.225 5.573-.568 1.862-1.332 3.068-2.503 4.321-.495.53-1.66 1.697-1.831 2.327-.031.115-.229.955-.185 1.569l-.615-1.17z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M111.075 128.229l.007-.064c.615-.733.188-2.934-.013-3.816-.348-1.521-.391-2.828-.138-4.377.292-1.788 1.271-3.403 2.275-4.884.448-.66 1.901-2.721 2.052-3.468.012-.064.08-.059.108.002.554 1.209.844 2.68 1.068 3.987.312 1.83.41 3.493-.225 5.573-.568 1.862-1.332 3.068-2.503 4.321-.495.53-1.66 1.697-1.831 2.327-.031.115-.229.955-.185 1.569l-.615-1.17h0z"/>
    <path fill="#000" fill-rule="nonzero" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".355" d="M106.977 104.156s.079.012.411-.211c.331-.222.733-.604.904-1.082.17-.477 1.366-4.448 2.962-6.906 1.865-2.872 3.801-4.16 6.149-6.268.369 2.276.423 5.985-1.501 8.962-1.762 2.725-6.23 4.666-6.747 4.869-.518.202-1.406.352-1.916 1.029-.289.382-.487 1.128-.487 1.128l.225-1.521h0z"/>
    <path fill="#000" fill-rule="nonzero" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M106.416 104.536s-.194-.009-.445-.363c-.25-.354-.501-.901-.501-1.462 0-1.187 1.126-4.741.567-7.431-1.066-5.134-2.679-7.193-2.927-9.098-.099-.76-4.292 5.186-3.017 10.704.632 2.735 3.811 5.791 4.256 6.193.446.402 1.055.923 1.346 1.725.3.828.236 1.77.236 1.77l.485-2.038h0zM108.877 95.704c.301-.735.8-1.606 1.261-1.992 1.133-.947 4.309-3.305 5.393-5.025 2.414-3.828 1.927-8.542 1.675-9.363-2.094 1.82-4.004 3.021-6.139 6.034-1.863 2.63-2.27 7.774-2.414 8.749-.157 1.064-.63 2.214-.63 2.214l.854-.617h0z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".466" d="M100.716 114.296a1.77 1.77 0 113.541.001 1.77 1.77 0 01-3.541-.001z"/>
    <path fill="#658D5C" fill-rule="nonzero" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M192.916 124.924s.981-1.022 1.772-1.419c1.535-.773 4.029.471 4.399-.229.189-.357-.096-.617.142-.712.237-.095 2.959.445 3.067-.786.02-.222-.283-.334-.047-.571.237-.237 1.955-.352 1.783-1.164-.054-.26-.428-.333-.333-.593.095-.261 1.383-1.485.833-2.235-.16-.219-.565-.176-.535-.444.096-.831.376-2.117-.311-2.184-.738-.071-1.601 1.43-1.889 1.487-.358.072-.571-.281-.928-.166-.594.19-.688 1.069-.927 1.069-.237 0-.219-.245-.601-.238-1.166.024-1.263 1.894-1.847 2.354-.333.261-.529-.091-.856-.023-.808.166-.569 2.185-.997 2.828-.429.642-1.355 1.402-1.545 1.498-.191.095-.565.094-.565.094l-.615 1.434h0z"/>
    <path fill="#658D5C" fill-rule="nonzero" d="M106.999 104.989l.084-.74c.574-.22 1.086-.792 1.317-1.347 1-2.394 1.61-4.828 3.016-7.039 1.824-2.87 3.779-3.987 5.751-5.563.092 2.537.539 5.209-1.481 8.533-1.148 1.887-4.493 3.758-6.575 4.579-.93.368-1.597.666-2.112 1.577z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".355" d="M106.999 104.989l.084-.74c.574-.22 1.086-.792 1.317-1.347 1-2.394 1.61-4.828 3.016-7.039 1.824-2.87 3.779-3.987 5.751-5.563.092 2.537.539 5.209-1.481 8.533-1.148 1.887-4.493 3.758-6.575 4.579-.93.368-1.597.666-2.112 1.577h0z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".234" d="M110.511 150.405s2.644-.65 4.236-1.331c2.026-.866 2.845-1.993 4.871-2.859 1.636-.7 4.356-1.358 4.356-1.358m-13.463 5.548s2.639-.782 4.236-1.505c2.007-.909 2.827-2.035 4.87-2.859 1.633-.658 4.356-1.184 4.356-1.184"/>
    <path fill="#000" fill-rule="nonzero" d="M98.418 99.93c3.192 2.658 4.102 3.901 5.041 8.013.11.482.721 2.563.851 2.819.307.612.943.901 1.335 1.404.043.055.149 1.139.165 1.281.01.086-.056.084-.105.04-.497-.454-.758-1.165-1.337-1.59-.498-.366-2.182-.725-2.908-1.025-1.855-.765-3.368-2.197-4.442-3.874-1.418-2.214-1.578-5.227-1.237-7.765.079-.582.157-1.284.364-1.836.029-.076.074-.058.113.002.755 1.179 1.241 1.765 2.16 2.531z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M98.418 99.93c3.192 2.658 4.102 3.901 5.041 8.013.11.482.721 2.563.851 2.819.307.612.943.901 1.335 1.404.043.055.149 1.139.165 1.281.01.086-.056.084-.105.04-.497-.454-.758-1.165-1.337-1.59-.498-.366-2.182-.725-2.908-1.025-1.855-.765-3.368-2.197-4.442-3.874-1.418-2.214-1.578-5.227-1.237-7.765.079-.582.157-1.284.364-1.836.029-.076.074-.058.113.002.755 1.179 1.241 1.765 2.16 2.531h0z"/>
    <path fill="#658D5C" fill-rule="nonzero" d="M105.497 112.271l.088.752c-1.515-2.05-2.022-1.444-4.117-2.312-1.824-.754-3.304-2.161-4.363-3.809-1.582-2.46-1.553-6.101-.884-8.875.849 1.674 4.173 3.354 5.449 5.445.82 1.345 1.33 2.942 1.737 4.457.203.757.433 2.284.746 2.91.233.468.94 1.093 1.344 1.432z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M105.497 112.271l.088.752c-1.515-2.05-2.022-1.444-4.117-2.312-1.824-.754-3.304-2.161-4.363-3.809-1.582-2.46-1.553-6.101-.884-8.875.849 1.674 4.173 3.354 5.449 5.445.82 1.345 1.33 2.942 1.737 4.457.203.757.433 2.284.746 2.91.233.468.94 1.093 1.344 1.432h0z"/>
    <path fill="#658D5C" fill-rule="nonzero" d="M111.262 128.27c.565-.863.07-3.051-.11-4.042-.279-1.533-.299-2.856-.049-4.403.408-2.528 2.008-4.221 3.421-6.27.279-.404.556-.813.775-1.254 1.624 4.572 2.08 9.242-1.46 13.082-.534.58-1.819 1.697-2.016 2.401-.106.377-.142.777-.172 1.167l-.389-.681z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M111.262 128.27c.565-.863.07-3.051-.11-4.042-.279-1.533-.299-2.856-.049-4.403.408-2.528 2.008-4.221 3.421-6.27.279-.404.556-.813.775-1.254 1.624 4.572 2.08 9.242-1.46 13.082-.534.58-1.819 1.697-2.016 2.401-.106.377-.142.777-.172 1.167l-.389-.681h0z"/>
    <path fill="#466343" fill-rule="nonzero" d="M124.743 144.549s-.2.318-1.14.318-3.879-.488-6.23.126c-3.546.927-5.01 4.243-7.675 5.422 4.049 1.744 7.012 1.314 10.381-1.38 1.43-1.143 2.311-3.379 4.279-3.786.898-.185 1.175-.158 1.76.069l-1.375-.769z"/>
    <path fill="#A48253" fill-rule="nonzero" d="M123.667 144.915l.256.41.641-.129.109-.007c.346-.06.586-.077.81-.05l.286-.018-1.025-.573s-.085.134-.401.228l-.035.034-.641.105z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M124.743 144.549s-.2.318-1.14.318-3.879-.488-6.23.126c-3.546.927-5.01 4.243-7.675 5.422 4.049 1.744 7.012 1.314 10.381-1.38 1.43-1.143 2.311-3.379 4.279-3.786.898-.185 1.175-.158 1.76.069m26.409 16.307s.037-.517.634-.724a.661.661 0 01.781.23"/>
    <path fill="#A48253" fill-rule="nonzero" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".291" d="M171.183 146.247s.74.248 1.23.275c.33.018.846-.047.846-.047s-.203.043-.422-.112c-.072-.051-.578-.5-.578-.5l-1.076.384h0z"/>
    <path fill="#A48253" fill-rule="nonzero" d="M173.15 146.865c.545-.618 2.123-.551 3.279.469.575.507.195 1.268-.488 1.871-.688.607-1.561.944-1.857.687-1.156-1.011-1.335-2.574-.934-3.027z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M173.15 146.865c.545-.618 2.123-.551 3.279.469.575.507.195 1.268-.488 1.871-.688.607-1.561.944-1.857.687-1.156-1.011-1.335-2.574-.934-3.027h0z"/>
    <path fill="#D99F31" fill-rule="nonzero" d="M174.319 149.961c-.259-.296.155-1.203.706-1.7.488-.441 1.283-.742 1.455-.557.675.724 1.365 2.013.829 2.651-.536.64-2.406.273-2.99-.394z"/>
    <path fill="#F1BF31" fill-rule="nonzero" d="M176.347 147.941a.773.773 0 01.345.007c.595.742 1.097 1.835.617 2.406-.192.229-.553.329-.966.333-.327-.199-.599-.599-.705-1.097-.168-.792.149-1.53.709-1.649z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".409" d="M174.319 149.961c-.259-.296.155-1.203.706-1.7.488-.441 1.283-.742 1.455-.557.675.724 1.365 2.013.829 2.651-.536.64-2.406.273-2.99-.394z"/>
    <path fill="#A48253" fill-rule="nonzero" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".291" d="M128.178 145.829s-.318.186-.413.269c-.217.195-.503.338-.592.497l-.346-.032c.215-.179.442-.485.565-.634.072-.086.001-.415.001-.415l.785.315h0z"/>
    <path fill="#658D5C" fill-rule="nonzero" d="M108.335 95.84c.636-1.802.622-3.806.978-5.677.751-3.944 1.871-5.226 4.72-7.864.908-.84 1.837-1.451 2.825-2.197.16 1.135.234 2.087.119 3.226-.561 5.572-2.934 6.888-6.913 10.296-.773.66-.972 1.585-1.729 2.216z"/>
    <path fill="#A48253" fill-rule="nonzero" d="M108.486 95.369c.104-.152.203-.24.257-.214.043.021.049.108.023.232a2.872 2.872 0 01-.431.454c.055-.156.105-.313.151-.472z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M108.335 95.84c.636-1.802.622-3.806.978-5.677.751-3.944 1.871-5.226 4.72-7.864.908-.84 1.837-1.451 2.825-2.197.16 1.135.234 2.087.119 3.226-.561 5.572-2.934 6.888-6.913 10.296-.773.66-.972 1.585-1.729 2.216h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M108.584 96.044s.143-.437.359-.994c.216-.557.16.149.16.149l-.127.608-.392.237z"/>
    <path fill="#658D5C" fill-rule="nonzero" d="M106.271 104.61l-.291.935c-.164-1.217-.676-1.759-1.553-2.552-3.818-3.454-5.449-7.519-3.847-12.272.561-1.661 1.1-2.578 2.281-3.874.746 1.278 1.107 2.043 1.624 3.429 1.31 3.519 1.832 5.509 1.313 9.54-.184 1.424-.866 3.158.079 4.424.104.138.229.299.394.37z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M106.271 104.61l-.291.935c-.164-1.217-.676-1.759-1.553-2.552-3.818-3.454-5.449-7.519-3.847-12.272.561-1.661 1.1-2.578 2.281-3.874.746 1.278 1.107 2.043 1.624 3.429 1.31 3.519 1.832 5.509 1.313 9.54-.184 1.424-.866 3.158.079 4.424.104.138.229.299.394.37h0z"/>
    <path fill="#A48253" fill-rule="nonzero" d="M106.162 106.676s.092-.924-.051-1.486a5.439 5.439 0 00-.256-.692s.459.234.565.213c.105-.019-.258 1.965-.258 1.965zM107.721 97.624c-.22.09-.897-.864-.979-1.51-.078-.614-.33-.357-.3.13.055.906 1.139 1.877.989 2.453.204-.494.3-.852.29-1.073z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".291" d="M107.721 97.624c-.22.09-.897-.864-.979-1.51-.078-.614-.33-.357-.3.13.055.906 1.023 1.712.873 2.288"/>
    <path fill="#D99F31" fill-rule="nonzero" d="M104.385 95.067a1.655 1.655 0 011.685-1.627 1.655 1.655 0 11-1.685 1.627z"/>
    <path fill="#F1BF31" fill-rule="nonzero" d="M106.14 94.908c-.449-.315-.645-.808-.439-1.103.206-.294.737-.279 1.187.036.449.315.645.808.439 1.102-.206.295-.738.278-1.187-.035z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M104.385 95.067a1.655 1.655 0 011.685-1.627 1.655 1.655 0 11-1.685 1.627h0z"/>
    <path fill="#A48253" fill-rule="nonzero" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".291" d="M193.943 97.67s.02-.664.543-2.536c.091-.326.339-.507.339-.507l-.977-.134s.249.237.245.546c-.012.874-.482 1.557-.482 1.557l.332 1.074h0z"/>
    <path fill="#658D5C" fill-rule="nonzero" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".317" d="M192.859 95.668s-.635-1.857-1.178-2.535c-.574-.719-.987-.87-2.03-1.203-.923-.295-2.364-.533-2.425-1.256-.061-.723.024-.586-.24-.656-.847-.222-1.906-.503-1.704-1.342.202-.839.515-.97.152-1.146-.536-.258-1.944-1.078-1.776-1.796.168-.719.794-.728.53-1.169-.19-.315-.869-.661-.815-1.235.038-.392.534-.519.519-.868-.015-.349-1.346-1.899-.563-2.546.714-.589 1.975.464 2.297.597.323.133.695-.551 1.167-.438.607.145.639 1.054.818 1.377.328.588.998-.177 1.523.105.875.47.57 2.087.806 2.414.235.327.881-.195 1.34.057.713.394.103 2.755.149 3.142.047.389.565.055.861.285.788.613-.171 2.475-.02 4.046.052.543.182 1.386.182 1.386l.407 2.781h0z"/>
    <path fill="#D99F31" fill-rule="nonzero" d="M114.487 127.442a1.714 1.714 0 110 3.429 1.714 1.714 0 010-3.429z"/>
    <path fill="#F1BF31" fill-rule="nonzero" d="M114.287 128.989c-.366-.504-.378-1.118-.028-1.372s.931-.053 1.296.451c.365.503.378 1.117.028 1.372-.35.254-.93.053-1.296-.451z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M114.487 127.442a1.714 1.714 0 110 3.429 1.714 1.714 0 010-3.429z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".466" d="M112.655 129.157a1.831 1.831 0 113.663 0 1.831 1.831 0 01-3.663 0z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".409" d="M105.995 96.884a1.77 1.77 0 11.062-3.54 1.77 1.77 0 01-.062 3.54z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M153.05 162.206s.019-.522.634-.667a.674.674 0 01.781.347l-1.415.32zm4.126 5.536c-1.121.231-1.806-1.175-1.421-1.521.252-.226.871-.177 1.326.044.812.397.594 1.374.095 1.477z"/>
    <path fill="#A48253" fill-rule="nonzero" d="M157.175 167.742c-.931.237-1.662-1.013-1.42-1.288.222-.254.694-.267 1.148-.046.813.397.766 1.209.272 1.334z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M157.175 167.742c-.931.237-1.662-1.013-1.42-1.288.222-.254.694-.267 1.148-.046.813.397.766 1.209.272 1.334h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M108.289 96.066c1.179-3.435 1.836-5.849 3.788-9.204 1.328-2.281 3.931-5.51 3.931-5.51s-2.452 3.313-3.724 5.61c-1.798 3.248-2.201 4.645-3.636 8.797l-.359.307z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".169" d="M108.289 96.066c1.179-3.435 1.836-5.849 3.788-9.204 1.328-2.281 3.931-5.51 3.931-5.51s-2.452 3.313-3.724 5.61c-1.798 3.248-2.201 4.645-3.636 8.797l-.359.307h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M102.706 88.433s-.43 5.395.248 8.735c.61 3.001 2.728 7.362 2.728 7.362s.118.088-.495-1.528c-.63-1.66-1.433-3.496-1.943-5.843-.724-3.332-.538-8.726-.538-8.726z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".169" d="M102.706 88.433s-.43 5.395.248 8.735c.61 3.001 2.728 7.362 2.728 7.362s.118.088-.495-1.528c-.63-1.66-1.433-3.496-1.943-5.843-.724-3.332-.538-8.726-.538-8.726h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M97.662 102.45s.714 1.867 1.326 2.983c1.68 3.065 3.122 4.668 5.99 6.665l.084-.258c-2.765-1.963-4.065-3.53-5.842-6.407-.689-1.116-1.558-2.983-1.558-2.983z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".169" d="M97.662 102.45s.714 1.867 1.326 2.983c1.68 3.065 3.122 4.668 5.99 6.665l.084-.258c-2.765-1.963-4.065-3.53-5.842-6.407-.689-1.116-1.558-2.983-1.558-2.983z"/>
    <path fill="#000" fill-rule="nonzero" d="M107.931 103.704s3.434-3.17 5.189-5.65c1.552-2.192 3.413-6.079 3.413-6.079s-1.7 3.934-3.206 6.14c-1.691 2.476-4.932 5.588-4.932 5.588h-.464v.001z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".169" d="M107.931 103.704s3.434-3.17 5.189-5.65c1.552-2.192 3.413-6.079 3.413-6.079s-1.7 3.934-3.206 6.14c-1.691 2.476-4.932 5.588-4.932 5.588h-.464v.001z"/>
    <path fill="#000" fill-rule="nonzero" d="M111.295 128.547c.653-1.574 1.636-3.741 2.369-6.237.92-3.132 1.467-4.659 1.572-8.223-.053 3.559-.432 4.789-1.186 7.754-.641 2.524-1.52 4.739-2.173 6.312l-.582.394z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".169" d="M111.295 128.547c.653-1.574 1.636-3.741 2.369-6.237.92-3.132 1.467-4.659 1.572-8.223-.053 3.559-.432 4.789-1.186 7.754-.641 2.524-1.52 4.739-2.173 6.312l-.582.394h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M109.34 112.751s2.586-2.262 3.742-4.286c1.523-2.669 1.779-4.811 1.779-4.811s-.025 2.154-1.547 4.822c-1.155 2.025-3.871 4.608-3.871 4.608l-.103-.333z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".169" d="M109.34 112.751s2.586-2.262 3.742-4.286c1.523-2.669 1.779-4.811 1.779-4.811s-.025 2.154-1.547 4.822c-1.155 2.025-3.871 4.608-3.871 4.608l-.103-.333h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M106.393 125.582s-2.232-1.414-3.455-2.445c-4.746-3.998-7.72-9.387-7.72-9.387s2.717 5.69 7.545 9.62c1.283 1.044 3.629 2.503 3.629 2.503v-.291h.001z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".169" d="M106.393 125.582s-2.232-1.414-3.455-2.445c-4.746-3.998-7.72-9.387-7.72-9.387s2.717 5.69 7.545 9.62c1.283 1.044 3.629 2.503 3.629 2.503v-.291h.001z"/>
    <path stroke="#000" stroke-linejoin="round" stroke-width=".35" d="M106.701 126.155s-.427-.182-.205-.589"/>
    <path fill="#000" fill-rule="nonzero" d="M123.74 142.498c-2.095-1.317-5.042-3.278-7.698-6.006-.909-.935-2.218-2.501-2.218-2.501s1.062 1.658 1.928 2.559c2.368 2.464 5.62 4.777 7.715 6.094l.273-.146z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".169" d="M123.74 142.498c-2.095-1.317-5.042-3.278-7.698-6.006-.909-.935-2.218-2.501-2.218-2.501s1.062 1.658 1.928 2.559c2.368 2.464 5.62 4.777 7.715 6.094l.273-.146h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M130.988 145.014s-3.94-2.826-5.712-5.293c-.964-1.341-1.432-2.169-2.085-3.687.783 1.521 1.314 2.345 2.376 3.687 1.774 2.244 5.421 4.944 5.421 4.944v.349z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".169" d="M130.988 145.014s-3.94-2.826-5.712-5.293c-.964-1.341-1.432-2.169-2.085-3.687.783 1.521 1.314 2.345 2.376 3.687 1.774 2.244 5.421 4.944 5.421 4.944v.349h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M111.481 150.078c5.793-.805 8.046-4.19 12.98-4.958-.52.027-.425-.177-.897-.077-4.351.918-7.012 4.209-12.083 5.035z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".169" d="M111.481 150.078c5.793-.805 8.046-4.19 12.98-4.958-.52.027-.425-.177-.897-.077-4.351.918-7.012 4.209-12.083 5.035z"/>
    <path fill="#D99F31" fill-rule="nonzero" d="M118.55 141.614a1.714 1.714 0 110 3.429 1.714 1.714 0 010-3.429z"/>
    <path fill="#F1BF31" fill-rule="nonzero" d="M120.233 143.002a.655.655 0 01-.067.16c-.252.435-.94.508-1.537.163-.598-.345-.879-.977-.627-1.412a.709.709 0 01.309-.282 1.714 1.714 0 011.922 1.371z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M118.55 141.614a1.714 1.714 0 110 3.429 1.714 1.714 0 010-3.429z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".466" d="M116.719 143.328a1.83 1.83 0 011.831-1.83 1.83 1.83 0 110 3.661 1.83 1.83 0 01-1.831-1.831z"/>
    <path fill="#A48253" fill-rule="nonzero" d="M124.55 144.124s.161.091.26.203c.1.112.136.246.068.331 0 0 .04.098.168.192.172.125.399.116.649.154.318.052.542.095.768.161.227.066.511.184.511.184l-1.285-.64-1.139-.585z"/>
    <path fill="#000" fill-rule="nonzero" d="M112.159 134.081s-3.198-.838-7.283-2.363c-4.086-1.525-6.839-4.077-6.839-4.077s2.753 2.319 6.839 3.844c4.085 1.525 7.283 2.247 7.283 2.247v.349z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".169" d="M112.159 134.081s-3.198-.838-7.283-2.363c-4.086-1.525-6.839-4.077-6.839-4.077s2.753 2.319 6.839 3.844c4.085 1.525 7.283 2.247 7.283 2.247v.349z"/>
    <path fill="#000" fill-rule="nonzero" d="M186.836 149.672c.248.426.698.719.906 1.157.295.615.019 1.24-.724 1.194-.322-.02-.617-.268-.926-.293-.249-.019-.962.934-1.654.903-.942-.041-1.391-1.191-1.966-1.779-.458-.097-.794.578-1.303.486-1.045-.189-.994-1.847-1.355-2.577-.288-.214-.723.239-1.303-.171-.895-.632-.426-2.619-1.387-3.175-.846-.49-2.634-.165-2.478-.247l.997-.518c.062-.033.253.058.315.071 1.634.325 3.82.057 5.495.057.803 0 .674.649.803.781l.065.01c.834.058 1.829-.003 2.54.522.86.639.046 1.209.043 1.353l.039.019.182.061.562.165c.435.14.922.333 1.214.703.425.542-.052.794-.065 1.278z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M186.836 149.672c.248.426.698.719.906 1.157.295.615.019 1.24-.724 1.194-.322-.02-.617-.268-.926-.293-.249-.019-.962.934-1.654.903-.942-.041-1.391-1.191-1.966-1.779-.458-.097-.794.578-1.303.486-1.045-.189-.994-1.847-1.355-2.577-.288-.214-.723.239-1.303-.171-.895-.632-.426-2.619-1.387-3.175-.846-.49-2.634-.165-2.478-.247l.997-.518c.062-.033.253.058.315.071 1.634.325 3.82.057 5.495.057.803 0 .674.649.803.781l.065.01c.834.058 1.829-.003 2.54.522.86.639.046 1.209.043 1.353l.039.019.182.061.562.165c.435.14.922.333 1.214.703.425.542-.052.794-.065 1.278z"/>
    <path fill="#466343" fill-rule="nonzero" d="M175.697 144.822c.361.228 5.17.076 5.816.076.698 0 .47.552.708.76.29.131 3.593.022 2.84 1.27-.086.143-.199.273-.272.423l.017.139c.337.279 1.508.301 2.018.958.304.392-.501 1.022-.156 1.271.662.476 2.142 2.593-.544 1.957-.646-.153-1.411 1.648-2.817.036-.17-.196-.567-.768-.73-.881-.392-.271-.892.359-1.373.28-.885-.146-.94-1.698-1.259-2.349-.439-.307-.807.058-1.331-.312-1.188-.838.268-3.715-3.576-3.286l.659-.342z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M175.697 144.822c.361.228 5.17.076 5.816.076.698 0 .47.552.708.76.29.131 3.593.022 2.84 1.27-.086.143-.199.273-.272.423l.017.139c.337.279 1.508.301 2.018.958.304.392-.501 1.022-.156 1.271.662.476 2.142 2.593-.544 1.957-.646-.153-1.411 1.648-2.817.036-.17-.196-.567-.768-.73-.881-.392-.271-.892.359-1.373.28-.885-.146-.94-1.698-1.259-2.349-.439-.307-.807.058-1.331-.312-1.188-.838.268-3.715-3.576-3.286l.659-.342z"/>
    <path fill="#000" fill-rule="nonzero" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".169" d="M175.346 144.902s3.62.733 5.7 1.781c1.909.962 4.47 3.174 4.47 3.174s-2.496-2.35-4.412-3.348c-1.982-1.032-5.468-1.723-5.468-1.723l-.29.116z"/>
    <path fill="#000" fill-rule="nonzero" d="M184.668 141.079c.939.87-.481 3.767-1.547 3.585-1.699-.29-2.632-2.208-2.312-3.026.342-.875 2.844-1.498 3.859-.559z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M184.668 141.079c.939.87-.481 3.767-1.547 3.585-1.699-.29-2.632-2.208-2.312-3.026.342-.875 2.844-1.498 3.859-.559z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M182.975 144.37c-.48-.344-.296-1.564.129-2.3.389-.675.943-1.098 1.366-.966 1.205.376 2.375 1.961 1.924 2.926-.443.952-2.941.682-3.419.34z"/>
    <path fill="#A48253" fill-rule="nonzero" d="M184.589 141.165c.863.798-.507 3.43-1.448 3.269-1.619-.276-2.202-1.943-2.006-2.629.201-.71 2.496-1.527 3.454-.64z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M184.589 141.165c.863.798-.507 3.43-1.448 3.269-1.619-.276-2.202-1.943-2.006-2.629.201-.71 2.496-1.527 3.454-.64h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M190.221 143.156c-.154.204-.478.583-.816.58-1.441-.014-1.341-1.244-2.098-2.062-.254-.062-.441.237-.938.124-.65-.149-.69-1.806-1.664-2.144-.492-.171-1.863-.178-2.625.537l1.362-1.06c1.317.517 2.503-2.286 4.011-1.914.428.106.442.385.938.342 1.101-.097 2.221-.82 3.545-.382.793.262-.237 1.324.765 1.202.645-.079 1.605-.076 1.797.726.105.44-.122.639-.073.958.13.101 2.549.943 1.269 1.591.203.569 1.175.949.66 1.646-.483.655-1.511-.072-1.796.199-.117.113-.113.426-.216.555-.427.53-1.55-.547-2.029-.648-.474 1.616-1.336.855-2.092-.25z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M190.221 143.156c-.154.204-.478.583-.816.58-1.441-.014-1.341-1.244-2.098-2.062-.254-.062-.441.237-.938.124-.65-.149-.69-1.806-1.664-2.144-.492-.171-1.863-.178-2.625.537l1.362-1.06c1.317.517 2.503-2.286 4.011-1.914.428.106.442.385.938.342 1.101-.097 2.221-.82 3.545-.382.793.262-.237 1.324.765 1.202.645-.079 1.605-.076 1.797.726.105.44-.122.639-.073.958.13.101 2.549.943 1.269 1.591.203.569 1.175.949.66 1.646-.483.655-1.511-.072-1.796.199-.117.113-.113.426-.216.555-.427.53-1.55-.547-2.029-.648-.474 1.616-1.336.855-2.092-.25h0z"/>
    <path fill="#658D5C" fill-rule="nonzero" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M184.796 139.659c1 .282 1.027 1.824 1.603 1.968.576.144.561-.16.922-.076.333.076.67 2.021 2.281 1.82.41-.051.616-.488.616-.488s.589.966 1.209 1.053c.892.125.636-.677.863-.704.362-.042 1.029.727 1.694.727.375 0 .322-.64.718-.64.397 0 1.179.377 1.501-.087.421-.606-.56-1.316-.631-1.532-.072-.216.279-.222.242-.431-.136-.77-1.387-.893-1.495-1.165-.108-.271.174-.551.069-.973-.179-.718-1.198-.743-1.658-.641-1.068.238-.1-.901-.86-1.221-1.31-.552-3.215.493-3.701.289-.327-.138-.452-.179-.731-.227-1.18-.205-2.267 1.516-3.012 1.875l.37.453h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M184.425 139.123s3.618-.223 5.741.516c1.837.639 4.261 2.592 4.261 2.592s-2.612-1.922-4.555-2.486c-2.091-.607-5.575-.274-5.575-.274l.128-.348z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".169" d="M184.425 139.123s3.618-.223 5.741.516c1.837.639 4.261 2.592 4.261 2.592s-2.612-1.922-4.555-2.486c-2.091-.607-5.575-.274-5.575-.274l.128-.348h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M181.23 139.376s.692-4.605.632-7.565c-.036-1.779-.375-4.546-.375-4.546s.476 2.868.55 4.721c.11 2.752-.517 7.041-.517 7.041l-.29.349z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".169" d="M181.23 139.376s.692-4.605.632-7.565c-.036-1.779-.375-4.546-.375-4.546s.476 2.868.55 4.721c.11 2.752-.517 7.041-.517 7.041l-.29.349h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M177.733 139.207c.066.197.736.391.675 1.137-.073.914-1.451.633-2.091 1.063-.242.163.287.948.009 1.321-.498.664-2.188.268-2.311.433-.227.304.551 1.14-.269 1.601-1.139.641-3.544-.373-4.841.777-.076.068-.581.635-.6.641-.741.195-2.708.601-2.478.505 1.154-.487 3.009-.962 3.372-1.756.31-.676.076-1.952.203-2.682.188-1.091.99-.652 1.254-.999.214-.28-.183-1.801.679-2.519.606-.503 1.256-.157 1.81-.556.578-.416-.296-1.805.847-2.201.687-.237 1.401.53 1.557.496.427-.092.928-1.379 1.795-1.206.863.172.478 1.31.441 1.837 1.446.179.662 1.255-.052 2.108z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M177.733 139.207c.066.197.736.391.675 1.137-.073.914-1.451.633-2.091 1.063-.242.163.287.948.009 1.321-.498.664-2.188.268-2.311.433-.227.304.551 1.14-.269 1.601-1.139.641-3.544-.373-4.841.777-.076.068-.581.635-.6.641-.741.195-2.708.601-2.478.505 1.154-.487 3.009-.962 3.372-1.756.31-.676.076-1.952.203-2.682.188-1.091.99-.652 1.254-.999.214-.28-.183-1.801.679-2.519.606-.503 1.256-.157 1.81-.556.578-.416-.296-1.805.847-2.201.687-.237 1.401.53 1.557.496.427-.092.928-1.379 1.795-1.206.863.172.478 1.31.441 1.837 1.446.179.662 1.255-.052 2.108h0z"/>
    <path fill="#658D5C" fill-rule="nonzero" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M166.032 146.574s3.078-1.286 3.329-1.932c.387-.99-.035-2.489.504-2.847.351-.234.703-.117.934-.45.231-.333-.069-1.948.763-2.6.521-.408.99-.042 1.625-.488.684-.482-.18-1.672.865-2.064.615-.231 1.234.522 1.54.442.589-.153.901-1.326 1.765-1.201.695.101.355.808.311 1.478-.026.385.578.206.757.745.132.392-.405 1.235-.764 1.549-.205.179.624.493.574 1.051-.072.801-1.618.857-1.977.986-.463.165-.045.857-.36 1.261-.503.645-1.792.244-2.022.552-.21.281.257 1.005-.335 1.449-.615.461-3.005.169-3.393.313a29.062 29.062 0 00-2.091.866l-2.025.89h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M169.329 144.766s3.365-2.42 5.085-4.584c.949-1.194 2.203-3.007 2.203-3.007s-1.214 1.88-2.145 3.123c-1.698 2.266-5.087 4.856-5.087 4.856l-.056-.388z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".169" d="M169.329 144.766s3.365-2.42 5.085-4.584c.949-1.194 2.203-3.007 2.203-3.007s-1.214 1.88-2.145 3.123c-1.698 2.266-5.087 4.856-5.087 4.856l-.056-.388h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M194.474 123.236c1.293-.699 3.642-1.909 5.559-3.659 1.29-1.177 2.972-3.35 2.972-3.35s-1.67 2.285-2.972 3.525c-1.957 1.864-4.44 3.192-5.734 3.891l.175-.407z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".169" d="M194.474 123.236c1.293-.699 3.642-1.909 5.559-3.659 1.29-1.177 2.972-3.35 2.972-3.35s-1.67 2.285-2.972 3.525c-1.957 1.864-4.44 3.192-5.734 3.891l.175-.407z"/>
    <path fill="#466343" fill-rule="nonzero" d="M191.727 124.974l-.297.987c0-1.364-.054-2.013-1.668-1.927-.25.012-.508.04-.756.001-.82-.128-.188-.621-.449-.997-.319-.458-2.479-.047-2.656-1.129-.068-.419.399-.917.057-1.246-.519-.497-1.81-.927-1.672-1.837.057-.374.559-.646.487-.995-.097-.465-1.025-1.273-.798-1.784.181-.407.512-.276.543-.66.036-.429-.715-1.5-.046-1.826.883-.429 1.481.409 2.238.47.439.036.737-.488 1.196-.385.593.131.597 1.087.979 1.282.14.071.56-.285.758-.308 1.063-.122.822 1.94 1.183 2.422.132.174.412.001.602.06.665.207-.047 2.461.227 2.763.093.103.431.045.579.086.555.156.167 1.589.076 1.941-.207.792-.921 2.344-.583 3.082z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M191.727 124.974l-.297.987c0-1.364-.054-2.013-1.668-1.927-.25.012-.508.04-.756.001-.82-.128-.188-.621-.449-.997-.319-.458-2.479-.047-2.656-1.129-.068-.419.399-.917.057-1.246-.519-.497-1.81-.927-1.672-1.837.057-.374.559-.646.487-.995-.097-.465-1.025-1.273-.798-1.784.181-.407.512-.276.543-.66.036-.429-.715-1.5-.046-1.826.883-.429 1.481.409 2.238.47.439.036.737-.488 1.196-.385.593.131.597 1.087.979 1.282.14.071.56-.285.758-.308 1.063-.122.822 1.94 1.183 2.422.132.174.412.001.602.06.665.207-.047 2.461.227 2.763.093.103.431.045.579.086.555.156.167 1.589.076 1.941-.207.792-.921 2.344-.583 3.082h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M191.686 124.815s-1.03-4.101-2.492-6.305c-1.211-1.825-3.864-4.075-3.864-4.075s2.538 2.267 3.689 4.075c1.412 2.216 2.375 6.305 2.375 6.305h.292z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".169" d="M191.686 124.815s-1.03-4.101-2.492-6.305c-1.211-1.825-3.864-4.075-3.864-4.075s2.538 2.267 3.689 4.075c1.412 2.216 2.375 6.305 2.375 6.305h.292z"/>
    <path fill="#000" fill-rule="nonzero" d="M193.923 103.385s-2.435-3.955-4.528-6.012c-1.708-1.68-4.869-3.747-4.869-3.747s3.012 2.126 4.636 3.812c1.985 2.06 4.296 5.947 4.296 5.947h.465z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".169" d="M193.923 103.385s-2.435-3.955-4.528-6.012c-1.708-1.68-4.869-3.747-4.869-3.747s3.012 2.126 4.636 3.812c1.985 2.06 4.296 5.947 4.296 5.947h.465z"/>
    <path fill="#A48253" fill-rule="nonzero" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M192.862 93.921c.406 1.016.987 2.586 1.374 4.052 1.815 6.876 1.982 14.822 1.205 19.412-2.393 14.14-15.249 27.294-25.538 29.349-8.736 1.744-13.9 5.53-17.567 9.576-1.739 1.917-3.127 3.831-4.202 5.654-1.066 1.807-1.765 3.357-2.711 4.914-.011.005-.028.013-.053.021a1.647 1.647 0 01-.247.061 5.248 5.248 0 01-.721.079 2.442 2.442 0 01-.617-.033l-.033-.009c.133-.206.266-.407.396-.62.902-1.487 1.686-2.937 2.718-4.659 1.029-1.716 2.245-3.527 3.9-5.398 3.816-4.313 9.239-8.486 18.508-10.349 10.79-2.169 23.11-15.549 25.523-29.806.786-4.643.768-12.66-2.182-21.347l.247-.897h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M192.441 93.092c-.895-2.281-1.525-3.534-2.82-5.613-1.674-2.686-5.073-6.33-5.073-6.33s3.216 3.66 4.799 6.319c1.674 2.814 2.441 4.546 3.402 7.676l-.308-2.052z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".169" d="M192.441 93.092c-.895-2.281-1.525-3.534-2.82-5.613-1.674-2.686-5.073-6.33-5.073-6.33s3.216 3.66 4.799 6.319c1.674 2.814 2.441 4.546 3.402 7.676l-.308-2.052z"/>
    <path fill="#A48253" fill-rule="nonzero" d="M192.963 95.281s-.137-.341-.208-.565c-.148-.465-.267-1.223-.267-1.223s.22.531.367.881c.129.308.336.92.336.92l-.228-.013z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".466" d="M124.177 148.115a1.83 1.83 0 113.66 0 1.83 1.83 0 01-3.66 0z"/>
    <path fill="#D99F31" fill-rule="nonzero" d="M126.006 146.401a1.715 1.715 0 110 3.43 1.715 1.715 0 010-3.43z"/>
    <path fill="#F1BF31" fill-rule="nonzero" d="M127.308 149.232c-.548.208-1.272-.148-1.679-.853-.439-.76-.343-1.637.215-1.96l.024-.013a1.715 1.715 0 011.853 1.71c0 .426-.155.816-.413 1.116z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M126.006 146.401a1.715 1.715 0 110 3.43 1.715 1.715 0 010-3.43z"/>
    <path fill="#000" fill-rule="nonzero" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M193.392 118.876c-.667-.554-2.494-2.239-2.494-3.1 0-1.026.958-.849 1.188-1-.041-.416-1.447-1.442-.883-2.367.371-.61 1.216-.439 1.529-.688.239-.654-.689-2.351.055-3.272.441-.545.782-.138.989-.188.041-.287.033-.579.065-.866.056-.508.24-1.011.83-1.042.826-.043 1.388 1.017 1.781 1.6.088-.051.518-.111.609-.156 1.016-.497.648 2.656.677 3.142.237-.008.717-.14.927-.017 1.088.642-1.074 4.019-1.305 4.814-.096.33 1.142.065.191 1.264-.554.7-1.261 1.32-1.873 1.969l-2.286-.093h0z"/>
    <path fill="#658D5C" fill-rule="nonzero" d="M193.737 121.49c.413-1.089.266-2.151-.555-2.746-.88-.637-2.053-2.15-2.052-3.026.003-1.035 1.032-.405 1.079-.894.036-.365-1.352-1.461-.857-2.266.322-.523 1.181-.463 1.399-.725.361-.437-.535-2.374.174-3.276.39-.496.692.031.952-.152.252-.178-.152-1.659.803-1.702.92-.041 1.596 1.501 1.743 1.453.297-.096.651-.406.986-.088.326.309.224 2.942.224 2.942s.625-.061.825.062c.831.511-.662 3.5-1.083 4.319-.359.699.692.787.153 1.505-.887 1.183-2.659 2.602-2.939 4.342l-.852.252z"/>
    <path fill="#A48253" fill-rule="nonzero" d="M193.911 120.871c.109-.215.221-.371.312-.354.216.04.119.59.072.808l-.558.165c.079-.209.137-.416.174-.619z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M193.737 121.49c.413-1.089.266-2.151-.555-2.746-.88-.637-2.053-2.15-2.052-3.026.003-1.035 1.032-.405 1.079-.894.036-.365-1.352-1.461-.857-2.266.322-.523 1.181-.463 1.399-.725.361-.437-.535-2.374.174-3.276.39-.496.692.031.952-.152.252-.178-.152-1.659.803-1.702.92-.041 1.596 1.501 1.743 1.453.297-.096.651-.406.986-.088.326.309.224 2.942.224 2.942s.625-.061.825.062c.831.511-.662 3.5-1.083 4.319-.359.699.692.787.153 1.505-.887 1.183-2.659 2.602-2.939 4.342"/>
    <path fill="#000" fill-rule="nonzero" d="M194.761 120.721s.666-4.283.85-6.542c.151-1.853-.261-4.903-.261-4.903s.238 2.934.087 4.787c-.184 2.259-1.177 7.634-1.177 7.634l.501-.976z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".169" d="M194.761 120.721s.666-4.283.85-6.542c.151-1.853-.261-4.903-.261-4.903s.238 2.934.087 4.787c-.184 2.259-1.177 7.634-1.177 7.634l.501-.976h0z"/>
    <path fill="#000" fill-rule="nonzero" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M185.023 136.637c.373-.958-.725-3.766.103-4.258.474-.282.773.248 1.234-.206.589-.582-.333-2.776.745-3.47.626-.403 1.122.178 1.725-.369.4-.364.418-1.832 1.435-2.086.676-.169.986.291 1.585.374.508.069 2.231-2.332 3.302-1.758 1.122.602.129 1.876-.097 2.729-.118.446.837.383.747 1.13-.081.683-.863 1.368-.863 1.655 0 .305.79.513.821 1.085.063 1.111-1.728 1.267-2.329 1.74-.315.248.406 1.046-.039 1.781-.472.781-2.481.233-2.815.4-.272.137-.006 1.461-.345 1.93-.55.763-3.174.047-4.146.206-1.301.213-1.87.48-2.688 1.451l1.625-2.334h0z"/>
    <path fill="#658D5C" fill-rule="nonzero" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M183.372 138.171s1.025-.531 1.395-1.123c.363-.585.41-.795.396-1.719-.009-.561-.498-2.493.053-2.801.452-.252.703.242 1.267-.29.687-.649-.321-2.896.658-3.503.59-.366 1.123.281 1.806-.329.465-.416.386-1.817 1.303-2.047.574-.144 1.032.304 1.575.377.664.09 2.179-2.119 3.128-1.602.416.227.539.545.463 1.001-.082.492-.361.927-.468 1.407-.137.619.7.565.624 1.218-.071.62-.807 1.215-.807 1.618 0 .441.664.727.706 1.269.081 1.028-2.068 1.048-2.242 1.612-.172.558.246.977-.16 1.636-.439.71-1.951.082-2.542.323-.493.2-.195 1.314-.494 1.761-.825 1.231-4.819-.498-6.459 1.757l-.202-.565h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M183.491 138.314s4.246-3.614 6.576-6.291c1.794-2.06 4.217-5.594 4.217-5.594s-2.262 3.577-3.985 5.652c-2.104 2.535-5.997 5.949-5.997 5.949l-.811.284z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".169" d="M183.491 138.314s4.246-3.614 6.576-6.291c1.794-2.06 4.217-5.594 4.217-5.594s-2.262 3.577-3.985 5.652c-2.104 2.535-5.997 5.949-5.997 5.949l-.811.284h0z"/>
    <path fill="#A48253" fill-rule="nonzero" d="M183.111 138.409l.696-.202s-.255.3-.358.392c-.087.078-.316.292-.316.292l-.022-.482z"/>
    <path fill="#D99F31" fill-rule="nonzero" d="M183.076 144.286c-.344-.247-.255-1.377.179-2.13.394-.682 1.005-1.013 1.258-.895.93.437 2.095 1.898 1.724 2.695-.369.791-2.705.656-3.161.33z"/>
    <path fill="#F1BF31" fill-rule="nonzero" d="M184.484 141.831a.822.822 0 01.525-.258c.793.613 1.535 1.726 1.228 2.383a.513.513 0 01-.052.087c-.398.067-.897-.094-1.305-.461-.602-.541-.779-1.325-.396-1.751z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".409" d="M183.076 144.286c-.344-.247-.255-1.377.179-2.13.394-.682 1.005-1.013 1.258-.895.93.437 2.095 1.898 1.724 2.695-.369.791-2.705.656-3.161.33h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M194.241 95.47c-.883-.305-1.486-1.937-1.119-3.385.365-1.434 4.024-.85 3.63.858-.311 1.347-1.761 2.785-2.511 2.527z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M194.241 95.47c-.883-.305-1.486-1.937-1.119-3.385.365-1.434 4.024-.85 3.63.858-.311 1.347-1.761 2.785-2.511 2.527h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M196.653 92.424c-.13.86-1.263.956-1.787.849-.558-.112-1.609-.704-1.486-1.416.261-1.511 1.48-2.675 2.199-2.562.785.124 1.283 1.744 1.074 3.129z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M196.653 92.424c-.13.86-1.263.956-1.787.849-.558-.112-1.609-.704-1.486-1.416.261-1.511 1.48-2.675 2.199-2.562.785.124 1.283 1.744 1.074 3.129h0z"/>
    <path fill="#A48253" fill-rule="nonzero" d="M194.318 95.192c-.744-.115-1.325-1.65-.969-3.05.333-1.314 3.462-.483 3.178.748-.31 1.346-1.482 2.414-2.209 2.302z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M194.318 95.192c-.744-.115-1.325-1.65-.969-3.05.333-1.314 3.462-.483 3.178.748-.31 1.346-1.482 2.414-2.209 2.302h0z"/>
    <path fill="#D99F31" fill-rule="nonzero" d="M195.55 89.525c.61.096 1.258 1.472 1.046 2.872-.102.676-1.157.639-1.696.53-.581-.118-1.558-.506-1.465-1.042.258-1.493 1.553-2.45 2.115-2.36z"/>
    <path fill="#F1BF31" fill-rule="nonzero" d="M195.616 90.764c-.096-.56-.339-1.03-.25-1.236a.694.694 0 01.108-.009.48.48 0 01.077.006c.217.034.438.229.624.53.057.172.107.381.15.628.167.945.137 1.391-.243 1.458-.381.066-.372-.828-.466-1.377z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".409" d="M195.55 89.525c.61.096 1.258 1.472 1.046 2.872-.102.676-1.157.639-1.696.53-.581-.118-1.558-.506-1.465-1.042.258-1.493 1.553-2.45 2.115-2.36h0z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M143.851 167.514c-.289-.425.372-1.731 1.756-1.574.607.069.313.947-.44 1.473-.528.368-1.027.527-1.316.101h0z"/>
    <path fill="#A48253" fill-rule="nonzero" d="M143.851 167.514c-.289-.425.132-1.339 1.524-1.398.5-.022.545.772-.208 1.298-.528.367-1.027.526-1.316.1z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M143.851 167.514c-.289-.425.132-1.339 1.524-1.398.5-.022.545.772-.208 1.298-.528.367-1.027.526-1.316.1h0zM148.594 161.625s-.037-.517-.634-.724c-.514-.179-.839.23-.839.23m.891 1.192s-.019-.522-.633-.667c-.561-.131-.782.347-.782.347"/>
    <path fill="#A48253" fill-rule="nonzero" d="M109.098 124.706s.195.596.347.966c.113.275.313.694.313.694s-.419.09-.419.043-.327-1.696-.327-1.696l.086-.007z"/>
    <path fill="#658D5C" fill-rule="nonzero" d="M108.878 125.053c-.619-.948-1.936-2.003-2.68-2.776-1.28-1.329-2.074-2.852-2.366-4.684-.689-4.33.57-7.242 3.729-10.169.2 3.037 1.937 5.556 2.304 8.589.527 4.351-2.242 5.171-.318 9.429l-.669-.389z"/>
    <path fill="#A48253" fill-rule="nonzero" d="M108.458 124.189s.037.188.089.42c.123.149.235.298.33.443l.548.319-.293-1-.674-.182z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M108.878 125.053c-.619-.948-1.936-2.003-2.68-2.776-1.28-1.329-2.074-2.852-2.366-4.684-.689-4.33.57-7.242 3.729-10.169.2 3.037 1.937 5.556 2.304 8.589.527 4.351-2.242 5.171-.318 9.429"/>
    <path fill="#000" fill-rule="nonzero" d="M108.874 124.153c-1.258-1.893-1.504-2.818-2.036-4.768-1-3.663-1.007-8.112-.065-9.737-.942 1.625-1.135 6.051-.225 9.74.512 2.079.92 3.164 2.087 5.078l.239-.313z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".169" d="M108.874 124.153c-1.258-1.893-1.504-2.818-2.036-4.768-1-3.663-1.007-8.112-.065-9.737-.942 1.625-1.135 6.051-.225 9.74.512 2.079.92 3.164 2.087 5.078l.239-.313h0z"/>
    <path stroke="#000" stroke-linejoin="round" stroke-width=".35" d="M108.721 124.832c-.307-.359.086-.948.439-.428"/>
    <path fill="#FFF" fill-rule="nonzero" d="M171.273 64.212a1.517 1.517 0 113.034 0 1.517 1.517 0 01-3.034 0z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M171.273 64.212a1.517 1.517 0 113.034 0 1.517 1.517 0 01-3.034 0z"/>
    <path fill="#FFF" fill-rule="nonzero" d="M171.728 58.884a1.905 1.905 0 113.81 0 1.905 1.905 0 01-3.81 0z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M171.728 58.884a1.905 1.905 0 113.81 0 1.905 1.905 0 01-3.81 0z"/>
    <path fill="#FFF" fill-rule="nonzero" d="M140.18 53.145a1.466 1.466 0 112.931 0 1.466 1.466 0 01-2.931 0z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M140.18 53.145a1.466 1.466 0 112.931 0 1.466 1.466 0 01-2.931 0z"/>
    <path fill="#FFF" fill-rule="nonzero" d="M125.521 58.884a1.905 1.905 0 113.81 0 1.905 1.905 0 01-3.81 0z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M125.521 58.884a1.905 1.905 0 113.81 0 1.905 1.905 0 01-3.81 0z"/>
    <path fill="#FFF" fill-rule="nonzero" d="M134.543 53.762a1.581 1.581 0 113.162 0 1.581 1.581 0 01-3.162 0z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M134.543 53.762a1.581 1.581 0 113.162 0 1.581 1.581 0 01-3.162 0z"/>
    <path fill="#FFF" fill-rule="nonzero" d="M126.754 64.212a1.517 1.517 0 113.034 0 1.517 1.517 0 01-3.034 0z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M126.754 64.212a1.517 1.517 0 113.034 0 1.517 1.517 0 01-3.034 0z"/>
    <path fill="#FFF" fill-rule="nonzero" d="M128.745 55.169a1.832 1.832 0 113.665 0 1.832 1.832 0 01-3.665 0z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M128.745 55.169a1.832 1.832 0 113.665 0 1.832 1.832 0 01-3.665 0z"/>
    <path fill="#FFF" fill-rule="nonzero" d="M157.948 53.145a1.466 1.466 0 112.931 0 1.466 1.466 0 01-2.931 0z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M157.948 53.145a1.466 1.466 0 112.931 0 1.466 1.466 0 01-2.931 0z"/>
    <path fill="#FFF" fill-rule="nonzero" d="M163.353 53.762a1.581 1.581 0 113.162 0 1.581 1.581 0 01-3.162 0z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M163.353 53.762a1.581 1.581 0 113.162 0 1.581 1.581 0 01-3.162 0z"/>
    <path fill="#FFF" fill-rule="nonzero" d="M168.651 55.169a1.832 1.832 0 113.665 0 1.832 1.832 0 01-3.665 0z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M168.651 55.169a1.832 1.832 0 113.665 0 1.832 1.832 0 01-3.665 0z"/>
    <path fill="#000" fill-rule="nonzero" d="M153.321 49.506c5.057 5.827 13.933 2.981 17.982 5.249 7.044 3.946-.618 13.307-2.06 15.097l-7.677-2.004c2.819-2.255 8.471-6.085 6.769-9.197-1.745-3.191-11.015.047-15.069-6.765-.852-1.432-.198-2.672.055-2.38z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M153.321 49.506c5.057 5.827 13.933 2.981 17.982 5.249 7.044 3.946-.618 13.307-2.06 15.097l-7.677-2.004c2.819-2.255 8.471-6.085 6.769-9.197-1.745-3.191-11.015.047-15.069-6.765-.852-1.432-.198-2.672.055-2.38h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M147.683 49.506c-5.056 5.827-13.933 2.981-17.982 5.249-7.044 3.946.618 13.307 2.061 15.097l7.676-2.004c-2.818-2.255-8.47-6.085-6.768-9.197 1.745-3.191 11.014.047 15.068-6.765.852-1.432.199-2.672-.055-2.38z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M147.683 49.506c-5.056 5.827-13.933 2.981-17.982 5.249-7.044 3.946.618 13.307 2.061 15.097l7.676-2.004c-2.818-2.255-8.47-6.085-6.768-9.197 1.745-3.191 11.014.047 15.068-6.765.852-1.432.199-2.672-.055-2.38h0z"/>
    <path stroke="#000" stroke-width=".339" d="M135.655 76.605s-.396-.4-.517-.726c-.096-.257-.104-.695-.104-.695l-1.421-2.276 3.19-3.034 6.066-1.74 7.648-.547h.012l7.649.547 6.065 1.74 3.19 3.034-1.42 2.276s-.009.438-.104.695c-.121.326-.517.726-.517.726l-7.114-2.405-7.749-.347-7.76.347-7.114 2.405h0z"/>
    <path fill="#F1BF31" fill-rule="nonzero" d="M135.828 76.349s-.284-.355-.392-.621c-.157-.384-.136-1.055-.136-1.055l-1.685-1.764 3.19-3.034 6.065-1.74 7.661-.546 7.66.546 6.066 1.74 3.189 3.034-1.684 1.764s.02.671-.137 1.055c-.107.266-.392.621-.392.621l-6.943-2.148-7.759-.348-7.76.348-6.943 2.148z"/>
    <path stroke="#000" stroke-width=".35" d="M135.828 76.349s-.284-.355-.392-.621c-.157-.384-.136-1.055-.136-1.055l-1.685-1.764 3.19-3.034 6.065-1.74 7.661-.546 7.66.546 6.066 1.74 3.189 3.034-1.684 1.764s.02.671-.137 1.055c-.107.266-.392.621-.392.621l-6.943-2.148-7.759-.348-7.76.348-6.943 2.148h0z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".409" d="M171.118 64.211a1.672 1.672 0 113.344 0 1.672 1.672 0 01-3.344 0zm.415-5.327a2.1 2.1 0 114.202 0 2.1 2.1 0 01-4.202 0zm-31.503-5.739a1.616 1.616 0 113.232 0 1.616 1.616 0 01-3.232 0zm-14.704 5.739a2.1 2.1 0 114.202 0 2.1 2.1 0 01-4.202 0zm9.055-5.122a1.744 1.744 0 113.488 0 1.744 1.744 0 01-3.488 0zm-7.783 10.449a1.672 1.672 0 113.344 0 1.672 1.672 0 01-3.344 0zm1.959-9.042a2.02 2.02 0 114.041 0 2.02 2.02 0 01-4.041 0z"/>
    <path fill="#F1BF31" fill-rule="nonzero" d="M147.683 49.68c-5.056 5.828-13.646 3.035-17.695 5.304-7.044 3.947.912 13.078 2.355 14.868l6.631-2.004c-2.819-2.255-8.175-6.201-6.473-9.313 1.744-3.191 11.183-.012 15.237-6.823.852-1.432.199-2.323-.055-2.032z"/>
    <path fill="#000" fill-rule="nonzero" d="M148.142 50.129c-5.476 7.394-13.696 3.661-17.229 6.546-3.991 3.259 1.824 9.673 4.629 12.211l.438-.132.001-.029c-2.276-2.158-9.022-8.565-4.735-12.024 3.082-2.489 11.749.758 16.915-6.396a1.791 1.791 0 00-.019-.176z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".237" d="M148.142 50.129c-5.476 7.394-13.696 3.661-17.229 6.546-3.991 3.259 1.824 9.673 4.629 12.211m.437-.133l.001-.029c-2.276-2.158-9.022-8.565-4.735-12.024 3.082-2.489 11.749.758 16.915-6.396h0"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M147.683 49.68c-5.056 5.828-13.646 3.035-17.695 5.304-7.044 3.947.912 13.078 2.355 14.868l6.631-2.004c-2.819-2.255-8.175-6.201-6.473-9.313 1.744-3.191 11.183-.012 15.237-6.823.852-1.432.199-2.323-.055-2.032z"/>
    <path fill="#D99F31" fill-rule="nonzero" d="M136.107 68.851l-.133-.127.796-.232z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".227" d="M136.107 68.851l-.133-.127.796-.232z"/>
    <path fill="#F1BF31" fill-rule="nonzero" d="M138.849 67.75l.125.099-2.204.644.498-.269c.554-.135 1.08-.294 1.581-.474z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".339" d="M138.849 67.75l.125.099-2.204.644.498-.269c.554-.135 1.08-.294 1.581-.474z"/>
    <path fill="#D99F31" fill-rule="nonzero" d="M134.686 64.24a5.562 5.562 0 00-.471 1.25c-2.341-2.788-4.094-5.678-2.249-8.064 1.679-2.173 5.795-1.19 10.079-2.695 1.962-.582 3.888-1.618 5.529-3.56-.158.425-.321.94-.502 1.455-4.348 5.536-13.032 2.883-14.688 5.91-.976 1.785.411 3.846 2.302 5.704z"/>
    <path fill="#000" fill-rule="nonzero" d="M147.27 51.516c.208-.025.424-.084.638-.162a6.1 6.1 0 01-.164.291 2.52 2.52 0 01-.699.111c.075-.079.151-.159.225-.24zM145.872 52.809c.247.124.536.212.818.272-.089.099-.18.194-.273.287a3.272 3.272 0 01-.845-.336c.1-.072.2-.146.3-.223zM144.15 53.898c.311.225.72.374 1.087.464a9.08 9.08 0 01-.377.249c-.374-.118-.757-.295-1.049-.547.113-.053.226-.108.339-.166zM141.814 54.796c.336.325.761.566 1.215.72-.161.062-.324.119-.487.174a3.367 3.367 0 01-1.107-.794c.127-.031.253-.064.379-.1zM139.493 55.269c.252.387.596.714 1.007.957-.16.032-.32.062-.479.091a3.335 3.335 0 01-.899-.999l.371-.049zM136.897 55.527c.138.392.417.738.784 1.015a.166.166 0 01.068.107l-.415.052c-.375-.318-.655-.709-.789-1.149l.352-.025zM134.235 55.759c.092.528.371.974.805 1.294-.144.03-.283.062-.419.097a2.346 2.346 0 01-.724-1.343c.111-.017.224-.033.338-.048zM132.741 58.065a3.352 3.352 0 01-1.296-1.414c.103-.074.211-.142.325-.205.206.438.594 1.028 1.271 1.366a2.638 2.638 0 00-.3.253zM132.064 59.773c-.778-.074-1.532-.481-2.061-1.06.037-.155.087-.308.147-.458.304.38.964 1.039 1.945 1.124-.02.131-.03.262-.031.394zM131.487 61.578c.426-.067.598-.11.935-.3.058.129.123.258.194.387a2.708 2.708 0 01-.91.345c-.396.082-.807.057-1.171-.003a9.006 9.006 0 01-.216-.501c.356.068.815.127 1.168.072zM132.542 63.742c.489-.141.738-.412 1.01-.728.111.134.225.267.344.4-.187.248-.401.491-.722.66-.388.204-.813.255-1.211.255-.133-.179-.263-.36-.389-.542.323.032.685.037.968-.045z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".227" d="M148.16 50.421c.016.358-.085.666-.422 1.233-4.055 6.811-13.609 3.69-15.354 6.881-1.677 3.067 3.616 6.944 6.464 9.214-.5.18-1.027.339-1.581.473l-.498.269-.737.233c-2.276-2.158-9.016-8.507-4.73-11.966 3.084-2.488 11.692.816 16.858-6.337h0z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".409" d="M157.798 53.145a1.616 1.616 0 113.232 0 1.616 1.616 0 01-3.232 0zm5.393.617a1.744 1.744 0 113.488.002 1.744 1.744 0 01-3.488-.002zm5.272 1.407a2.021 2.021 0 114.042 0 2.021 2.021 0 01-4.042 0z"/>
    <path fill="#F1BF31" fill-rule="nonzero" d="M153.377 49.68c5.056 5.828 13.646 3.035 17.695 5.304 7.044 3.947-.912 13.078-2.355 14.868l-6.631-2.004c2.82-2.255 8.175-6.201 6.473-9.313-1.745-3.191-11.184-.012-15.237-6.823-.852-1.432-.199-2.323.055-2.032z"/>
    <path fill="#000" fill-rule="nonzero" d="M152.918 50.129c5.476 7.394 13.696 3.661 17.229 6.546 3.985 3.253-1.856 9.651-4.673 12.198l-.397-.12.002-.028c2.276-2.158 9.022-8.565 4.736-12.024-3.083-2.489-11.75.758-16.916-6.396a1.53 1.53 0 01.019-.176z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".237" d="M152.918 50.129c5.476 7.394 13.696 3.661 17.229 6.546 3.985 3.253-1.856 9.651-4.673 12.198m-.397-.121l.002-.028c2.276-2.158 9.022-8.565 4.736-12.024-3.083-2.489-11.75.758-16.916-6.396h0"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M153.377 49.68c5.056 5.828 13.646 3.035 17.695 5.304 7.044 3.947-.912 13.078-2.355 14.868l-6.631-2.004c2.82-2.255 8.175-6.201 6.473-9.313-1.745-3.191-11.184-.012-15.237-6.823-.852-1.432-.199-2.323.055-2.032h0z"/>
    <path fill="#D99F31" fill-rule="nonzero" d="M166.373 64.24c.134.255.335.693.471 1.25 2.34-2.788 4.094-5.678 2.249-8.064-1.679-2.174-5.795-1.19-10.08-2.695-1.962-.582-3.888-1.618-5.528-3.56.158.425.32.939.501 1.453 4.347 5.538 13.034 2.884 14.689 5.912.977 1.785-.41 3.846-2.302 5.704z"/>
    <path fill="#000" fill-rule="nonzero" d="M153.79 51.516a2.867 2.867 0 01-.637-.162 6.1 6.1 0 00.164.292c.236.069.472.11.698.11a12.762 12.762 0 01-.225-.24zM155.188 52.809a3.472 3.472 0 01-.817.272c.089.099.18.194.272.287a3.32 3.32 0 00.845-.335 10.616 10.616 0 01-.3-.224zM156.911 53.898c-.311.225-.72.373-1.087.464.124.087.25.169.377.249.373-.118.756-.294 1.049-.546a8.867 8.867 0 01-.339-.167zM159.246 54.796a3.247 3.247 0 01-1.215.72c.161.062.324.119.487.174a3.352 3.352 0 001.106-.794 10.544 10.544 0 01-.378-.1zM161.567 55.269a3.057 3.057 0 01-1.007.957c.16.032.32.063.48.091.366-.272.673-.611.898-.999-.124-.015-.247-.032-.371-.049zM164.163 55.527c-.138.392-.417.738-.785 1.015a.166.166 0 00-.068.107l.415.053c.374-.318.654-.709.788-1.149l-.35-.026zM166.825 55.759a2.016 2.016 0 01-.804 1.294c.144.03.283.062.418.097.384-.356.634-.816.724-1.343a16.174 16.174 0 00-.338-.048zM168.319 58.065a3.365 3.365 0 001.296-1.414 3.247 3.247 0 00-.325-.205c-.206.438-.594 1.028-1.271 1.366.109.078.209.163.3.253zM168.996 59.773c.778-.075 1.531-.482 2.06-1.06a3.117 3.117 0 00-.147-.457c-.304.38-.963 1.038-1.944 1.123.02.13.03.262.031.394zM169.573 61.578c-.426-.067-.597-.11-.935-.3a5.825 5.825 0 01-.194.387 2.7 2.7 0 00.91.345c.396.082.807.057 1.171-.003.078-.167.151-.334.216-.501-.356.068-.816.127-1.168.072zM168.518 63.975c-.563-.14-.843-.387-1.212-.722-.111.128-.225.256-.343.383.258.249.524.493.923.671.345.153.708.212 1.055.229.129-.169.256-.34.38-.513a2.77 2.77 0 01-.803-.048z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".227" d="M152.9 50.421c-.016.358.085.666.423 1.233 4.053 6.811 13.608 3.69 15.353 6.881 1.676 3.067-3.617 6.944-6.465 9.214.5.18 1.027.339 1.581.473l.497.269.738.233c2.276-2.158 9.016-8.507 4.73-11.966-3.083-2.488-11.691.816-16.857-6.337h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M150.53 62.512h-2.565s.304-2.446-.04-4.103c-.278-1.34-1.347-2.878-1.347-4.005 0-1.127 1.221-4.295 1.221-4.295h5.463s1.162 3.167 1.162 4.295c0 1.128-1.011 2.665-1.289 4.005-.343 1.657-.04 4.103-.04 4.103h-2.565z"/>
    <path stroke="#000" stroke-width=".35" d="M150.53 62.512h-2.565s.304-2.446-.04-4.103c-.278-1.34-1.347-2.878-1.347-4.005 0-1.127 1.221-4.295 1.221-4.295h5.463s1.162 3.167 1.162 4.295c0 1.128-1.011 2.665-1.289 4.005-.343 1.657-.04 4.103-.04 4.103h-2.565 0z"/>
    <path fill="#F1BF31" fill-rule="nonzero" d="M150.53 62.512h-2.565s.395-2.442.076-4.103c-.259-1.352-1.172-2.401-1.292-3.771-.171-1.934 1.516-4.529 1.516-4.529h4.532s1.629 2.594 1.458 4.529c-.12 1.371-.975 2.42-1.235 3.771-.318 1.661.077 4.103.077 4.103h-2.567z"/>
    <path fill="#D99F31" fill-rule="nonzero" d="M146.741 54.15c.161-.055.332-.093.494-.093.563 0 .786.368.786.368s1.475-.341 2.508-.341c1.034 0 2.509.341 2.509.341s.223-.368.786-.368c.143 0 .292.03.436.075.01.172.008.341-.006.506-.121 1.372-.977 2.42-1.235 3.771-.158.821-.141 1.832-.084 2.646l-2.406 1.144-2.406-1.144c.057-.813.074-1.825-.083-2.646-.259-1.352-1.172-2.399-1.293-3.771a3.599 3.599 0 01-.006-.488z"/>
    <path fill="#000" fill-rule="nonzero" d="M148.191 62.512c.29-1.435.532-2.99.466-4.449-.016-.371-.149-.945-.306-1.517a37.609 37.609 0 00-.448-1.49c-.333-1.052-.068-2.3.28-3.281a10.7 10.7 0 01.756-1.665h.505c-.155.254-1.741 2.935-1.049 4.917.254.728.75 2.249.761 3.034.018 1.289-.456 3.209-.862 4.452h-.103v-.001zM153.16 55.055c-.112.354-.292.92-.448 1.491-.156.571-.288 1.145-.306 1.517-.071 1.451.177 3.026.466 4.449h-.105c-.408-1.244-.879-3.161-.861-4.451.01-.786.507-2.307.762-3.035.367-1.051.291-1.761.033-2.465-.259-.707-.701-1.408-1.064-2.435-.002-.007-.002-.012.001-.016h.487c.371 1.011.789 1.718 1.037 2.434.251.726.33 1.46-.002 2.511z"/>
    <path stroke="#000" stroke-width=".35" d="M150.53 62.512h-2.565s.395-2.442.076-4.103c-.259-1.352-1.172-2.401-1.292-3.771-.171-1.934 1.516-4.529 1.516-4.529h4.532s1.629 2.594 1.458 4.529c-.12 1.371-.975 2.42-1.235 3.771-.318 1.661.077 4.103.077 4.103h-2.567 0z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".357" d="M153.549 35.944c.666 0 1.152.594 1.152 1.261 0 .667-.486 1.321-1.152 1.321-.87 0-2.132-1.029-2.09-.21l.021.414.462 3.804c1.133.255-3.958.255-2.825 0l.463-3.821.02-.397c.042-.818-1.252.21-2.089.21-.667 0-1.152-.654-1.152-1.321 0-.667.485-1.261 1.152-1.261.656 0 1.381.611 1.846.324.731-.451-.149-1.465-.149-2.149 0-.666.654-1.209 1.321-1.209.667 0 1.321.543 1.321 1.209 0 .684-.886 1.668-.148 2.149.441.287 1.167-.324 1.847-.324h0z"/>
    <path fill="#D99F31" fill-rule="nonzero" d="M153.349 36.104c.622 0 1.184.508 1.184 1.131 0 .622-.562 1.131-1.184 1.131-.622 0-2.151-1.089-2.115-.259l.02.443.261 3.669c1.059.238-3.031.238-1.973 0l.263-3.685.019-.427c.037-.829-1.539.259-2.113.259-.622 0-1.185-.508-1.185-1.131s.562-1.131 1.185-1.131c.613 0 1.453.522 1.887.255.683-.421-.256-1.414-.256-2.052 0-.622.565-1.184 1.187-1.184.623 0 1.187.562 1.187 1.184 0 .638-.944 1.603-.255 2.052.412.266 1.253-.256 1.888-.255z"/>
    <path fill="#F1BF31" fill-rule="nonzero" d="M150.974 36.289c0-.192.091-.372.25-.526-.039.235.008.443.239.594.411.268 1.251-.255 1.887-.255.404 0 .784.215 1 .537-.244.39-.884.668-1.634.668-.963.001-1.742-.455-1.742-1.018zM149.748 36.226a.407.407 0 01.082.237c0 .412-.712.746-1.592.746-.736 0-1.356-.235-1.537-.554a1.218 1.218 0 011.009-.552c.614 0 1.453.523 1.887.255a.551.551 0 00.151-.132zM149.559 34.316a.955.955 0 01-.173-.304c.142-.494.623-.889 1.145-.889.229 0 .449.076.638.203-.04.135-.344.163-.631.404-.413.344-.634.997-.979.586z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M153.349 36.104c.622 0 1.184.508 1.184 1.131 0 .622-.562 1.131-1.184 1.131-.622 0-2.151-1.089-2.115-.259l.02.443.261 3.669c1.059.238-3.031.238-1.973 0l.263-3.685.019-.427c.037-.829-1.539.259-2.113.259-.622 0-1.185-.508-1.185-1.131s.562-1.131 1.185-1.131c.613 0 1.453.522 1.887.255.683-.421-.256-1.414-.256-2.052 0-.622.565-1.184 1.187-1.184.623 0 1.187.562 1.187 1.184 0 .638-.944 1.603-.255 2.052.412.266 1.253-.256 1.888-.255h0zm-5.926 7.504a3.106 3.106 0 116.214 0 3.108 3.108 0 11-6.214 0z"/>
    <path fill="#F1F1F2" fill-rule="nonzero" d="M147.711 43.608a2.82 2.82 0 115.639.001 2.82 2.82 0 01-5.639-.001z"/>
    <path fill="#000" fill-rule="nonzero" d="M150.7 45.966c-.013.001-.028-.012-.03-.024-.001-.005 0-.018.012-.035l.023-.028.082-.092c.068-.076.16-.182.26-.305.199-.248.418-.564.509-.858.067-.215.29-.114.242.069-.085.32-.28.655-.47.919-.21.292-.456.347-.46.349l-.168.005zm-2.989-2.359c0 .053.001.105.004.156a.166.166 0 00.072-.028c.051-.043.11-.098.179-.153.069-.055.15-.114.245-.169.125-.084.038-.279-.114-.205-.161.078-.293.16-.38.219-.004.06-.006.12-.006.18zm.057.568c.022.108.051.214.085.317a.13.13 0 00.07-.042c.216-.229.578-.562.636-.616.134-.127-.04-.273-.154-.192a5.211 5.211 0 00-.457.363c-.075.069-.138.13-.18.17zm.224.661a2.7 2.7 0 00.139.253.155.155 0 00.054-.028c.455-.354.859-.782.924-.853.128-.139-.021-.329-.173-.19-.072.077-.663.601-.94.815l-.004.003zm3.967.953c-.146.111-.305-.039-.204-.191.007-.008.04-.031.089-.074.051-.044.119-.108.192-.188.143-.162.299-.389.35-.651.019-.093.085-.137.151-.123.064.014.11.08.096.169a1.616 1.616 0 01-.372.765c-.156.182-.298.29-.302.293zm-1.95.03l-.073.002a.82.82 0 01-.098-.027.183.183 0 01-.087-.059c-.023-.034-.018-.073.014-.109.415-.475.718-.881.991-1.337.041-.068.116-.086.171-.058.058.028.086.098.046.18a5.61 5.61 0 01-.607.972c-.205.259-.357.436-.357.436zm-1.185-.201a.172.172 0 01-.223-.009c-.056-.049-.082-.138-.011-.209a9.222 9.222 0 001.107-1.088c.104-.128.331-.029.201.163a4.82 4.82 0 01-.659.766l-.415.377z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M147.711 43.608a2.82 2.82 0 115.639.001 2.82 2.82 0 01-5.639-.001z"/>
    <path fill="#D99F31" fill-rule="nonzero" d="M152.801 50.11h-2.271l-2.262-.001c-.384.649-1.596 1.089-2.588.571-.704-.368-.849-1.204-.849-1.204s1.898.069 1.909-1.61c0-1.702.978-2.814 2.383-2.646.613.073 1.029.382 1.408.727.38-.345.797-.655 1.411-.727 1.405-.167 2.381.944 2.381 2.632 0 1.694 1.908 1.625 1.908 1.625s-.144.835-.849 1.203c-.997.519-2.216.074-2.581-.57z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M152.801 50.11h-2.271l-2.262-.001c-.384.649-1.596 1.089-2.588.571-.704-.368-.849-1.204-.849-1.204s1.898.069 1.909-1.61c0-1.702.978-2.814 2.383-2.646.613.073 1.029.382 1.408.727.38-.345.797-.655 1.411-.727 1.405-.167 2.381.944 2.381 2.632 0 1.694 1.908 1.625 1.908 1.625s-.144.835-.849 1.203c-.997.519-2.216.074-2.581-.57h0z"/>
    <path stroke="#FFF" stroke-width="2.263" d="M164.085 80.318h-.003"/>
    <path fill="#000" fill-rule="nonzero" d="M135.081 77.733c0-2.819 6.7-4.792 15.437-4.792 8.739-.001 15.439 1.973 15.439 4.792 0 2.255-6.7 4.059-15.439 4.059-8.737 0-15.437-1.804-15.437-4.059z"/>
    <path stroke="#000" stroke-width=".35" d="M135.081 77.733c0-2.819 6.7-4.792 15.437-4.792 8.739-.001 15.439 1.973 15.439 4.792 0 2.255-6.7 4.059-15.439 4.059-8.737 0-15.437-1.804-15.437-4.059z"/>
    <path fill="#F1BF31" fill-rule="nonzero" d="M135.597 77.733c0-2.819 6.184-4.617 14.921-4.617 8.739-.001 14.922 1.798 14.922 4.617 0 2.255-6.184 3.946-14.922 3.946-8.737 0-14.921-1.691-14.921-3.946z"/>
    <path stroke="#000" stroke-width=".35" d="M135.597 77.733c0-2.819 6.184-4.617 14.921-4.617 8.739-.001 14.922 1.798 14.922 4.617 0 2.255-6.184 3.946-14.922 3.946-8.737 0-14.921-1.691-14.921-3.946z"/>
    <path fill="#D99F31" fill-rule="nonzero" d="M136.724 78.155c0-1.946 5.855-3.523 13.794-3.523 7.941 0 13.795 1.577 13.795 3.523 0 1.946-5.854 3.523-13.795 3.523-7.938.001-13.794-1.577-13.794-3.523z"/>
    <path fill="#D99F31" fill-rule="nonzero" d="M164.195 78.629c-2.582 1.105-7.662 1.698-13.677 1.698s-11.094-.593-13.676-1.698a1.044 1.044 0 01-.118-.473c0-1.946 5.855-3.523 13.795-3.523 7.94 0 13.794 1.577 13.794 3.523 0 .16-.04.318-.118.473z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".409" d="M164.195 78.629c-2.582 1.105-7.662 1.698-13.677 1.698s-11.094-.593-13.676-1.698"/>
    <path fill="#D99F31" fill-rule="nonzero" d="M163.966 77.348c-2.646 1.008-7.606 1.512-13.447 1.512-5.842 0-10.801-.504-13.447-1.512 1.367-1.556 6.641-2.716 13.447-2.716 6.807 0 12.08 1.16 13.447 2.716z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".379" d="M163.966 77.348c-2.646 1.008-7.606 1.512-13.447 1.512-5.842 0-10.801-.504-13.447-1.512"/>
    <path fill="#D99F31" fill-rule="nonzero" d="M162.672 76.457c-2.811.815-7.16 1.276-12.153 1.276-4.994 0-9.342-.461-12.153-1.276 2.276-1.088 6.727-1.824 12.153-1.824 5.427-.001 9.878.736 12.153 1.824z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M162.672 76.457c-2.811.815-7.16 1.276-12.153 1.276-4.994 0-9.342-.461-12.153-1.276"/>
    <path fill="#D99F31" fill-rule="nonzero" d="M160.804 75.776c-2.712.535-6.292.83-10.285.83-3.993 0-7.574-.295-10.285-.83 2.475-.703 6.089-1.144 10.286-1.144 4.196 0 7.81.441 10.284 1.144z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M160.804 75.776c-2.712.535-6.292.83-10.285.83-3.993 0-7.574-.295-10.285-.83"/>
    <path fill="#D99F31" fill-rule="nonzero" d="M157.66 75.119c-2.122.234-4.544.359-7.141.359-2.596 0-5.018-.125-7.141-.359 2.052-.309 4.479-.487 7.141-.487 2.663 0 5.09.178 7.141.487z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M157.66 75.119c-2.122.234-4.544.359-7.141.359-2.596 0-5.018-.125-7.141-.359"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M136.724 78.155c0-1.946 5.855-3.523 13.794-3.523 7.941 0 13.795 1.577 13.795 3.523 0 1.946-5.854 3.523-13.795 3.523-7.938.001-13.794-1.577-13.794-3.523z"/>
    <path fill="#F1BF31" fill-rule="nonzero" d="M155.891 70.876a.562.562 0 10-1.123-.097.564.564 0 001.123.097zm1.306 1.246a.565.565 0 00-1.124-.098.564.564 0 101.124.098zm1.332-1.001a.563.563 0 00-.512-.61c-.31-.027-.527.203-.555.512-.027.311.146.584.456.611a.563.563 0 00.611-.513zm-1.135-1.246a.564.564 0 10-1.123-.097.564.564 0 001.123.097z"/>
    <path fill="#D99F31" fill-rule="nonzero" d="M155.857 71.024a.564.564 0 00-1.057 0 .564.564 0 00.479.364.562.562 0 00.578-.364zm.446-1a.562.562 0 001.056 0 .562.562 0 00-1.056 0zm1.186 1.256a.486.486 0 00.431.355.564.564 0 00.577-.365.565.565 0 00-.479-.364c-.263-.024-.458.137-.529.374zm-1.383.99a.565.565 0 001.057.001.565.565 0 00-1.057-.001z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M155.891 70.876a.562.562 0 10-1.123-.097.564.564 0 001.123.097h0zm1.306 1.246a.565.565 0 00-1.124-.098.564.564 0 101.124.098h0zm1.332-1.001a.563.563 0 00-.512-.61c-.31-.027-.527.203-.555.512-.027.311.146.584.456.611a.563.563 0 00.611-.513h0zm-1.135-1.246a.564.564 0 10-1.123-.097.564.564 0 001.123.097h0z"/>
    <path fill="#FFF" fill-rule="nonzero" d="M149.121 55.586a1.38 1.38 0 112.761 0 1.38 1.38 0 01-2.761 0z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".343" d="M149.121 55.586a1.38 1.38 0 112.761 0 1.38 1.38 0 01-2.761 0z"/>
    <path fill="#FFF" fill-rule="nonzero" d="M149.224 52.741c0-.474.572-.975 1.279-.975.706 0 1.278.501 1.278.975s-.572.906-1.278.906c-.707 0-1.279-.431-1.279-.906z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".343" d="M149.224 52.741c0-.474.572-.975 1.279-.975.706 0 1.278.501 1.278.975s-.572.906-1.278.906c-.707 0-1.279-.431-1.279-.906z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M136.841 78.155c0-1.946 5.739-3.413 13.678-3.413 7.94 0 13.678 1.467 13.678 3.413 0 1.946-5.739 3.407-13.678 3.407-7.94.001-13.678-1.461-13.678-3.407z"/>
    <path fill="#D99F31" fill-rule="nonzero" d="M141.275 72.317l-.224-1.344-.905-.456s-.972.264-1.578.48c-.628.223-1.579.649-1.579.649l-.226.395.283 1.52.507.451 3.383-1.014.339-.681z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".291" d="M141.275 72.317l-.224-1.344-.905-.456s-.972.264-1.578.48c-.628.223-1.579.649-1.579.649l-.226.395.283 1.52.507.451 3.383-1.014.339-.681h0z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".409" d="M141.208 72.317l-.78-.165"/>
    <path fill="#F1BF31" fill-rule="nonzero" d="M137.549 73.29l2.71-.857.169-.281-.235-1.407-.097-.097-3.009 1.105.229 1.275z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".409" d="M137.549 73.29l2.71-.857.169-.281-.179-1.125"/>
    <path fill="#D99F31" fill-rule="nonzero" d="M159.763 72.317l.224-1.344.905-.456s.972.264 1.578.48c.627.223 1.579.649 1.579.649l.226.395-.283 1.52-.507.451-3.383-1.014-.339-.681z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".291" d="M159.763 72.317l.224-1.344.905-.456s.972.264 1.578.48c.627.223 1.579.649 1.579.649l.226.395-.283 1.52-.507.451-3.383-1.014-.339-.681h0z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".409" d="M160.082 72.884l.697-.451"/>
    <path fill="#F1BF31" fill-rule="nonzero" d="M163.488 73.29l-2.708-.857-.17-.281.235-1.407.096-.097 3.011 1.105-.23 1.275z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".409" d="M163.488 73.29l-2.708-.857-.17-.281.178-1.125"/>
    <path fill="#F1BF31" fill-rule="nonzero" d="M145.147 70.876a.563.563 0 111.122-.098.563.563 0 01-1.122.098zm-1.306 1.246a.565.565 0 111.126-.096.565.565 0 01-1.126.096zm-1.333-1.001a.563.563 0 01.512-.61c.31-.027.527.203.554.512.027.311-.145.584-.456.611a.564.564 0 01-.61-.513zm1.137-1.246a.564.564 0 111.123-.097.564.564 0 01-1.123.097z"/>
    <path fill="#D99F31" fill-rule="nonzero" d="M145.18 71.024a.564.564 0 011.057.001.565.565 0 01-1.057-.001zm-1.502-1a.565.565 0 001.056-.001.563.563 0 00-1.056.001zm-1.136 1.245a.565.565 0 00.578.365.485.485 0 00.43-.354c-.071-.237-.267-.398-.529-.375a.566.566 0 00-.479.364zm1.333 1a.563.563 0 001.057 0 .566.566 0 00-1.057 0z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M145.147 70.876a.563.563 0 111.122-.098.563.563 0 01-1.122.098zm-1.306 1.246a.565.565 0 111.126-.096.565.565 0 01-1.126.096h0zm-1.333-1.001a.563.563 0 01.512-.61c.31-.027.527.203.554.512.027.311-.145.584-.456.611a.564.564 0 01-.61-.513h0zm1.137-1.246a.564.564 0 111.123-.097.564.564 0 01-1.123.097h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M133.534 72.285c-1.354-1.801-2.562-3.027-5.493-4.063.402-.324 1.114-.618 1.798-.786 1.116-.275 2.256-.126 2.989.258-.582-2.083.536-4.16.536-4.16s1.807-.103 3.637 1.367c.06-2.522.799-3.72.799-3.72s1.902.091 3.846 2.024c.371-2.609 1.778-4.025 1.778-4.025s2.336.732 3.671 3.196c.89-3.571 3.434-4.97 3.434-4.97s2.544 1.399 3.434 4.97c1.336-2.465 3.672-3.196 3.672-3.196s1.399 1.365 1.778 4.025c1.945-1.932 3.846-2.024 3.846-2.024s.74 1.198.8 3.72c1.83-1.471 3.817-.923 3.817-.923s.938 1.633.356 3.716c.733-.384 1.874-.533 2.989-.258.683.168 1.395.462 1.798.786-2.626.953-4.197 2.262-5.551 4.063-1.267-2.588-8.046-5.435-16.938-5.435-8.892.001-15.729 2.847-16.996 5.435z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M133.534 72.285c-1.354-1.801-2.562-3.027-5.493-4.063.402-.324 1.114-.618 1.798-.786 1.116-.275 2.256-.126 2.989.258-.582-2.083.536-4.16.536-4.16s1.807-.103 3.637 1.367c.06-2.522.799-3.72.799-3.72s1.902.091 3.846 2.024c.371-2.609 1.778-4.025 1.778-4.025s2.336.732 3.671 3.196c.89-3.571 3.434-4.97 3.434-4.97s2.544 1.399 3.434 4.97c1.336-2.465 3.672-3.196 3.672-3.196s1.399 1.365 1.778 4.025c1.945-1.932 3.846-2.024 3.846-2.024s.74 1.198.8 3.72c1.83-1.471 3.817-.923 3.817-.923s.938 1.633.356 3.716c.733-.384 1.874-.533 2.989-.258.683.168 1.395.462 1.798.786-2.626.953-4.197 2.262-5.551 4.063-1.267-2.588-8.046-5.435-16.938-5.435-8.892.001-15.729 2.847-16.996 5.435h0z"/>
    <path fill="#F1BF31" fill-rule="nonzero" d="M133.534 71.995c-1.158-1.815-2.83-3.143-4.564-3.815.403-.324 1.109-.448 1.858-.5.878-.061 1.609.062 2.229.357-.582-2.082.363-4.211.363-4.211s1.862.007 3.693 1.478c.06-2.523.86-3.774.86-3.774s1.783.198 3.728 2.132c.417-2.605 1.781-4.075 1.781-4.075s2.336.725 3.671 3.189c.89-3.57 3.375-4.846 3.375-4.846s2.486 1.276 3.376 4.846c1.335-2.464 3.671-3.189 3.671-3.189s1.407 1.317 1.781 4.075c1.945-1.933 3.728-2.132 3.728-2.132s.799 1.251.86 3.774c1.831-1.471 3.925-1.071 3.925-1.071s.714 1.723.132 3.805c.62-.294 1.351-.417 2.228-.357.749.052 1.457.176 1.859.5-1.734.671-3.406 2-4.563 3.815-1.267-2.588-8.104-5.197-16.997-5.197-8.89-.002-15.727 2.608-16.994 5.196z"/>
    <path fill="#000" fill-rule="nonzero" d="M135.76 68.621c-.283-.621-.791-1.614-.791-1.614-.008-.019.016-.029.025-.013 0 0 .637.901.995 1.46.194.303.457.689.66.983-.135.067-.268.134-.396.202a31.724 31.724 0 01-.493-1.018z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".169" d="M135.76 68.621c-.283-.621-.791-1.614-.791-1.614-.008-.019.016-.029.025-.013 0 0 .637.901.995 1.46.194.303.457.689.66.983m-.396.202a31.753 31.753 0 01-.494-1.018"/>
    <path fill="#000" fill-rule="nonzero" d="M144.647 65.3c-.144-.848-.23-1.326-.388-2.172-.003-.02.022-.026.028-.008.282.819.43 1.284.671 2.116.172.593.388 1.437.494 1.857-.169.02-.337.041-.503.063-.062-.389-.2-1.252-.302-1.856z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".169" d="M144.647 65.3c-.144-.848-.23-1.326-.388-2.172-.003-.02.022-.026.028-.008.282.819.43 1.284.671 2.116.172.593.388 1.437.494 1.857m-.504.062c-.062-.389-.199-1.252-.301-1.856"/>
    <path fill="#000" fill-rule="nonzero" d="M139.997 66.233c-.276-.668-.755-1.692-.755-1.692-.008-.018.014-.031.025-.014 0 0 .669 1.011 1.039 1.692.311.571.711 1.464.796 1.656l-.234.059-.214.002c-.015-.011-.388-1.051-.657-1.703z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".169" d="M139.997 66.233c-.276-.668-.755-1.692-.755-1.692-.008-.018.014-.031.025-.014 0 0 .669 1.011 1.039 1.692.311.571.711 1.464.796 1.656m-.235.058l-.214.002c-.014-.01-.387-1.05-.656-1.702"/>
    <path fill="#000" fill-rule="nonzero" d="M155.609 67.092c.106-.42.322-1.263.493-1.857.242-.831.389-1.296.671-2.115a.014.014 0 01.028.007 113.42 113.42 0 00-.689 4.028 36.337 36.337 0 00-.503-.063z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".169" d="M155.609 67.092c.106-.42.322-1.263.493-1.857.242-.831.389-1.296.671-2.115a.014.014 0 01.028.007 113.42 113.42 0 00-.689 4.028"/>
    <path fill="#000" fill-rule="nonzero" d="M159.958 67.875c.086-.192.486-1.084.796-1.656.37-.68 1.039-1.692 1.039-1.692.011-.017.034-.004.025.014 0 0-.48 1.024-.755 1.692-.27.652-.643 1.692-.643 1.692l-.247.003-.215-.053z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".169" d="M159.958 67.875c.086-.192.486-1.084.796-1.656.37-.68 1.039-1.692 1.039-1.692.011-.017.034-.004.025.014 0 0-.48 1.024-.755 1.692-.27.652-.643 1.692-.643 1.692l-.247.003"/>
    <path fill="#000" fill-rule="nonzero" d="M165.245 68.621c.282-.621.791-1.614.791-1.614.007-.019-.016-.029-.027-.013 0 0-.637.901-.995 1.46a36.76 36.76 0 01-.646.963c.135.066.266.132.393.2.155-.306.345-.692.484-.996z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".169" d="M165.245 68.621c.282-.621.791-1.614.791-1.614.007-.019-.016-.029-.027-.013 0 0-.637.901-.995 1.46a36.76 36.76 0 01-.646.963m.395.199c.153-.305.344-.691.483-.995"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M133.534 71.995c-1.158-1.815-2.83-3.143-4.564-3.815.403-.324 1.109-.448 1.858-.5.878-.061 1.609.062 2.229.357-.582-2.082.363-4.211.363-4.211s1.862.007 3.693 1.478c.06-2.523.86-3.774.86-3.774s1.783.198 3.728 2.132c.417-2.605 1.781-4.075 1.781-4.075s2.336.725 3.671 3.189c.89-3.57 3.375-4.846 3.375-4.846s2.486 1.276 3.376 4.846c1.335-2.464 3.671-3.189 3.671-3.189s1.407 1.317 1.781 4.075c1.945-1.933 3.728-2.132 3.728-2.132s.799 1.251.86 3.774c1.831-1.471 3.925-1.071 3.925-1.071s.714 1.723.132 3.805c.62-.294 1.351-.417 2.228-.357.749.052 1.457.176 1.859.5-1.734.671-3.406 2-4.563 3.815-1.267-2.588-8.104-5.197-16.997-5.197-8.89-.002-15.727 2.608-16.994 5.196h0z"/>
    <path fill="#D99F31" fill-rule="nonzero" d="M147.82 71.397v-1.46l.851-.792h3.719l.851.792.001 1.459-.852.737h-3.719z"/>
    <path fill="#F1BF31" fill-rule="nonzero" d="M152.465 69.317l-.3.305v1.394l-.237.235-2.816-.02-.234-.234v-1.339l-.252-.341z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".291" d="M147.82 71.397v-1.46l.851-.792h3.719l.851.792.001 1.459-.852.737h-3.719z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".409" d="M148.049 71.336l.829-.319m4.122.342l-.835-.343m-3.394 1.023l.338-.789m3.158.789l-.338-.789m.236-1.628v1.394l-.237.234-2.817-.02-.234-.234v-1.394"/>
    <path fill="#000" fill-rule="nonzero" d="M132.873 72.809c0-3.283 7.838-6.07 17.645-6.07 9.808 0 17.355 2.899 17.355 5.779 0 .69-.61 1.284-1.414 1.789a1.94 1.94 0 00.117-.661c0-2.88-6.25-5.214-16.058-5.214-9.807 0-16.057 2.334-16.057 5.214 0 .224.04.445.117.661-.805-.505-1.705-.809-1.705-1.498z"/>
    <path stroke="#000" stroke-width=".35" d="M132.873 72.809c0-3.283 7.838-6.07 17.645-6.07 9.808 0 17.355 2.899 17.355 5.779 0 .69-.61 1.284-1.414 1.789a1.94 1.94 0 00.117-.661c0-2.88-6.25-5.214-16.058-5.214-9.807 0-16.057 2.334-16.057 5.214 0 .224.04.445.117.661-.805-.505-1.705-.809-1.705-1.498h0z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".27" d="M136.102 74.033c-.126-.715-.744-1.29-1.221-1.206-.476.083-1.152.796-1.026 1.51.125.715 1.019 1.234 1.496 1.151.476-.084.877-.741.751-1.455h0z"/>
    <path fill="#D99F31" fill-rule="nonzero" d="M135.839 74.05c-.109-.618-.532-1.061-.944-.988-.413.073-.885.633-.777 1.251.11.618.771 1.068 1.183.996.413-.073.647-.641.538-1.259z"/>
    <path fill="#F1BF31" fill-rule="nonzero" d="M135.309 73.154a.899.899 0 01-1.135 1.357 1.117 1.117 0 01-.055-.198c-.109-.619.364-1.179.776-1.251a.547.547 0 01.09-.008c.111 0 .221.035.324.1z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".291" d="M135.839 74.05c-.109-.618-.532-1.061-.944-.988-.413.073-.885.633-.777 1.251.11.618.771 1.068 1.183.996.413-.073.647-.641.538-1.259h0z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".27" d="M164.902 74.033c.126-.715.745-1.29 1.22-1.206.476.083 1.153.796 1.028 1.51-.126.715-1.02 1.234-1.497 1.151-.475-.084-.877-.741-.751-1.455z"/>
    <path fill="#D99F31" fill-rule="nonzero" d="M165.165 74.05c.109-.618.532-1.061.944-.988.413.073.886.633.777 1.251-.109.618-.77 1.068-1.183.996-.413-.073-.647-.641-.538-1.259z"/>
    <path fill="#F1BF31" fill-rule="nonzero" d="M165.483 73.734c0-.221.08-.424.213-.58a.615.615 0 01.324-.1c.029 0 .06.002.09.008.413.073.885.632.776 1.252a1.127 1.127 0 01-.055.199.9.9 0 01-1.348-.779z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".291" d="M165.165 74.05c.109-.618.532-1.061.944-.988.413.073.886.633.777 1.251-.109.618-.77 1.068-1.183.996-.413-.073-.647-.641-.538-1.259z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".27" d="M136.102 74.033c-.126-.715-.744-1.29-1.221-1.206-.476.083-1.152.796-1.026 1.51.125.715 1.019 1.234 1.496 1.151.476-.084.877-.741.751-1.455h0z"/>
    <path fill="#D99F31" fill-rule="nonzero" d="M135.839 74.05c-.109-.618-.532-1.061-.944-.988-.413.073-.885.633-.777 1.251.11.618.771 1.068 1.183.996.413-.073.647-.641.538-1.259z"/>
    <path fill="#F1BF31" fill-rule="nonzero" d="M135.309 73.154a.899.899 0 01-1.135 1.357 1.117 1.117 0 01-.055-.198c-.109-.619.364-1.179.776-1.251a.547.547 0 01.09-.008c.111 0 .221.035.324.1z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".291" d="M135.839 74.05c-.109-.618-.532-1.061-.944-.988-.413.073-.885.633-.777 1.251.11.618.771 1.068 1.183.996.413-.073.647-.641.538-1.259h0z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".27" d="M136.102 74.033c-.126-.715-.744-1.29-1.221-1.206-.476.083-1.152.796-1.026 1.51.125.715 1.019 1.234 1.496 1.151.476-.084.877-.741.751-1.455h0z"/>
    <path fill="#D99F31" fill-rule="nonzero" d="M135.839 74.05c-.109-.618-.532-1.061-.944-.988-.413.073-.885.633-.777 1.251.11.618.771 1.068 1.183.996.413-.073.647-.641.538-1.259z"/>
    <path fill="#F1BF31" fill-rule="nonzero" d="M135.309 73.154a.899.899 0 01-1.135 1.357 1.117 1.117 0 01-.055-.198c-.109-.619.364-1.179.776-1.251a.547.547 0 01.09-.008c.111 0 .221.035.324.1z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".291" d="M135.839 74.05c-.109-.618-.532-1.061-.944-.988-.413.073-.885.633-.777 1.251.11.618.771 1.068 1.183.996.413-.073.647-.641.538-1.259h0z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".313" d="M159.605 72.329l.244-1.46.961-.477s1.041.283 1.691.514c.671.238 1.69.691 1.69.691l.242.423-.302 1.632-.543.484-3.623-1.087-.36-.72h0zm-11.966-2.449l.899-.841h3.984l.898.841.002 1.579-.9.781h-3.984l-.899-.781V69.88h0zm-6.206 2.449l-.245-1.46-.961-.477s-1.041.283-1.69.514a30.21 30.21 0 00-1.69.691l-.241.423.301 1.632.543.484 3.623-1.087.36-.72h0z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".386" d="M148.949 55.586a1.553 1.553 0 113.106-.002 1.553 1.553 0 01-3.106.002zm.116-2.902c0-.534.644-1.033 1.438-1.033.794 0 1.438.499 1.438 1.033 0 .533-.644 1.018-1.438 1.018-.794.001-1.438-.484-1.438-1.018h0z"/>
    <path fill="#F1BF31" fill-rule="nonzero" d="M133.334 72.518c0-2.491 7.378-5.604 17.186-5.604s17.186 3.114 17.186 5.604c0 .69-.768.759-.837.547-.824-2.525-6.541-4.75-16.349-4.75-9.807 0-15.524 2.225-16.349 4.75-.07.212-.837.143-.837-.547z"/>
    <path stroke="#000" stroke-width=".339" d="M133.334 72.518c0-2.491 7.378-5.604 17.186-5.604s17.186 3.114 17.186 5.604c0 .69-.768.759-.837.547-.824-2.525-6.541-4.75-16.349-4.75-9.807 0-15.524 2.225-16.349 4.75-.07.212-.837.143-.837-.547h0z"/>
    <path fill="#D99F31" fill-rule="nonzero" d="M152.802 50.109h-.157c-.421.39-1.082.786-2.116 1.015-1.035-.229-1.695-.625-2.115-1.015h-.148s-1.59 1.128-2.705.282a1.365 1.365 0 01-.499-.741s1.908-.104 1.908-1.798c0-1.689.917-2.4 2.15-2.4.539 0 1.029.266 1.409.611.379-.345.871-.611 1.409-.611 1.233 0 2.15.712 2.15 2.4 0 1.694 1.908 1.798 1.908 1.798s-.087.43-.498.741c-1.115.846-2.696-.282-2.696-.282z"/>
    <path fill="#F1BF31" fill-rule="nonzero" d="M147.593 46.78c0-.571.947-1.05 2.225-1.182.264.111.504.276.712.465a2.54 2.54 0 01.71-.464c1.275.132 2.219.61 2.219 1.181 0 1.354-1.547 2.009-2.934 2.008-1.386 0-2.932-.654-2.932-2.008z"/>
    <path stroke="#F1BF31" stroke-linecap="round" stroke-linejoin="round" stroke-width=".339" d="M147.187 46.888c-.099.24.059 1.074.059 1.074s.052.757-.544 1.334c-.416.403-1.367.578-1.367.578m8.541-2.986c.1.24-.058 1.074-.058 1.074s-.051.757.543 1.334c.416.403 1.367.578 1.367.578"/>
    <path fill="#F1BF31" fill-rule="nonzero" d="M150.494 50.95c-2.361-.536-2.667-1.999-2.602-1.982.155.038.7 1.105 2.638 1.644 1.938-.539 2.482-1.606 2.638-1.644.065-.016-.241 1.446-2.601 1.982h-.073z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M152.802 50.109h-.157c-.421.39-1.082.786-2.116 1.015-1.035-.229-1.695-.625-2.115-1.015h-.148s-1.59 1.128-2.705.282a1.365 1.365 0 01-.499-.741s1.908-.104 1.908-1.798c0-1.689.917-2.4 2.15-2.4.539 0 1.029.266 1.409.611.379-.345.871-.611 1.409-.611 1.233 0 2.15.712 2.15 2.4 0 1.694 1.908 1.798 1.908 1.798s-.087.43-.498.741c-1.115.846-2.696-.282-2.696-.282h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M153.359 48.982s-.282 1.894-2.83 2.458c-2.548-.564-2.83-2.458-2.83-2.458s.282 1.578 2.83 2.142c2.549-.564 2.83-2.142 2.83-2.142z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M153.359 48.982s-.282 1.894-2.83 2.458c-2.548-.564-2.83-2.458-2.83-2.458s.282 1.578 2.83 2.142c2.549-.564 2.83-2.142 2.83-2.142z"/>
    <path fill="#000" fill-rule="nonzero" d="M150.341 64.535c.057-.881.174-2.838.174-2.838.003-.019.027-.31.03-.29 0 0 .11 2.246.163 3.127.053.879.108 2.256.108 2.256a.015.015 0 01-.015.015l-.564.003a.015.015 0 01-.015-.015c.001-.001.063-1.379.119-2.258z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".169" d="M150.341 64.535c.057-.881.174-2.838.174-2.838.003-.019.027-.31.03-.29 0 0 .11 2.246.163 3.127.053.879.108 2.256.108 2.256a.015.015 0 01-.015.015l-.564.003a.015.015 0 01-.015-.015c.001-.001.063-1.379.119-2.258h0z"/>
    <path fill="#A0CFEB" fill-rule="nonzero" d="M150.047 85.309c5.078-.089 26.932-3.753 26.932 26.597 0 28.99-26.46 40.518-26.46 40.518s-26.461-11.168-26.461-42.717c0-28.151 25.989-24.398 25.989-24.398z"/>
    <path fill="#94BB79" fill-rule="nonzero" d="M158.63 130.645a10.708 10.708 0 015.232-1.357c3.089 0 5.873 1.303 7.834 3.388-7.72 13.881-21.178 19.748-21.178 19.748s-13.11-5.533-20.874-19.812a10.72 10.72 0 017.773-3.324c2.13 0 4.114.619 5.784 1.687a10.72 10.72 0 017.874-3.431c2.946 0 5.613 1.184 7.555 3.101z"/>
    <path fill="#658D5C" fill-rule="nonzero" d="M160.657 145.837c-3.399-3.336-11.042-10.124-17.439-14.879a10.724 10.724 0 016.969-3.378l.144.019c3.691.529 7.034 4.486 5.677 5.254-.996.562-3.846-.361-3.995.742-.049.367 3.822 2.708 6.089 4.657 2.225 1.913 3.681 3.288 5.331 5.091a51 51 0 01-2.776 2.494zM154.17 148.474a46.631 46.631 0 01-3.652 1.737 45.91 45.91 0 01-3.652-1.737c-3.266-2.644-9.99-8.815-14.725-17.786a10.53 10.53 0 015.161-1.342c.153 0 .789.185 1.255.404 2.976 1.398 6.658 5.413 5.302 6.181-.996.562-3.846-.361-3.995.742-.056.417 5.286 3.352 8.322 5.936 2.488 2.118 6.375 5.548 5.984 5.865zM163.238 129.435c1.333.154 3.665 1.409 4.792 3.101-.876 1.448-2.558 3.748-3.487 5.015-2.061-3.025-6.331-7.075-6.035-6.783.043.041.222-.104.274-.133a20.18 20.18 0 011.171-.615 10.668 10.668 0 012.384-.626c.284-.008.582.004.901.041z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M158.63 130.645a10.708 10.708 0 015.232-1.357c3.089 0 5.873 1.303 7.834 3.388m-42.051-.065a10.72 10.72 0 017.773-3.324c2.13 0 4.114.619 5.784 1.687a10.72 10.72 0 017.874-3.431c2.944 0 5.611 1.184 7.553 3.101"/>
    <path fill="#000" fill-rule="nonzero" d="M145.699 132.561c.073.066.092.156.052.223-.043.07-.133.086-.209.03-1.345-.955-2.001-1.356-2.78-1.845-.061-.039-.102-.104-.056-.172.03-.046.094-.075.153-.093.122-.035.307-.041.425.033.789.497 1.319.924 2.415 1.824zM158.777 130.479c.707.667 1.015 1.223 1.018 1.227.068.113-.008.372-.211.182 0 0-.281-.268-1.107-1.073-1.112-1.085-3.035-2.456-4.627-2.724-.153-.026-.219-.101-.2-.212a.226.226 0 01.267-.192c.028.005 2.378.448 4.86 2.792z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".466" d="M158.63 130.645c1.549-.864 3.333-1.531 5.232-1.531 3.104 0 5.9 1.363 7.862 3.512m-42.116-.085c1.96-2.131 4.734-3.485 7.81-3.485 2.13 0 4.114.851 5.784 1.919 1.963-2.111 4.764-3.663 7.874-3.663 2.944 0 5.611 1.416 7.553 3.333"/>
    <path stroke="#4C819A" stroke-linecap="round" stroke-linejoin="round" stroke-width=".291" d="M138.467 86.445h25.113m-28.468 1.392h31.545m-33.844 1.392h35.923m-37.667 1.393h39.237m-40.623 1.392h41.868m-42.999 1.392h44.015m-44.946 1.393h45.788m-46.563 1.392h47.27m-47.909 1.392h48.499m-49.031 1.393h49.53m-49.968 1.391h50.383m-50.738 1.393h51.083m-18.842 1.392h19.127m-51.647 0h19.258m19.493 1.392h13.126m-52.094 0h13.292m28.101 1.393h10.881m-52.433 0h10.963m32.952 1.392h8.655m-52.671 0h8.623m37.726 1.392h6.419m-52.821 0h6.411m41.667 1.391h4.8m-52.887 0h4.886m44.737 1.393h3.289m-52.885 0h3.181"/>
    <path fill="#000" fill-rule="nonzero" d="M139.032 100.763c-.106-.964-.481-2.523-1.768-2.325.066 1.676 2.198 7.984-.239 8.42l-.081-.087c.032-2.864-2.831-5.99-2.274-8.485.368-1.641 1.61-2.529 3.264-2.529 3.017 0 3.435 3.749 1.23 5.064l-.132-.058z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M139.032 100.763c-.106-.964-.481-2.523-1.768-2.325.066 1.676 2.198 7.984-.239 8.42l-.081-.087c.032-2.864-2.831-5.99-2.274-8.485.368-1.641 1.61-2.529 3.264-2.529 3.017 0 3.435 3.749 1.23 5.064l-.132-.058h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M152.283 98.729c-.105-.955-.478-2.582-1.768-2.383.065 1.683 2.197 8.042-.238 8.479l-.083-.088c.032-2.864-2.831-5.99-2.273-8.486.367-1.641 1.609-2.528 3.264-2.528 3.016 0 3.434 3.749 1.229 5.063l-.131-.057z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M152.283 98.729c-.105-.955-.478-2.582-1.768-2.383.065 1.683 2.197 8.042-.238 8.479l-.083-.088c.032-2.864-2.831-5.99-2.273-8.486.367-1.641 1.609-2.528 3.264-2.528 3.016 0 3.434 3.749 1.229 5.063l-.131-.057h0z"/>
    <path fill="#FFF" fill-rule="nonzero" d="M151.182 93.81c1.468 0 2.639.881 2.639 2.325 0 1.026-.591 1.771-1.451 2.284-.319-1.518-.93-2.284-1.856-2.284a.999.999 0 00-.231.027c.052 1.559 1.12 3.903 1.12 6.426 0 1.283-.838 1.915-1.18 1.915 0-3.499-2.308-4.802-2.308-7.011 0-.352.043-.786.15-1.221.174-.707.952-1.852 1.876-2.206.68-.258 1.129-.258 1.241-.255z"/>
    <path fill="#000" fill-rule="nonzero" d="M148.592 97.233c.233-.637.995-1.282 1.675-1.363.141-.017.255.11.311.266l-.065-.001c-.078 0-.154.01-.231.028.004.116.014.237.028.362-.319.02-.872.149-1.435.84-.262.321-.4.189-.283-.132z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".409" d="M148.75 97.427l1.594-.651m-1.473 1.226l1.576-.68m-1.449 1.288l1.575-.742m-1.272 1.32l1.42-.733m-.945 1.269l1.106-.629m-.308 1.013l.484-.269"/>
    <path fill="#000" fill-rule="nonzero" d="M152.178 95.313c-.835-1.23-2.938-.538-3.479.241-.039.057-.177-.028-.255-.058a.132.132 0 01-.029-.015c.146-.242.327-.487.534-.707 1.018-.65 2.789-.781 3.501.368a.155.155 0 01-.05.22.161.161 0 01-.222-.049z"/>
    <path fill="#FFF" fill-rule="nonzero" d="M137.931 95.845c1.468 0 2.639.88 2.639 2.324 0 1.026-.591 1.771-1.451 2.284-.318-1.518-.929-2.284-1.857-2.284a.94.94 0 00-.23.028c.051 1.559 1.121 3.902 1.121 6.426 0 1.282-.839 1.915-1.18 1.915 0-3.5-2.308-4.802-2.308-7.012 0-.351.044-.786.15-1.22.174-.708.952-1.852 1.876-2.206.679-.259 1.126-.26 1.24-.255z"/>
    <path fill="#000" fill-rule="nonzero" d="M135.34 99.267c.233-.637.995-1.283 1.675-1.363.141-.017.255.11.311.267l-.065-.001c-.078 0-.154.01-.231.028.004.116.013.238.028.362-.319.02-.872.148-1.436.839-.261.321-.399.189-.282-.132z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".409" d="M135.499 99.462l1.593-.651m-1.472 1.225l1.575-.68m-1.448 1.287l1.575-.742m-1.272 1.321l1.419-.733m-.944 1.27l1.106-.629m-.308 1.012l.484-.269"/>
    <path fill="#000" fill-rule="nonzero" d="M138.927 97.347c-.835-1.231-2.937-.539-3.479.241-.039.057-.177-.028-.255-.059a.16.16 0 01-.029-.015c.146-.242.327-.486.534-.706 1.018-.65 2.789-.781 3.502.368a.154.154 0 01-.051.219.16.16 0 01-.222-.048zM165.534 100.763c-.105-.955-.479-2.582-1.768-2.383.065 1.683 2.197 8.043-.239 8.478l-.082-.087c.032-2.864-2.831-5.99-2.273-8.485.367-1.641 1.609-2.529 3.264-2.529 3.017 0 3.435 3.749 1.229 5.064l-.131-.058z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M165.534 100.763c-.105-.955-.479-2.582-1.768-2.383.065 1.683 2.197 8.043-.239 8.478l-.082-.087c.032-2.864-2.831-5.99-2.273-8.485.367-1.641 1.609-2.529 3.264-2.529 3.017 0 3.435 3.749 1.229 5.064l-.131-.058h0z"/>
    <path fill="#FFF" fill-rule="nonzero" d="M164.433 95.845c1.468 0 2.639.88 2.639 2.324 0 1.026-.591 1.771-1.451 2.284-.318-1.518-.93-2.284-1.856-2.284a.934.934 0 00-.23.028c.051 1.559 1.12 3.902 1.12 6.426 0 1.282-.838 1.915-1.18 1.915 0-3.5-2.308-4.802-2.308-7.012 0-.351.043-.786.15-1.22.173-.708.951-1.852 1.876-2.206.679-.259 1.127-.26 1.24-.255z"/>
    <path fill="#000" fill-rule="nonzero" d="M161.843 99.267c.233-.637.995-1.283 1.675-1.363.141-.017.254.109.311.267l-.064-.001c-.078 0-.154.01-.231.028.004.116.014.237.028.362-.319.02-.872.148-1.436.839-.262.32-.401.189-.283-.132z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".409" d="M162.001 99.462l1.594-.651m-1.473 1.225l1.576-.68m-1.449 1.287l1.575-.742m-1.272 1.321l1.42-.734m-.944 1.271l1.105-.628m-.308 1.011l.484-.269"/>
    <path fill="#000" fill-rule="nonzero" d="M165.429 97.347c-.835-1.231-2.938-.539-3.479.241-.039.057-.177-.028-.255-.059a.16.16 0 01-.029-.015c.146-.242.327-.486.534-.706 1.018-.65 2.789-.781 3.502.368a.154.154 0 01-.051.219.16.16 0 01-.222-.048zM134.246 112.649l-.006-2.709 2.905-1.134 2.906.785.007 3.058z"/>
    <path fill="#FFF" fill-rule="nonzero" d="M140.464 124.959l1.273 5.231a10.72 10.72 0 00-4.319-.902c-1.751 0-3.404.419-4.864 1.161l1.453-5.28-.034-10.273-1.419-1.704v-3.575l1.976-.839v2.246l1.453-.579v-2.237l1.162-.508 1.162.334v2.27l1.453.374v-2.248l1.977.52v3.69l-1.273 1.889v10.43z"/>
    <path fill="#BDBFC1" fill-rule="nonzero" d="M137.145 124.555v-16.854l-1.162.508v2.237l-1.453.579v-2.246l-1.976.839v3.575l1.42 1.703.033 10.272-1.453 5.281a10.694 10.694 0 014.592-1.158v-4.736h-.001z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".466" d="M137.145 124.555v-16.854m0 21.59v-4.736"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M140.464 124.959l1.273 5.231a10.72 10.72 0 00-4.319-.902c-1.751 0-3.404.419-4.864 1.161l1.453-5.28-.034-10.273-1.419-1.704v-3.575l1.976-.839v2.246l1.453-.579v-2.237l1.162-.508 1.162.334v2.27l1.453.374v-2.248l1.977.52v3.69l-1.273 1.889v10.43h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M160.399 112.708l-.006-3.059 2.906-.814 2.906.959.006 2.914zM147.323 110.615l-.007-2.768 2.906-1.046 2.906.901.006 2.913z"/>
    <path fill="#FFF" fill-rule="nonzero" d="M153.541 123.041l1.273 5.171a10.718 10.718 0 00-3.737-.667 10.7 10.7 0 00-5.446 1.479l1.453-5.888-.034-10.343-1.419-1.739v-3.615l1.976-.78v2.246l1.453-.521v-2.267l1.162-.45 1.162.392v2.269l1.453.403v-2.218l1.977.635v3.679l-1.273 1.813v10.401z"/>
    <path fill="#BDBFC1" fill-rule="nonzero" d="M150.222 127.571v-21.905l-1.162.451v2.267l-1.454.519v-2.245l-1.976.78v3.615l1.42 1.739.034 10.343-1.454 5.888a10.69 10.69 0 014.592-1.446v-.006z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".466" d="M150.222 127.571v-21.905"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M153.541 123.041l1.273 5.171a10.718 10.718 0 00-3.737-.667 10.7 10.7 0 00-5.446 1.479l1.453-5.888-.034-10.343-1.419-1.739v-3.615l1.976-.78v2.246l1.453-.521v-2.267l1.162-.45 1.162.392v2.269l1.453.403v-2.218l1.977.635v3.679l-1.273 1.813v10.401h0z"/>
    <path fill="#FFF" fill-rule="nonzero" d="M166.618 125.075l1.273 4.993a10.719 10.719 0 00-4.028-.78 10.71 10.71 0 00-5.155 1.314l1.453-5.637-.033-10.33-1.42-1.82v-3.661l1.976-.636v2.245l1.453-.433v-2.266l1.162-.363 1.162.391v2.27l1.453.49v-2.219l1.977.665v3.633l-1.273 1.802v10.342z"/>
    <path fill="#BDBFC1" fill-rule="nonzero" d="M163.299 124.555v-16.854l-1.162.363v2.267l-1.454.433v-2.245l-1.976.636v3.661l1.42 1.82.034 10.331-1.454 5.636a10.698 10.698 0 014.592-1.3v-4.748z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".466" d="M163.299 124.555v-16.854m0 21.601v-4.747"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M166.618 125.075l1.273 4.993a10.719 10.719 0 00-4.028-.78 10.71 10.71 0 00-5.155 1.314l1.453-5.637-.033-10.33-1.42-1.82v-3.661l1.976-.636v2.245l1.453-.433v-2.266l1.162-.363 1.162.391v2.27l1.453.49v-2.219l1.977.665v3.633l-1.273 1.802v10.342h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M139.47 121.126v4.378l-1.396-.192.001-4.418c0-.544.33-.876.628-.876.527 0 .767.594.767 1.108z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".409" d="M137.151 111.525l4.585 1.116m-4.585-1.116l-4.597 1.668"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M137.151 113.821l3.136.709m-3.136-.709l-3.178 1.076"/>
    <path fill="#000" fill-rule="nonzero" d="M152.546 119.15v4.377l-1.396-.221.001-4.418c0-.544.33-.846.628-.846.528 0 .767.593.767 1.108z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".409" d="M150.222 122.492l-3.139.643m3.139-13.642l4.591 1.335m-4.591-1.335l-4.592 1.561m4.599.733l3.136.849m-3.136-.844l-3.179 1"/>
    <path fill="#000" fill-rule="nonzero" d="M165.624 121.213v4.377l-1.396-.221.001-4.418c0-.544.331-.847.628-.847.527 0 .767.595.767 1.109z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".409" d="M163.305 124.515l-3.145.509m3.145-13.499l4.585 1.406m-4.585-1.406l-4.598 1.29m4.598 1.006l3.136.912m-3.136-.912l-3.178.814m-9.899-1.325l3.136.807m-3.136.721l3.136.768m-3.136.766l3.136.716m-3.136.813l3.136.674m-3.136.855l3.166.634m-3.166.9l3.195.593m-3.195-8.247l-3.181.953m3.181.575l-3.181.912m3.181.622l-3.181.861m3.181.668l-3.181.79m3.181.739l-3.152.75m3.152.784l-3.152.68m5.186-9.271v1.439m-1.162-.248v1.468m1.162.311v1.467m-2.964-1.691v1.468m-1.395-2.499v1.409m1.395-3.414v1.468m14.001 1.793l3.137.837m-3.137.692l3.137.797m-3.137.737l3.137.745m-3.137.784l3.137.703m-3.137.825l3.166.663m-3.166.871l3.195.593m-3.195-8.247l-3.181.778m3.181.751l-3.181.738m3.181.796l-3.181.686m3.181.843l-3.181.645m3.181.883l-3.152.605m3.152.929l-3.152.535m5.186-9.097v1.439m-1.163-.276v1.468m1.163.339v1.468m-2.963-1.779v1.468m0 1.569v1.468m-1.396.365v1.41m0-7.37v1.41m1.396-3.356v1.468m-13.072 5.651v1.468m13.833 3.438l-3.319.509m-11.909-8.099v1.409m2.271 5.732l-3.855.739m3.913-.739l3.909.495m-4.09-2.069l-3.216.617m16.416 2.961l-3.911.611m3.968-.611l3.982.528"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M142.082 130.162c.028.116-.283.008-.392-.041a10.596 10.596 0 00-4.271-.892c-1.732 0-3.367.414-4.813 1.148-.112.057-.431.161-.397.04l1.681-5.264-.033-10.215-1.393-1.672a.385.385 0 01-.027-.074v-3.575c0-.031.041-.094.07-.107l1.976-.84c.09-.038.162.01.162.107v2.074l1.22-.486v-2.159c0-.03.042-.094.07-.106l1.162-.508a.298.298 0 01.079-.005l1.162.334c.038.011.084.072.084.111v2.179l1.22.315v-2.097c0-.085.065-.134.146-.113l1.976.52c.04.01.087.071.087.112v3.691c0 .006-.016.06-.02.065l-1.253 1.86v10.379l1.504 5.219h0zm-.455-.17a11.122 11.122 0 00-4.208-.82c-1.677 0-3.324.379-4.743 1.058l1.443-5.03-.03-10.304a.407.407 0 00-.026-.074l-1.393-1.671v-3.455l1.743-.74v2.07c0 .095.071.143.16.107l1.453-.578c.03-.012.073-.075.073-.108v-2.162l1.054-.461 1.037.299v2.182c0 .041.047.102.087.112l1.453.374c.082.021.146-.029.146-.112v-2.098l1.744.459v3.566l-1.253 1.859a.36.36 0 00-.02.066v10.429l1.28 5.032h0zm13.532-1.809c.027.112-.279.003-.386-.037a10.627 10.627 0 00-3.696-.66c-1.966 0-3.807.533-5.387 1.463-.111.065-.435.171-.404.046l1.682-5.875-.034-10.286-1.393-1.707a.373.373 0 01-.026-.073v-3.615c0-.033.043-.096.074-.108l1.975-.781c.089-.035.159.013.159.108v2.08l1.221-.437v-2.185c0-.033.043-.096.075-.108l1.162-.451a.36.36 0 01.079-.001l1.162.392c.034.012.079.073.079.109v2.182l1.221.338v-2.066c0-.089.067-.138.152-.111l1.977.636c.036.012.08.073.08.111v3.679a.373.373 0 01-.021.067l-1.251 1.783v10.349l1.5 5.158h0zm-.452-.154a11.207 11.207 0 00-3.63-.601c-1.912 0-3.766.494-5.327 1.36l1.446-5.625-.029-10.37a.32.32 0 00-.027-.073l-1.393-1.706v-3.495l1.744-.69v2.075c0 .093.068.141.155.11l1.453-.521c.034-.012.077-.074.077-.109v-2.186l1.049-.407 1.043.352v2.186c0 .04.046.101.085.112l1.453.403c.082.023.148-.027.148-.112v-2.059l1.744.561v3.558l-1.252 1.783a.52.52 0 00-.021.067v10.4l1.282 4.987h0zm13.528 2.009c.029.114-.279.006-.388-.038a10.61 10.61 0 00-3.983-.771c-1.848 0-3.586.472-5.1 1.3-.112.061-.375-.066-.343-.189l1.623-5.39-.033-10.276-1.395-1.788a.307.307 0 01-.025-.072v-3.662c0-.037.045-.099.081-.111l1.975-.635c.086-.028.153.021.153.111v2.09l1.22-.364v-2.179c0-.038.045-.1.082-.112l1.163-.363a.348.348 0 01.072.001l1.161.392c.035.012.079.074.079.11v2.186l1.221.412v-2.056c0-.09.068-.139.153-.111l1.977.665c.034.012.079.074.079.111v3.632a.4.4 0 01-.021.067l-1.251 1.772v10.291l1.5 4.977h0zm-.455-.162a11.152 11.152 0 00-3.916-.705c-1.794 0-3.544.435-5.035 1.204l1.445-5.381-.03-10.36a.384.384 0 00-.025-.072l-1.394-1.787v-3.537l1.743-.561v2.086c0 .087.065.136.149.112l1.453-.434c.037-.011.083-.071.083-.111v-2.181l1.045-.326 1.046.354v2.186c0 .036.044.099.079.11l1.454.491c.086.029.153-.019.153-.11v-2.057l1.744.587v3.512l-1.252 1.771a.575.575 0 00-.021.067v10.342l1.279 4.8h0z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".409" d="M137.093 124.515l-3.086.654m3.145 2.455l4.045.438m-4.104-.438l-3.739.796m3.634-2.318l-3.325.677m3.483-11.429l3.136.663m-3.136.86l3.136.651m-3.136.883l3.136.599m-3.136.93l3.136.558m-3.136.97l3.165.518m-3.165 1.016l3.195.477m-3.195-8.125l-3.181 1.029m3.181.494l-3.181.971m3.181.563l-3.181.919m3.181.61l-3.181.849m3.181.679l-3.153.779m3.153.755l-3.153.709m5.186-9.386v1.439m-1.162-.219v1.468m1.162.31v1.469m-2.964-1.606v1.468m0 1.541v1.467m-1.394.511v1.352m0-7.283v1.352m1.394-3.415v1.469"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M139.196 100.323c-.246-1.076-.837-2.479-2.192-2.239l-.089.117c.062 1.802 2.321 7.282.171 8.249-.057-3.138-2.886-5.174-2.16-8.116.201-.815 1.014-1.828 1.806-2.125.371-.14.794-.256 1.196-.249 2.722.057 3.428 2.878 1.268 4.363h0zm13.251-2.034c-.246-1.076-.838-2.479-2.192-2.239l-.089.117c.062 1.802 2.321 7.282.17 8.249-.057-3.138-2.885-5.173-2.159-8.115.201-.815 1.015-1.827 1.806-2.125.372-.14.794-.257 1.196-.248 2.722.054 3.428 2.876 1.268 4.361zm13.251 2.034c-.246-1.076-.838-2.479-2.192-2.239l-.089.117c.062 1.802 2.321 7.282.171 8.249-.057-3.138-2.886-5.174-2.159-8.116.201-.815 1.015-1.828 1.805-2.125.372-.14.795-.256 1.197-.249 2.722.057 3.428 2.878 1.267 4.363h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M140.543 80.61l8.056 3.769c.422-.589 1.228-.972 2.006-.972.779 0 1.585.383 2.006.972l8.056-3.769.497 11.916-8.535-5.087c-.643.677-1.139 1.025-2.024 1.025-.885.001-1.465-.425-2.025-1.025l-8.593 5.029.556-11.858z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M140.543 80.61l8.056 3.769c.422-.589 1.228-.972 2.006-.972.779 0 1.585.383 2.006.972l8.056-3.769.497 11.916-8.535-5.087c-.643.677-1.139 1.025-2.024 1.025-.885.001-1.465-.425-2.025-1.025l-8.593 5.029.556-11.858h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M181.394 107.289c0-12.815-5.088-26.697-15.179-26.697-3.448 0-7.145 1.781-7.145 5.145s1.974 5.837 4.921 5.837 3.856-1.375 3.856-1.375c1.224.807 5.554 3.241 6.195 17.113 1.249 27.036-21.756 42.811-21.756 42.811s29.108-12.394 29.108-42.834z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M181.394 107.289c0-12.815-5.088-26.697-15.179-26.697-3.448 0-7.145 1.781-7.145 5.145s1.974 5.837 4.921 5.837 3.856-1.375 3.856-1.375c1.224.807 5.554 3.241 6.195 17.113 1.249 27.036-21.756 42.811-21.756 42.811s29.108-12.394 29.108-42.834h0z"/>
    <path fill="#D99F31" fill-rule="nonzero" d="M181.394 107.289c0-12.815-5.088-26.697-15.179-26.697-3.448 0-7.145 1.781-7.145 5.145s1.974 5.837 4.921 5.837 4.03-1.724 4.03-1.724c1.224.807 5.671 3.59 6.311 17.462 1.249 27.036-22.048 42.811-22.048 42.811s29.11-12.394 29.11-42.834z"/>
    <path fill="#000" fill-rule="nonzero" d="M165.921 87.282c3.567.436 6.772 4.614 8.127 10.12.032.14-.001.279-.12.311-.116.032-.219-.043-.269-.223-1.542-5.935-4.599-9.253-7.785-9.659l.047-.549z"/>
    <path fill="#000" fill-rule="nonzero" d="M177.79 97.866c.028.145-.021.281-.145.301-.119.019-.215-.083-.243-.222-1.759-8.364-5.92-14.52-12.43-14.52-2.289 0-4.248.787-5.897 2.158.007-.207.028-.408.062-.603 1.658-1.277 3.591-1.992 5.835-1.992 6.858.001 11.132 6.491 12.818 14.878z"/>
    <path fill="#000" fill-rule="nonzero" d="M176.355 97.666c.039.154.025.284-.111.318-.111.028-.211-.055-.248-.219-1.789-7.644-5.615-12.886-10.943-12.886-2.292 0-4.246.571-5.865 2.236a7.6 7.6 0 01-.074-.536c1.661-1.602 3.635-2.135 5.939-2.135 5.671-.001 9.616 5.599 11.302 13.222z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M181.394 107.289c0-12.815-5.088-26.697-15.179-26.697-3.448 0-7.145 1.781-7.145 5.145s1.974 5.837 4.921 5.837 4.03-1.724 4.03-1.724c1.224.807 5.671 3.59 6.311 17.462 1.249 27.036-22.048 42.811-22.048 42.811s29.11-12.394 29.11-42.834h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M119.643 107.289c0-12.815 5.087-26.697 15.179-26.697 3.447 0 7.144 1.781 7.144 5.145s-1.974 5.837-4.921 5.837-3.855-1.375-3.855-1.375c-1.224.807-5.554 3.241-6.195 17.113-1.249 27.036 21.756 42.811 21.756 42.811s-29.108-12.394-29.108-42.834z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M119.643 107.289c0-12.815 5.087-26.697 15.179-26.697 3.447 0 7.144 1.781 7.144 5.145s-1.974 5.837-4.921 5.837-3.855-1.375-3.855-1.375c-1.224.807-5.554 3.241-6.195 17.113-1.249 27.036 21.756 42.811 21.756 42.811s-29.108-12.394-29.108-42.834h0z"/>
    <path fill="#D99F31" fill-rule="nonzero" d="M138.917 80.304l11.689 5.079v.582l-10.1 5.971s1.343-3.439 1.123-5.689c-.249-2.537-2.712-5.943-2.712-5.943z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M143.455 90.192c.274-.325 1.098-1.496 1.098-3.851m.111 3.136c.256-.344.872-1.354.872-3.137m.324 2.431c.218-.335.658-1.168.658-2.43m.506 1.741c.15-.299.419-.947.419-1.742"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M138.917 80.304l11.689 5.079v.582l-10.1 5.971s1.343-3.439 1.123-5.689c-.249-2.537-2.712-5.943-2.712-5.943h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M160.084 89.504l-.251-.359c.825-2.55 2.359-3.758 5.251-3.767 4.66.022 8.238 5.272 10.098 12.125.038.135.012.28-.108.304-.118.023-.213-.067-.247-.195-1.865-6.763-5.414-11.689-9.714-11.711-2.769.008-4.169 1.009-5.029 3.603z"/>
    <path fill="#D99F31" fill-rule="nonzero" d="M162.295 80.304l-11.689 5.079v.582l10.099 5.971s-1.342-3.439-1.122-5.689c.248-2.537 2.712-5.943 2.712-5.943z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M157.757 90.192c-.274-.325-1.098-1.496-1.098-3.851m-.111 3.136c-.256-.344-.872-1.354-.872-3.137m-.322 2.431c-.218-.335-.66-1.168-.66-2.431m-.507 1.742a3.995 3.995 0 01-.418-1.742"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M162.295 80.304l-11.689 5.079v.582l10.099 5.971s-1.342-3.439-1.122-5.689c.248-2.537 2.712-5.943 2.712-5.943h0z"/>
    <path fill="#D99F31" fill-rule="nonzero" d="M119.643 107.289c0-12.815 5.087-26.697 15.179-26.697 3.447 0 7.144 1.781 7.144 5.145s-1.974 5.837-4.921 5.837-4.03-1.724-4.03-1.724c-1.224.807-5.671 3.59-6.312 17.462-1.248 27.036 22.048 42.811 22.048 42.811s-29.108-12.394-29.108-42.834z"/>
    <path fill="#000" fill-rule="nonzero" d="M140.942 89.469c-.863-2.576-2.271-3.568-5.017-3.568-3.621 0-6.601 3.29-8.571 8.264-.048.114-.132.181-.24.144-.094-.032-.108-.16-.066-.276 1.918-5.055 4.994-8.655 8.878-8.655 2.882 0 4.421 1.185 5.256 3.693-.076.138-.156.27-.24.398z"/>
    <path fill="#000" fill-rule="nonzero" d="M135.984 84.747c-4.323 0-7.694 3.645-9.818 9.007-.061.128-.148.196-.266.148-.092-.038-.102-.175-.055-.289 2.017-5.423 5.556-9.273 10.14-9.273 2.299 0 4.27.728 5.929 2.324-.02.176-.046.348-.077.517-1.626-1.668-3.575-2.434-5.853-2.434z"/>
    <path fill="#000" fill-rule="nonzero" d="M135.984 83.411c-4.955 0-8.632 3.489-11.115 9.926-.057.12-.17.212-.294.168-.098-.035-.109-.175-.065-.301 2.483-6.558 6.272-10.2 11.473-10.2 2.264 0 4.21.558 5.876 1.782.044.195.074.396.091.604-1.67-1.361-3.65-1.979-5.966-1.979z"/>
    <path fill="#000" fill-rule="nonzero" d="M133.284 88.23c-2.024 1.307-3.859 3.786-4.888 6.347-.057.134-.144.174-.233.142-.079-.029-.113-.143-.076-.256a14.899 14.899 0 015.075-6.82l.122.587z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M119.643 107.289c0-12.815 5.087-26.697 15.179-26.697 3.447 0 7.144 1.781 7.144 5.145s-1.974 5.837-4.921 5.837-4.03-1.724-4.03-1.724c-1.224.807-5.671 3.59-6.312 17.462-1.248 27.036 22.048 42.811 22.048 42.811s-29.108-12.394-29.108-42.834h0z"/>
    <path fill="#D99F31" fill-rule="nonzero" d="M148.281 85.965a2.325 2.325 0 114.65 0 2.325 2.325 0 01-4.65 0z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M148.301 85.417a2.368 2.368 0 012.305-1.834c.985 0 1.83.598 2.193 1.451m.123 1.523a2.392 2.392 0 01-2.316 1.789 2.381 2.381 0 01-2.223-1.527m2.835.61a1.587 1.587 0 01-2.199-1.404m2.953.748a1.587 1.587 0 01-.754.656m-.446-.712a.771.771 0 01-.933-.674m-.792-.465c.047-.208.138-.333.176-.392"/>
    <path fill="#000" fill-rule="nonzero" d="M136.476 85.779a2.465 2.465 0 01-.024-.311c.022-.023.032-.026.043-.028a.3.3 0 01.065-.003c1.676.196 2.8 1.311 2.856 2.911.032.909-.423 2.174-1.043 2.742a.06.06 0 01-.082-.004l-.235-.258a.058.058 0 01.003-.082c.526-.48.89-1.616.863-2.38-.05-1.427-1.025-2.362-2.419-2.526a.137.137 0 01-.027-.061zM164.529 85.779c.005-.035.01-.081.014-.127.007-.093.01-.184.01-.184-.022-.023-.032-.026-.043-.028a.3.3 0 00-.065-.003c-1.676.196-2.8 1.311-2.856 2.911-.032.909.423 2.174 1.043 2.742a.06.06 0 00.082-.004l.235-.258a.06.06 0 00-.003-.082c-.526-.48-.89-1.616-.864-2.38.05-1.427 1.025-2.362 2.419-2.526a.13.13 0 00.028-.061z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M148.301 85.417c0-1.094.959-2.01 2.305-2.01 1.027 0 2.193.722 2.193 1.627m.123 1.523c0 1.011-1.205 1.904-2.316 1.904-1.014 0-2.223-.737-2.223-1.643"/>
    <path fill="#000" fill-rule="nonzero" d="M141.783 90.833c-1.225 1.624-3.116 2.652-5.238 2.652-2.773 0-4.885-1.724-4.885-4.479.063-1.46 1.049-3.263 3.381-3.263 1.515 0 2.802 1.461 2.802 2.976 0 .838-.161 1.344-.675 1.974 2.076-.667 3.45-1.774 3.45-4.129 0-2.415-1.602-4.145-4.226-4.363-8.769-.729-14.128 9.005-15.16 22.46-1.472 19.191 8.879 34.49 24.614 43.226 2.196 1.219 3.919 2.194 4.665 2.51l.01 2.387c-5.99-2.29-12.936-6.709-17.267-10.666-8.247-7.534-16.115-20.279-14.921-37.445.754-10.84 4.13-18.11 8.273-21.701 7.085-6.143 16.744-3.689 16.744 3.591-.002 1.589-.627 3.084-1.567 4.27z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M141.783 90.833c-1.225 1.624-3.116 2.652-5.238 2.652-2.773 0-4.885-1.724-4.885-4.479.063-1.46 1.049-3.263 3.381-3.263 1.515 0 2.802 1.461 2.802 2.976 0 .838-.161 1.344-.675 1.974 2.076-.667 3.45-1.774 3.45-4.129 0-2.415-1.602-4.145-4.226-4.363-8.769-.729-14.128 9.005-15.16 22.46-1.472 19.191 8.879 34.49 24.614 43.226 2.196 1.219 3.919 2.194 4.665 2.51l.01 2.387c-5.99-2.29-12.936-6.709-17.267-10.666-8.247-7.534-16.115-20.279-14.921-37.445.754-10.84 4.13-18.11 8.273-21.701 7.085-6.143 16.744-3.689 16.744 3.591-.002 1.589-.627 3.084-1.567 4.27h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M159.255 90.833c1.225 1.624 3.116 2.652 5.237 2.652 2.774 0 4.886-1.724 4.886-4.479-.063-1.46-1.049-3.263-3.381-3.263-1.515 0-2.801 1.461-2.801 2.976 0 .838.161 1.344.675 1.974-2.076-.667-3.45-1.774-3.45-4.129 0-2.415 1.602-4.145 4.226-4.363 8.769-.729 14.128 9.005 15.16 22.46 1.472 19.191-8.879 34.49-24.614 43.226-2.197 1.219-3.919 2.194-4.665 2.51l-.01 2.387c5.991-2.29 12.936-6.709 17.267-10.666 8.247-7.534 16.115-20.279 14.921-37.445-.754-10.84-4.13-18.11-8.273-21.701-7.085-6.143-16.744-3.689-16.744 3.591 0 1.589.626 3.084 1.566 4.27z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M159.255 90.833c1.225 1.624 3.116 2.652 5.237 2.652 2.774 0 4.886-1.724 4.886-4.479-.063-1.46-1.049-3.263-3.381-3.263-1.515 0-2.801 1.461-2.801 2.976 0 .838.161 1.344.675 1.974-2.076-.667-3.45-1.774-3.45-4.129 0-2.415 1.602-4.145 4.226-4.363 8.769-.729 14.128 9.005 15.16 22.46 1.472 19.191-8.879 34.49-24.614 43.226-2.197 1.219-3.919 2.194-4.665 2.51l-.01 2.387c5.991-2.29 12.936-6.709 17.267-10.666 8.247-7.534 16.115-20.279 14.921-37.445-.754-10.84-4.13-18.11-8.273-21.701-7.085-6.143-16.744-3.689-16.744 3.591 0 1.589.626 3.084 1.566 4.27h0z"/>
    <path fill="#F1BF31" fill-rule="nonzero" d="M150.518 152.608c-5.991-2.29-12.936-7.001-17.267-10.957-8.247-7.534-15.592-19.989-14.398-37.154.755-10.839 3.956-17.703 8.099-21.294 7.085-6.142 16.222-3.346 16.222 3.36a6.691 6.691 0 01-6.687 6.689c-2.235 0-4.595-1.814-4.362-4.538a2.744 2.744 0 015.485.12c0 .837-.549 1.645-1.14 2.148 2.333-.083 4.379-2.065 4.379-4.42 0-2.415-1.834-4.436-4.458-4.654-8.769-.729-14.437 9.237-15.393 22.75-1.359 19.202 9.111 34.723 24.846 43.459a47.202 47.202 0 004.675 2.281 47.797 47.797 0 004.675-2.281c15.735-8.735 26.205-24.257 24.846-43.459-.956-13.514-6.623-23.479-15.393-22.75-2.624.218-4.458 2.238-4.458 4.654 0 2.355 2.045 4.337 4.379 4.42-.591-.504-1.141-1.311-1.141-2.148a2.743 2.743 0 015.486-.12c.233 2.724-2.127 4.538-4.363 4.538a6.69 6.69 0 01-6.686-6.689c0-6.706 9.136-9.502 16.221-3.36 4.144 3.592 7.345 10.455 8.099 21.294 1.194 17.166-6.151 29.62-14.398 37.154-4.332 3.957-11.277 8.667-17.268 10.957z"/>
    <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M150.518 152.608c-5.991-2.29-12.936-7.001-17.267-10.957-8.247-7.534-15.592-19.989-14.398-37.154.755-10.839 3.956-17.703 8.099-21.294 7.085-6.142 16.222-3.346 16.222 3.36a6.691 6.691 0 01-6.687 6.689c-2.235 0-4.595-1.814-4.362-4.538a2.744 2.744 0 015.485.12c0 .837-.549 1.645-1.14 2.148 2.333-.083 4.379-2.065 4.379-4.42 0-2.415-1.834-4.436-4.458-4.654-8.769-.729-14.437 9.237-15.393 22.75-1.359 19.202 9.111 34.723 24.846 43.459a47.202 47.202 0 004.675 2.281 47.797 47.797 0 004.675-2.281c15.735-8.735 26.205-24.257 24.846-43.459-.956-13.514-6.623-23.479-15.393-22.75-2.624.218-4.458 2.238-4.458 4.654 0 2.355 2.045 4.337 4.379 4.42-.591-.504-1.141-1.311-1.141-2.148a2.743 2.743 0 015.486-.12c.233 2.724-2.127 4.538-4.363 4.538a6.69 6.69 0 01-6.686-6.689c0-6.706 9.136-9.502 16.221-3.36 4.144 3.592 7.345 10.455 8.099 21.294 1.194 17.166-6.151 29.62-14.398 37.154-4.332 3.957-11.277 8.667-17.268 10.957h0z"/>
    <path fill="#FFF" fill-rule="nonzero" d="M151.139 160.186c2.884-.034 5.052-.308 7.518-.795-.582 1.977-1.745 5.739 2.904 5.739 3.383 0 6.974-2.035 10.461-.245 7.617 3.906 13.367 1.117 18.017-4.114 0 0-3.437.734-6.974.581 0 0 2.65-2.258 2.906-5.231-2.325 1.743-4.65 1.998-6.975 1.998-2.324 0-3.81-1.404-6.854-1.884.171-1.052.355-2.123.629-3.6.299-1.613-.146-2.921-2.492-3.197-5.105-.599-10.05 2.345-19.085 2.487-9.096-.142-15.331-3.087-20.437-2.487-2.346.276-2.79 1.584-2.491 3.197.274 1.477.458 2.548.628 3.6-3.043.48-4.529 1.884-6.853 1.884-2.327 0-4.651-.255-6.976-1.998.256 2.973 2.906 5.231 2.906 5.231-3.536.153-6.974-.581-6.974-.581 4.65 5.23 10.4 8.02 18.017 4.114 3.487-1.789 7.078.245 10.461.245 4.649 0 3.486-3.762 2.904-5.739 2.639.521 5.559.808 8.758.808l.002-.013z"/>
    <path fill="#000" fill-rule="nonzero" d="M127.54 162.367c-.338.157-.512-.061-.131-.255.308-.158 2.003-.959 2.908-1.019a.23.23 0 01.242.156c.029.098-.026.199-.16.209-.813.053-2.535.759-2.859.909zM139.439 162.327c-1.876-.195-4.365-.835-6.752-.94-.123-.005-.201-.088-.201-.182-.001-.098.079-.177.204-.171 2.395.106 4.913.677 6.796.872.695.073 1.455-.065 1.82-.392.11-.099.392.272.287.365-.483.433-1.402.526-2.154.448zM134.252 163.445c-1.974-.307-4.408.026-6.293.965a.62.62 0 01-.213.061c-.046.004-.114.002-.144-.051a.097.097 0 01-.003-.083.19.19 0 01.044-.063.693.693 0 01.179-.123c2.778-1.346 4.899-1.257 6.551-1.069 1.771.2 3.325.584 5.02.712.147.011.188.143.226.258.035.106.099.3-.431.243-1.678-.178-3.213-.581-4.936-.85z"/>
    <path fill="#000" fill-rule="nonzero" d="M139.455 161.476c-.989-.102-2.115-.323-3.307-.518a.185.185 0 01-.157-.217c.015-.089.093-.176.217-.156 1.185.193 2.304.413 3.286.515.404.041.776-.015 1.099-.142a.182.182 0 01.244.107c.033.085.01.198-.107.244a2.681 2.681 0 01-1.275.167zM126.652 158.099a.388.388 0 01-.153.034c-.04-.002-.09-.017-.108-.065-.018-.046.004-.091.029-.121a.337.337 0 01.12-.086c.885-.39 1.238-.516 2.471-.75a.188.188 0 01.216.111c.032.081-.009.175-.121.196-1.204.228-1.631.353-2.454.681zM126.849 159.223a.535.535 0 01-.163.039c-.036 0-.095-.009-.114-.064-.017-.05.016-.095.04-.121a.48.48 0 01.131-.086c.877-.403 1.104-.474 2.388-.816.108-.029.189.035.207.118.018.081-.025.174-.133.2a15.448 15.448 0 00-2.356.73zM139.452 163.364c-1.449-.15-3.16-.589-4.977-.842-2.3-.321-4.3-.201-6.694.884a.615.615 0 01-.253.068c-.044.001-.112-.01-.126-.077-.011-.051.024-.099.056-.131a.666.666 0 01.157-.104c2.228-1.139 4.56-1.344 6.9-1.025 1.813.247 3.541.627 4.987.776.814.084 1.742-.053 2.048-.543.08-.128.311.149.235.269-.462.74-1.543.807-2.333.725zM127.334 161.342c-.338.156-.512-.061-.131-.255.269-.138 1.495-.74 2.339-.906.023-.005.057.014.066.036l.085.224c.013.037-.006.071-.044.078-.795.14-2.032.693-2.315.823zM127.14 160.238c-.338.156-.512-.061-.131-.255.27-.139 1.436-.646 2.28-.812a.057.057 0 01.068.05l.031.225c.005.029-.019.06-.047.065-.794.14-1.918.595-2.201.727zM136.074 156.459c-.607-.052-1.585-.106-1.585-.106l-.354-4.831 1.174.14.244 3.769c.119.007.646.042.998.08a27.66 27.66 0 011.044.145l.074.97c0 .001-.985-.114-1.595-.167zM147.324 158.087c-.627-.059-1.627-.199-1.627-.199l-.046-2.318-.045-2.327 2.671.277.008.458.009.465-1.494-.155.019.956 1.57.162.017.864-1.57-.163.018.983c.119.019.656.101 1.017.133.406.037 1.071.051 1.071.051l.018.923c.001 0-1.011-.05-1.636-.11zM157.979 153.815l-1.464.202.021 3.707-1.178.163-.022-3.708-1.464.202-.006-.918 4.107-.567zM164.863 156.525c-.759.013-1.16-.118-1.505-.302l.323-.861c.43.144.681.204 1.184.195.554-.01.825-.184.829-.435.003-.168-.125-.286-.344-.392-.219-.107-.511-.195-.803-.309-.585-.228-1.187-.564-1.173-1.365.016-.909 1.032-1.373 1.847-1.388.733-.013 1.144.137 1.5.287l-.297.835c-.361-.098-.723-.182-1.186-.174-.458.008-.725.245-.728.415-.002.132.126.237.352.343.222.105.519.202.816.326.595.249 1.206.611 1.193 1.363-.019 1.036-1.058 1.446-2.008 1.462zM142.179 154.567l.402.069c.599.102.645-.104.641-.356-.006-.304-.147-.49-.752-.594l-.307-.053.016.934zm.043 1.852l.56.096c.652.111.811-.01.804-.337-.007-.375-.233-.6-.937-.719l-.447-.077.02 1.037zm.687 1.037l-1.874-.32-.085-4.602 1.686.287c1.157.197 1.782.705 1.798 1.578a.816.816 0 01-.426.739c.326.182.788.597.8 1.207.015.856-.504 1.348-1.899 1.111zM139.792 156.911l-1.183-.181-.265-4.658 1.183.181z"/>
    <path fill="#000" fill-rule="nonzero" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M150.515 160.248c-3.879 0-6.414-.593-9.88-1.22-8.068-1.46-9.822-.35-9.822 1.046 0 .448.414.58.713.446.153-.069.279-.326.279-.326-.284-.003-.47-.116-.411-.468.172-1.019 2.268-1.483 9.241-.348 3.476.565 6.004 1.045 9.883 1.045l-.003-.175h0zm.006 0c3.879 0 6.415-.593 9.88-1.22 8.068-1.46 9.822-.35 9.822 1.046 0 .448-.414.58-.713.446-.153-.069-.279-.326-.279-.326.284-.003.47-.116.41-.468-.172-1.019-2.267-1.483-9.241-.348-3.476.565-6.004 1.045-9.883 1.045l.004-.175h0z"/>
    <path fill="#000" fill-rule="nonzero" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".375" d="M128.891 156.296c-1.278.205-2.451.642-3.649 1.112-3.266 1.282-7.012 1.086-9.914-.978.203 1.467.846 2.744 1.786 3.864.23.273.935.97.935.97.117.122.096.2-.07.205-2.223.095-4.242.066-6.428-.342 5.161 5.675 10.345 7.088 17.411 3.654 3.713-1.803 6.806.233 10.515.233 3.934 0 3.563-2.575 2.697-5.484l.472.051c.633 1.787 1.627 6.196-3.169 5.84-3.697-.274-6.656-2.158-10.381-.381-5.735 2.736-9.88 2.697-14.87-1.006-1.018-.755-2.657-2.112-3.357-3.146-.17-.251.001-.402.257-.323 1.894.588 4.763.589 6.223.551-1.182-1.189-2.512-3.396-2.454-4.979.008-.205.105-.244.279-.155 2.703 1.401 3.718 1.964 6.87 1.964 1.856 0 5.649-2.097 6.778-1.88l.069.23h0z"/>
    <path fill="#000" fill-rule="nonzero" d="M173.498 162.367c.338.157.512-.061.131-.255-.309-.158-2.004-.959-2.908-1.019a.23.23 0 00-.241.156c-.029.098.025.199.159.209.812.053 2.534.759 2.859.909zM161.598 162.327c1.876-.195 4.365-.835 6.751-.94.123-.005.201-.088.202-.182.001-.098-.08-.177-.205-.171-2.394.106-4.913.677-6.796.872-.695.073-1.455-.065-1.82-.392-.109-.099-.391.272-.287.365.484.433 1.403.526 2.155.448zM166.785 163.445c1.974-.307 4.408.026 6.293.965a.62.62 0 00.213.061c.046.004.115.002.144-.051a.097.097 0 00.003-.083.19.19 0 00-.044-.063.73.73 0 00-.179-.123c-2.778-1.346-4.899-1.257-6.551-1.069-1.771.2-3.325.584-5.02.712-.148.011-.188.143-.227.258-.035.106-.099.3.431.243 1.679-.178 3.214-.581 4.937-.85z"/>
    <path fill="#000" fill-rule="nonzero" d="M161.582 161.476c.989-.102 2.115-.323 3.307-.518a.185.185 0 00.157-.217c-.015-.089-.093-.176-.217-.156-1.185.193-2.304.413-3.286.515a2.344 2.344 0 01-1.099-.142.182.182 0 00-.244.107.182.182 0 00.107.244c.379.151.812.215 1.275.167zM174.385 158.099a.388.388 0 00.153.034c.04-.002.09-.017.108-.065.018-.046-.004-.091-.029-.121a.347.347 0 00-.12-.086c-.885-.39-1.238-.516-2.471-.75a.188.188 0 00-.216.111c-.032.081.008.175.12.196 1.206.228 1.632.353 2.455.681zM174.188 159.223a.535.535 0 00.163.039c.037 0 .095-.009.114-.064.017-.05-.016-.095-.04-.121a.48.48 0 00-.131-.086c-.877-.403-1.104-.474-2.388-.816-.109-.029-.189.035-.207.118-.018.081.025.174.133.2 1.001.239 1.489.395 2.356.73zM161.585 163.364c1.449-.15 3.16-.589 4.977-.842 2.299-.321 4.3-.201 6.694.884.131.06.202.066.253.068.044.001.112-.01.126-.077.011-.051-.024-.099-.056-.131a.654.654 0 00-.158-.104c-2.228-1.139-4.56-1.344-6.899-1.025-1.814.247-3.541.627-4.987.776-.813.084-1.742-.053-2.048-.543-.08-.128-.311.149-.235.269.462.74 1.543.807 2.333.725zM173.703 161.342c.338.156.512-.061.131-.255-.27-.138-1.496-.74-2.339-.906-.023-.005-.057.014-.066.036l-.085.224c-.014.037.005.071.044.078.794.14 2.032.693 2.315.823zM173.897 160.238c.338.156.512-.061.131-.255-.27-.139-1.436-.646-2.279-.812a.058.058 0 00-.069.05l-.031.225a.061.061 0 00.047.065c.794.14 1.918.595 2.201.727z"/>
    <path fill="#000" fill-rule="nonzero" d="M172.146 156.296c1.278.205 2.451.642 3.649 1.112 3.266 1.282 7.012 1.086 9.914-.978-.202 1.467-.846 2.744-1.787 3.864-.229.273-.933.97-.933.97-.118.122-.097.2.07.205 2.222.095 4.242.066 6.428-.342-5.162 5.675-10.345 7.088-17.412 3.654-3.712-1.803-6.805.233-10.514.233-3.934 0-3.563-2.575-2.697-5.484l-.471.051c-.634 1.787-1.628 6.196 3.168 5.84 3.697-.274 6.656-2.158 10.381-.381 5.735 2.736 9.88 2.697 14.87-1.006 1.018-.755 2.657-2.112 3.357-3.146.171-.251-.002-.402-.256-.323-1.894.588-4.763.589-6.223.551 1.181-1.189 2.512-3.396 2.454-4.979-.008-.205-.105-.244-.279-.155-2.703 1.401-3.718 1.964-6.87 1.964-1.857 0-5.649-2.097-6.778-1.88l-.071.23z"/>
    <path fill="#000" fill-rule="nonzero" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".35" d="M172.146 156.296c1.278.205 2.451.642 3.649 1.112 3.266 1.282 7.012 1.086 9.914-.978-.202 1.467-.846 2.744-1.787 3.864-.229.273-.933.97-.933.97-.118.122-.097.2.07.205 2.222.095 4.242.066 6.428-.342-5.162 5.675-10.345 7.088-17.412 3.654-3.712-1.803-6.805.233-10.514.233-3.934 0-3.563-2.575-2.697-5.484l-.471.051c-.634 1.787-1.628 6.196 3.168 5.84 3.697-.274 6.656-2.158 10.381-.381 5.735 2.736 9.88 2.697 14.87-1.006 1.018-.755 2.657-2.112 3.357-3.146.171-.251-.002-.402-.256-.323-1.894.588-4.763.589-6.223.551 1.181-1.189 2.512-3.396 2.454-4.979-.008-.205-.105-.244-.279-.155-2.703 1.401-3.718 1.964-6.87 1.964-1.857 0-5.649-2.097-6.778-1.88l-.071.23h0z"/>
    <path fill="#000" fill-rule="nonzero" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".375" d="M150.303 151.931c-4.435.032-8.175-.776-11.297-1.479-2.587-.584-5.601-1.251-8.253-.898-1.932.259-2.721 1.112-2.391 3.06.411 2.421.772 4.863 1.212 7.277.254 1.393.996 1.58 2.305 1.349 1.885-.333 7.93-1.685 9.288.032 1.303 1.647.033 2.834-1.781 2.865-.254.004-.172-.173-.086-.173 1.736 0 2.957-1.004 1.836-2.544-1.181-1.623-6.837-.433-9.14.105-1.465.342-2.439.154-2.708-1.645-.371-2.478-.599-4.181-1.385-7.386-1.328-5.417 8.339-2.908 11.124-2.26 3.797.881 6.966 1.448 11.469 1.583 4.512-.135 7.681-.701 11.478-1.583 2.784-.647 12.453-3.156 11.125 2.26-.786 3.205-1.015 4.907-1.386 7.386-.269 1.798-1.243 1.987-2.708 1.645-2.303-.538-7.959-1.728-9.14-.105-1.121 1.54.1 2.544 1.836 2.544.086 0 .167.177-.087.173-1.813-.03-3.084-1.218-1.781-2.865 1.358-1.717 7.403-.365 9.288-.032 1.309.231 2.051.044 2.305-1.349.439-2.413.801-4.855 1.213-7.277.33-1.948-.459-2.802-2.392-3.06-2.653-.353-5.665.315-8.252.898-3.121.704-6.862 1.511-11.297 1.479h-.395 0z"/>
    <path fill="#000" fill-rule="nonzero" d="M151.219 155.71c.623-.011.897-.276.889-.701-.008-.446-.201-.633-.766-.623l-.523.009.023 1.321.377-.006zm2.611 2.407c-.921-.03-1.417-.492-1.903-1.037l-.514-.576a3.406 3.406 0 01-.231.009l-.305.006.027 1.623-1.155.019-.08-4.645 1.793-.031c1.029-.018 1.788.443 1.807 1.477.009.504-.222.951-.694 1.241l.446.466c.251.26.531.433 1.022.551l-.213.897zM160.484 153.448c-.141.505-.29 1.007-.442 1.509.322-.033.66-.09.97-.183a55.215 55.215 0 01-.404-1.349l-.124.023zm1.257 3.374l-.456-1.247a3.95 3.95 0 01-.765.181c-.265.04-.531.067-.784.106l-.498 1.43-1.194.223 1.444-3.984-.506.095-.009-.918a67.25 67.25 0 003.137-.589l.042.912-.51.096 1.331 3.464-1.232.231z"/>
  </g>
</svg>
