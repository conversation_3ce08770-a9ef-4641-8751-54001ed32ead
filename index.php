<?php
ob_start();
?>
<style>
    div.dt-top-container {
        display: grid;
        grid-template-columns: auto auto auto;
    }

    div.dt-center-in-div {
        margin: 0 auto;
    }

    div.dt-filter-spacer {
        margin: 10px 0;
    }
</style>
<?php
require_once 'inc/cfg_head.php';
require_once 'inc/cfg_menu.php';
require_once 'inc/cfg_functions.php';
global $accesMJApps;
if ($accesMJApps == 'OutsideMJ') {
    header('Location: sp_it_just.php');
    ob_end_clean();
    exit;
}
?>
<div class="wrapper d-flex flex-column min-vh-100 bg-light">
    <?php include 'inc/cfg_topnav.php'; ?>
    <div class="body flex-grow-1 px-3">

        <div class="row">
            <div class="col-md-12">
                <div class="card mb-4">
                    <div class="card-header">Carte telefon</div>
                    <div class="card-body">

                        <div class="row mb-2">
                            <div class="col">
                                <button class="btn btn-light resetDatatable">
                                    Resetare sortare tabel
                                </button>
                            </div>
                            <div class="col printZone"
                                 style="display: flex; align-items: center; justify-content: flex-end;">
                                <button class="form-control"
                                        onclick="javascript:printContent('printTableCartetelefon');" href='#'
                                        style='text-decoration: none; color: #333; width: 5em; margin-right: 0.5em;'>
                                    <svg style='height: 1em; width: 1em;'>
                                        <use xlink:href='vendors/@coreui/icons/svg/free.svg#cil-print'></use>
                                    </svg>
                                    Print
                                </button>
                            </div>
                        </div>

                        <div class="row">
                            <div class="table-responsive" id="printTableCartetelefon">
                                <table class='table table-bordered table-hover table-striped table-sm border mb-0'
                                       id="tabelCarteTelefoane"
                                       style="text-align: center; width: 100%;">
                                    <thead>
                                    <tr>
                                        <th>Et/Cam</th>
                                        <th class="text-wrap">Nume Prenume</th>
                                        <th>Functie</th>
                                        <th colspan="3">Telefoane</th>
                                        <th rowspan="2">E-mail</th>
                                        <th rowspan="2">Observatii</th>
                                    </tr>
                                    <tr>
                                        <th></th>
                                        <th></th>
                                        <th></th>
                                        <th>037 204 xxxx</th>
                                        <th>mobil de serviciu</th>
                                        <th>fax</th>
                                    </tr>
                                    </thead>
                                    <tfoot style="display: table-row-group;">
                                    <th></th>
                                    <th></th>
                                    <th></th>
                                    <th></th>
                                    <th></th>
                                    <th></th>
                                    <th></th>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <?php
        require_once 'inc/cfg_footer.php';
        global $session_uid, $session_id_structura;
        if ($session_uid && ($session_id_structura == 287 || $session_id_structura > 1000000)) {//287 Ministerul Justiei
            ?>
            <script src="view/carte-telefon.js"></script>
            <?php
        }
        ?>
        <link href="assets/js/datatable-jquery.dataTables.css" rel="stylesheet" type="text/css"/>
        <link href="assets/js/datatable-buttons.dataTables.css" rel="stylesheet" type="text/css"/>
        <script src="assets/js/datatable-jquery.dataTables.js"></script>
        <script src="assets/js/datatable-buttons.dataTables.js"></script>
        <script src="assets/js/jquery.dataTables.min.js"></script>
        <script src="assets/js/dataTables.buttons.min.js"></script>
        <script src="assets/js/buttons.html5.min.js"></script>
        <script src="assets/js/jszip.min.js"></script>
        <script src="assets/js/pdfmake.min.js"></script>
        <script src="assets/js/vfs_fonts.js"></script>

        <script>
            function stopPropagation(evt) {
                if (evt.stopPropagation !== undefined) {
                    evt.preventDefault();
                    evt.stopPropagation();
                } else {
                    evt.cancelBubble = true;
                }
            }

            function resetSort() {
                $('#tabelCarteTelefoane').dataTable({"bRetrieve": true}).fnSort([]);
                $('#tabelCarteTelefoane').DataTable().state.clear().destroy();
                window.location.reload();
            }

            function datatableRows() {
                var select = '<label>Afișează <select name="tabelCarteTelefoane_length" aria-controls="tabelCarteTelefoane" class="form-control-color" style="width: fit-content;">' +
                    '<option value="30"';
                if ($.cookie('datatableRows') == 30) {
                    select += 'selected="selected"';
                }
                select += '>30</option>' +
                    '<option value="50"';
                if ($.cookie('datatableRows') == 50) {
                    select += 'selected="selected"';
                }
                select += '>50</option>' +
                    '<option value="100"';
                if ($.cookie('datatableRows') == 100) {
                    select += 'selected="selected"';
                }
                select += '>100</option>' +
                    '<option value="-1"';
                if ($.cookie('datatableRows') == -1) {
                    select += 'selected="selected"';
                }
                select += '>Toate</option>' +
                    '</select> înregistrări pe pagină</label>';
                $(document).find('#tabelCarteTelefoane_length').html(select);

                $(document).find('select[name="tabelCarteTelefoane_length"').on('change', function () {
                    var dataExpirareCookie = new Date();
                    dataExpirareCookie.setTime(dataExpirareCookie.getTime() + 24 * 60 * 60 * 1000);
                    $.cookie('datatableRows', $(this).find('option:selected').val(), {expires: dataExpirareCookie});
                    resetSort();
                });
            }


            function printContent(id) {
                var data = document.getElementById(id);
                console.log(data);
                // var data = $("#" + id).find("*").html();
                var data = $("#" + id).html();

                var popupWindow = window.open('', 'printwin', 'left=100,top=100,width=800,height=600');
                popupWindow.document.write('<HTML>\n<HEAD>\n');
                popupWindow.document.write('<TITLE>Imprimare</TITLE>\n');
                popupWindow.document.write('<URL></URL>\n');
                popupWindow.document.write("<link href='css/style.css' media='all' rel='stylesheet' type='text/css' />\n");
                popupWindow.document.write('<script>\n');
                popupWindow.document.write('function print_win(){\n');
                popupWindow.document.write('\nwindow.print();\n');
                popupWindow.document.write('\nwindow.close();\n');
                popupWindow.document.write('}\n');
                popupWindow.document.write('<\/script>\n');
                popupWindow.document.write('</HEAD>\n');
                popupWindow.document.write('<BODY onload="print_win()">\n');
                popupWindow.document.write('<BODY>\n');
                popupWindow.document.write(data);
                popupWindow.document.write('</BODY>\n');
                popupWindow.document.write('</HTML>\n');
                popupWindow.document.close();
            }


            $(document).ready(function () {

                $(document).off('click', '.resetDatatable').on('click', '.resetDatatable', function () {
                    resetSort();
                });


                var randuri = $('div#tabelCarteTelefoane_length.datatables_length option[value="' + $.cookie('datatableRows') + '"]').prop('selected', true);
                var randuriText = parseInt(randuri);
                if (randuri == -1) {
                    randuriText = 'Toate';
                }


                let exportFormatter = {
                    format: {
                        body: function (data, row, column, node) {
                            var tempElement = document.createElement('div');
                            tempElement.innerHTML = data;
                            var divToRemove = tempElement.querySelector('.removeElement');
                            if (divToRemove) {
                                divToRemove.parentNode.removeChild(divToRemove);
                            }
                            var divToRemove = tempElement.querySelector('select');
                            if (divToRemove) {
                                divToRemove.parentNode.removeChild(divToRemove);
                            }
                            var divToRemove = tempElement.querySelector('input');
                            if (divToRemove) {
                                divToRemove.parentNode.removeChild(divToRemove);
                            }
                            data = tempElement.innerHTML.replace(/<[^>]+>/g, '');
                            return data;
                        }
                    }
                };


                var tabelDatatable = $(document).find('#tabelCarteTelefoane').DataTable({
                    order: [[1, 'desc'], [0, 'asc']],
                    // lengthMenu: [[randuri], ['Toate']],
                    pageLength: $.cookie('datatableRows'),
                    // stateSave: true,
                    processing: true,
                    serverSide: true,
                    bInfo: false,
                    serverMethod: 'post',
                    searching: true,
                    columns: [
                        {
                            data: "etCam",
                            width: "3%"
                        },
                        {
                            data: 'numePrenume',
                            width: "30%"
                        },
                        {
                            data: 'functie',
                            width: "30%"
                        },
                        {
                            data: 'telFix',
                            width: "10%"
                        },
                        {
                            data: 'telMobil',
                            width: "10%"
                        },
                        {
                            data: 'telFax',
                            width: "10%"
                        },
                        {
                            data: 'email',
                            width: "5%"
                        },
                        {
                            data: 'observatii',
                            width: "10%"
                        }
                    ],
                    language: {url: 'assets/js/datatable-ro.json',},
                    ajax: {"url": 'controller/carte-telefon-datatable.php'},
                    initComplete: function (settings) {
                        sessionStorage.setItem('initialSort', JSON.stringify(settings.aaSorting));

                        //2 - select filter cu consilieri
                        this.api().column(2).every(function () {
                            var column = this;
                            var selectOption = '<option value="">Toate functiile</option>';
                            var select = $('<select class="form-control removeElement" onclick="stopPropagation(event);">' + selectOption + '</select>')
                                .appendTo($(column.header()).empty())
                                .on('change', function () {
                                    var val = $.fn.dataTable.util.escapeRegex(
                                        $(this).val()
                                    );
                                    column
                                        .search(val ? '' + val + '' : '', true, false)
                                        .draw();
                                });

                            var postForm = new FormData();
                            postForm.append('selectFiltruFunctie', 1);
                            $.ajax({
                                url: 'controller/carte-telefon.php',
                                cache: false,
                                contentType: false,
                                processData: false,
                                data: postForm,
                                type: 'post',
                                dataType: 'JSON',
                                success: function (r) {
                                    select.append(r.message);
                                }
                            });
                        });

                        //1-3-4-5 - input filter
                        this.api().columns([1, 3, 4, 5]).every(function () {
                            var column = this,
                                val_coloana = '';

                            switch (column.index()) {
                                case 3:
                                    placeholder = 'Interior';
                                    break;
                                case 4:
                                    placeholder = 'Mobil';
                                    break;
                                case 5:
                                    placeholder = 'Fax';
                                    break;
                                default:
                                    placeholder = 'Nume Prenume';
                            }

                            var input = $('<input type="text" class="form-control" placeholder="' + placeholder + '" value="' + val_coloana + '" onclick="stopPropagation(event);">')
                                .appendTo($(column.header()).empty())
                                .on('keyup change clear', function () {
                                    var val = $.fn.dataTable.util.escapeRegex($(this).val());
                                    column
                                        .search(val ? '' + val + '' : '', true, false)
                                        .draw();
                                });
                        });

                        apelFunctiiComplete();
                        $('.printZone').append($('.dt-buttons'));
                        $('.dt-buttons').find('button').addClass('form-control').removeClass('dt-button');
                    },
                    createdRow: function (row, data, dataIndex) {
                        var classTabel = $(row).find('td:first-child > input[type="hidden"][tabel]').attr('tabel');
                        $(row).addClass(classTabel);
                        $(row).find('td').each(function () {
                            $(this).css('vertical-align', 'middle');
                        });
                        $(row).find('td').eq(1).each(function () {
                            $(this).css('text-align', 'left');
                        });
                        //interior
                        $(row).find('td').eq(3).each(function () {
                            $(this).css('color', 'red');
                        });
                        //mobil
                        $(row).find('td').eq(4).each(function () {
                            $(this).css('color', 'blue');
                        });
                        //fax
                        $(row).find('td').eq(5).each(function () {
                            $(this).css('color', 'red');
                        });
                        // email
                        $(row).find('td').eq(6).each(function () {
                            $(this).css('color', 'blue');
                        });
                        //observatii
                        $(row).find('td').eq(7).each(function () {
                            $(this).css('color', 'blue');
                        });
                    },
                    fnDrawCallback: function (oSettings) {
                        apelFunctiiDraw();
                        $('select[name="tabelCarteTelefoane_length"]').removeClass('form-select');
                        $('input[type="search"]').removeClass('form-control');
                    }
                    , dom: '<"dt-top-container"<l><"dt-center-in-div"B><f>r>t<"dt-filter-spacer"f><ip>'
                    , buttons: [
                        {
                            extend: 'excel'
                            , orientation: 'landscape'
                            , exportOptions: $.extend({}, exportFormatter, {
                                stripHtml: true
                            })
                            , text: 'Exportă Excel'
                        }
                    ]
                });


                function apelFunctiiComplete() {
                    console.log('-- apelFunctiiComplete()');
                    $('table thead th').css({verticalAlign: 'middle', textAlign: 'center'});
                    $(document).find('#tabelCarteTelefoane_filter').append('<p style="text-align: center; font-style: italic; color: #8a93a2; font-weight: 600; font-size: small;">(de ex., căutare direcție)</p>')
                    $(document).find('#tabelCarteTelefoane_filter > label > input[type="search"]').focus();
                    datatableRows();
                }

                function apelFunctiiDraw() {
                    console.log('-- apelFunctiiDraw()');
                }


            });
        </script>
