<link href="css/bootstrap-datepicker.css" rel="stylesheet">
<?php
require_once 'inc/cfg_head.php';
require_once 'inc/cfg_menu.php';
require_once 'inc/cfg_functions.php';
require_once 'mentenanta.php';
global $date;
?>
<div class="wrapper d-flex flex-column min-vh-100 bg-light">
    <?php include 'inc/cfg_topnav.php'; ?>
    <div class="body flex-grow-1 px-3">

        <div class="row">
            <div class="col-md-12">
                <div class="card mb-4">
                    <div class="card-header"
                         style="display: flex; justify-content: space-between; align-items: center;">
                        <div style="display: flex; align-items: center;">
                            Trimitere SMS cu parola pentru conectarea la VPN
                        </div>
                        <div style="margin-left: auto;">
                            <a href="#" class="exportStatistica" style="color: #4f5d73; text-decoration: none;">
                                <svg class="nav-icon" style="width: 20px; height: 20px;">
                                    <use xlink:href="vendors/@coreui/icons/svg/free.svg#cil-data-transfer-down"></use>
                                </svg>
                                Export
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row mb-6">
                            <div class="col-md-4 formular" style="text-align: -webkit-center;">
                                <p>Parolă transmisă:</p>
                                <p>
                                    <input type='text' class='form-select-sm form-control-lg border w-75'
                                           id='parolaInput' placeholder='Scrie parola aici' value="">
                                </p>
                                <p>Număr telefon destinatar:</p>
                                <p><input type='text' class='form-select-sm form-control-lg border w-75'
                                          id='sms_dest' placeholder='ex: 0749607867'></p>
                                <p>Nume persoană:</p>
                                <p><input type='text' class='form-select-sm form-control-lg border w-75'
                                          id='nume_persoana' placeholder='ex: Hazsda Radu'></p>
                                <p>
                                    <button class="btn btn-outline-dark w-75" id="sms_send">
                                        Send SMS
                                        <svg class="nav-icon" style="width: 7%; height: 7%;">
                                            <use xlink:href="vendors/@coreui/icons/svg/free.svg#cil-send"></use>
                                        </svg>
                                    </button>
                                </p>
                                <p>Textul trimis va fi:</p>
                                <p style="font-size: small; color: gray;">"Parola pentru conectarea clientului
                                    FortiClient la serverul
                                    FortiEMS este următoarea: <em>parola-introdusa-in-input</em> Având în vedere
                                    caracterul sensibil al acestei parole,
                                    vă rugăm să rețineți că aceasta are un caracter confidențial și responsabilitatea
                                    păstrării acestuia cade în sarcina persoanei căreia i-a fost încredințată"</p>
                            </div>
                            <div class="col-md-8">
                                <table class="table table-hover table-dark" id="table-sms">
                                    <thead>
                                    <tr>
                                        <td>Personal DTI</td>
                                        <td>Comunicat către</td>
                                        <td>Persoană</td>
                                        <td>Moment</td>
                                    </tr>
									<tr><td style='text-align:center;' colspan=4>Cele mai recente 15 inregistrari</td></tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                            </div>
                        </div>
                        <div class="card-footer"><small>După trimitere formularul se va reseta.</small></div>

                    </div>
                </div>

            </div>

        </div>


    </div>
</div>


<?php
require_once 'inc/cfg_footer.php';
?>
<script src="assets/js/swal.js"></script>
<script src="assets/js/chartExportXLSX.js"></script>
<script src="view/dtiSendSMS.js"></script>
